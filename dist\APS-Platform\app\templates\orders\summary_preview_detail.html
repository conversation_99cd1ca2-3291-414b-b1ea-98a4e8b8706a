<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .preview-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table th {
            background-color: #495057;
            color: white;
            border: none;
            font-weight: 500;
            font-size: 0.9rem;
        }
        .table td {
            font-size: 0.85rem;
            vertical-align: middle;
        }
        .lot-type-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
            border-radius: 12px;
        }
        .classification-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
            border-radius: 12px;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        .print-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        @media print {
            .print-button, .no-print { display: none !important; }
            .table-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="preview-header no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-1">{{ title }}</h2>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-calendar me-2"></i>生成时间：{{ moment().format('YYYY年MM月DD日 HH:mm:ss') }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light me-2" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>打印预览
                    </button>
                    <button class="btn btn-outline-light" onclick="window.close()">
                        <i class="fas fa-times me-1"></i>关闭窗口
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 统计信息 -->
        <div class="row no-print">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="h4 text-primary mb-1">{{ orders|length }}</div>
                    <div class="text-muted small">总订单数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="h4 text-info mb-1">{{ orders|selectattr('classification', 'equalto', 'engineering')|list|length }}</div>
                    <div class="text-muted small">工程订单</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="h4 text-success mb-1">{{ orders|selectattr('classification', 'equalto', 'production')|list|length }}</div>
                    <div class="text-muted small">量产订单</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="h4 text-warning mb-1">{{ orders|rejectattr('classification')|list|length }}</div>
                    <div class="text-muted small">未分类订单</div>
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="table-container">
            {% if orders %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="8%">序号</th>
                            <th width="12%">订单号</th>
                            <th width="15%">产品名称</th>
                            <th width="12%">电路名称</th>
                            <th width="8%">Lot Type</th>
                            <th width="8%">分类</th>
                            <th width="8%">送包只数</th>
                            <th width="8%">送包片数</th>
                            <th width="10%">交期</th>
                            <th width="11%">承揽商</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td class="fw-bold">{{ order.order_number or '-' }}</td>
                            <td>{{ order.product_name or '-' }}</td>
                            <td>{{ order.circuit_name or '-' }}</td>
                            <td>
                                {% if order.lot_type %}
                                <span class="badge lot-type-badge bg-light text-dark">{{ order.lot_type }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.classification == 'engineering' %}
                                <span class="badge classification-badge bg-info">工程</span>
                                {% elif order.classification == 'production' %}
                                <span class="badge classification-badge bg-success">量产</span>
                                {% else %}
                                <span class="badge classification-badge bg-warning">未分类</span>
                                {% endif %}
                            </td>
                            <td class="text-end">{{ order.package_qty or '-' }}</td>
                            <td class="text-end">{{ order.package_pieces or '-' }}</td>
                            <td>{{ order.delivery_date or '-' }}</td>
                            <td class="small">{{ order.contractor_name or '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无{{ summary_type }}订单数据</h5>
                <p class="text-muted">请先导入订单数据或调整筛选条件</p>
            </div>
            {% endif %}
        </div>

        {% if orders %}
        <!-- 页脚统计 -->
        <div class="mt-4 mb-5">
            <div class="row">
                <div class="col-md-6">
                    <div class="stats-card">
                        <h6 class="mb-3">Lot Type分布</h6>
                        {% set lot_type_counts = {} %}
                        {% for order in orders %}
                            {% set lot_type = order.lot_type or '未知' %}
                            {% if lot_type_counts.update({lot_type: lot_type_counts.get(lot_type, 0) + 1}) %}{% endif %}
                        {% endfor %}
                        
                        {% for lot_type, count in lot_type_counts.items() %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small">{{ lot_type }}</span>
                            <span class="badge bg-secondary">{{ count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stats-card">
                        <h6 class="mb-3">承揽商统计</h6>
                        {% set contractor_counts = {} %}
                        {% for order in orders %}
                            {% set contractor = order.contractor_name or '未知' %}
                            {% if contractor_counts.update({contractor: contractor_counts.get(contractor, 0) + 1}) %}{% endif %}
                        {% endfor %}
                        
                        {% for contractor, count in contractor_counts.items() %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small">{{ contractor[:20] }}{% if contractor|length > 20 %}...{% endif %}</span>
                            <span class="badge bg-secondary">{{ count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 浮动打印按钮 -->
    <button class="btn btn-primary print-button" onclick="window.print()">
        <i class="fas fa-print"></i>
    </button>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 如果是打印模式，自动打印
            if (window.location.search.includes('print=true')) {
                setTimeout(() => {
                    window.print();
                }, 1000);
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+P 打印
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // ESC 关闭窗口
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html> 
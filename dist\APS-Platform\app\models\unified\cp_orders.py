"""
CP订单汇总模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Date
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class CPOrderSummary(Base):
    """CP订单汇总表模型"""
    __tablename__ = 'cp_summary'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    order_number = Column(String(100), comment='订单号')
    product_name = Column(String(200), comment='产品名称')
    chip_name = Column(String(200), comment='芯片名称')
    processing_type = Column(String(100), comment='加工类型')
    contractor_name = Column(String(200), comment='承包商名称')
    client_name = Column(String(200), comment='客户名称')
    processing_pieces = Column(Integer, comment='加工片数')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f'<CPOrderSummary {self.order_number}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'product_name': self.product_name,
            'chip_name': self.chip_name,
            'processing_type': self.processing_type,
            'contractor_name': self.contractor_name,
            'client_name': self.client_name,
            'processing_pieces': self.processing_pieces,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else '',
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else ''
        } 
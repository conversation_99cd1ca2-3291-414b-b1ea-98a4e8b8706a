# 空文件，使 config 目录成为 Python 包 

import os
from datetime import timedelta

class Config:
    # Security
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    
    # MySQL configuration - 100% MySQL模式
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or '127.0.0.1'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'WWWwww123!'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'aps'
    MYSQL_SYSTEM_DATABASE = os.environ.get('MYSQL_SYSTEM_DATABASE') or 'aps_system'
    MYSQL_CHARSET = os.environ.get('MYSQL_CHARSET') or 'utf8mb4'
    
    # Database URI配置 - 直接构建MySQL URI
    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{os.environ.get('MYSQL_USER') or 'root'}:{os.environ.get('MYSQL_PASSWORD') or 'WWWwww123!'}@{os.environ.get('MYSQL_HOST') or '127.0.0.1'}:{int(os.environ.get('MYSQL_PORT') or 3306)}/{os.environ.get('MYSQL_DATABASE') or 'aps'}?charset={os.environ.get('MYSQL_CHARSET') or 'utf8mb4'}"
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 数据库绑定配置 - 系统数据库绑定到aps_system
    SQLALCHEMY_BINDS = {
        'system': f"mysql+pymysql://{os.environ.get('MYSQL_USER') or 'root'}:{os.environ.get('MYSQL_PASSWORD') or 'WWWwww123!'}@{os.environ.get('MYSQL_HOST') or '127.0.0.1'}:{int(os.environ.get('MYSQL_PORT') or 3306)}/{os.environ.get('MYSQL_SYSTEM_DATABASE') or 'aps_system'}?charset={os.environ.get('MYSQL_CHARSET') or 'utf8mb4'}"
    }
    
    # Session security
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # 会话最长持续8小时
    SESSION_COOKIE_SECURE = False  # 在生产环境中应设置为True，要求HTTPS
    SESSION_COOKIE_HTTPONLY = True  # 防止JavaScript访问cookie
    SESSION_COOKIE_SAMESITE = 'Lax'  # 防止CSRF攻击
    
    # 请求处理
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 限制上传文件大小为16MB
    
    # 并发控制
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,  # 数据库连接池大小
        'max_overflow': 20,  # 最大额外连接数
        'pool_timeout': 30,  # 连接池超时时间
        'pool_recycle': 3600,  # 自动回收连接的时间
    }
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'app.log'
    
    # Application specific
    THEME_COLOR = '#b81717'     # theme color
    BACKGROUND_COLOR = '#FFFFFF'  # White
    SECONDARY_BACKGROUND = '#F5F5F5'  # Light Gray
    TEXT_COLOR = '#333333'  # Dark Gray
    
    # 应用版本号
    APP_VERSION = '2.1'  # 当前应用版本 
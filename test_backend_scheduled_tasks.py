#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端定时任务系统测试脚本
验证替代前端定时任务的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.services.background_scheduler_service import BackgroundSchedulerService
import json
from datetime import datetime

def test_backend_scheduled_tasks():
    """测试后端定时任务系统"""
    print("🧪 开始测试后端定时任务系统...")
    
    app, socketio = create_app()
    
    with app.app_context():
        try:
            # 1. 测试创建数据库表
            print("\n1️⃣ 测试数据库表创建...")
            
            # 执行SQL创建表
            from sqlalchemy import text
            
            # 创建定时任务配置表
            create_scheduled_tasks_sql = text("""
                CREATE TABLE IF NOT EXISTS `scheduled_tasks` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
                    `task_id` VARCHAR(255) NOT NULL UNIQUE COMMENT '任务唯一标识',
                    `name` VARCHAR(255) NOT NULL COMMENT '任务名称',
                    `type` ENUM('once', 'daily', 'weekly', 'interval') NOT NULL COMMENT '任务类型',
                    `config_data` JSON COMMENT '任务配置数据(JSON格式)',
                    `status` ENUM('active', 'paused', 'completed', 'error') DEFAULT 'active' COMMENT '任务状态',
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    INDEX `idx_task_id` (`task_id`),
                    INDEX `idx_status` (`status`),
                    INDEX `idx_type` (`type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务配置表'
            """)
            
            # 创建任务执行日志表
            create_execution_logs_sql = text("""
                CREATE TABLE IF NOT EXISTS `task_execution_logs` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
                    `execution_id` VARCHAR(255) NOT NULL COMMENT '执行ID',
                    `task_name` VARCHAR(255) NOT NULL COMMENT '任务名称',
                    `status` ENUM('started', 'completed', 'failed', 'error') NOT NULL COMMENT '执行状态',
                    `started_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
                    `finished_at` DATETIME NULL COMMENT '结束时间',
                    `config_data` JSON COMMENT '任务配置数据',
                    `result_data` JSON COMMENT '执行结果数据',
                    INDEX `idx_execution_id` (`execution_id`),
                    INDEX `idx_task_name` (`task_name`),
                    INDEX `idx_status` (`status`),
                    INDEX `idx_started_at` (`started_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行日志表'
            """)
            
            db.session.execute(create_scheduled_tasks_sql)
            db.session.execute(create_execution_logs_sql)
            db.session.commit()
            print("✅ 数据库表创建成功")
            
            # 2. 测试后端定时任务服务
            print("\n2️⃣ 测试后端定时任务服务...")
            
            scheduler_service = BackgroundSchedulerService()
            scheduler_service.init_app(app)
            
            print("✅ 后端定时任务服务初始化成功")
            
            # 3. 测试创建定时任务
            print("\n3️⃣ 测试创建定时任务...")
            
            # 测试每日任务
            daily_task = {
                'name': '每日自动排产测试',
                'type': 'daily',
                'hour': 9,
                'minute': 0,
                'strategy': 'intelligent',
                'target': 'balanced',
                'autoImport': True,
                'emailNotification': False
            }
            
            result = scheduler_service.create_scheduled_task(daily_task)
            if result.get('success'):
                print(f"✅ 每日任务创建成功: {result.get('task_id')}")
            else:
                print(f"❌ 每日任务创建失败: {result.get('message')}")
            
            # 测试间隔任务
            interval_task = {
                'name': '间隔排产测试',
                'type': 'interval',
                'intervalValue': 30,
                'intervalUnit': 'minutes',
                'strategy': 'deadline',
                'target': 'makespan',
                'autoImport': False,
                'emailNotification': True
            }
            
            result = scheduler_service.create_scheduled_task(interval_task)
            if result.get('success'):
                print(f"✅ 间隔任务创建成功: {result.get('task_id')}")
            else:
                print(f"❌ 间隔任务创建失败: {result.get('message')}")
            
            # 4. 测试获取所有任务
            print("\n4️⃣ 测试获取所有任务...")
            
            tasks = scheduler_service.get_all_tasks()
            print(f"✅ 获取到 {len(tasks)} 个任务")
            
            for task in tasks:
                print(f"   - {task.get('name')} ({task.get('type')}) - {task.get('status')}")
            
            # 5. 测试API接口模拟
            print("\n5️⃣ 测试API接口模拟...")
            
            # 模拟前端发送的任务数据
            frontend_task_data = {
                'name': '前端迁移测试任务',
                'type': 'weekly',
                'hour': 10,
                'minute': 30,
                'strategy': 'product',
                'target': 'efficiency',
                'autoImport': True,
                'emailNotification': True,
                'weekdays': ['monday', 'wednesday', 'friday']
            }
            
            result = scheduler_service.create_scheduled_task(frontend_task_data)
            if result.get('success'):
                print(f"✅ 前端迁移任务创建成功: {result.get('task_id')}")
                
                # 测试暂停任务
                task_id = result.get('task_id')
                pause_result = scheduler_service.pause_task(task_id)
                if pause_result.get('success'):
                    print(f"✅ 任务暂停成功")
                else:
                    print(f"❌ 任务暂停失败: {pause_result.get('message')}")
                
                # 测试恢复任务
                resume_result = scheduler_service.resume_task(task_id)
                if resume_result.get('success'):
                    print(f"✅ 任务恢复成功")
                else:
                    print(f"❌ 任务恢复失败: {resume_result.get('message')}")
                
            else:
                print(f"❌ 前端迁移任务创建失败: {result.get('message')}")
            
            # 6. 测试数据库查询
            print("\n6️⃣ 测试数据库查询...")
            
            # 查询定时任务配置
            config_sql = text("SELECT task_id, name, type, status FROM scheduled_tasks")
            config_results = db.session.execute(config_sql).fetchall()
            
            print(f"✅ 数据库中有 {len(config_results)} 个任务配置:")
            for row in config_results:
                print(f"   - {row[1]} ({row[2]}) - {row[3]}")
            
            # 7. 清理测试数据
            print("\n7️⃣ 清理测试数据...")
            
            # 删除测试任务
            tasks = scheduler_service.get_all_tasks()
            for task in tasks:
                if '测试' in task.get('name', ''):
                    delete_result = scheduler_service.delete_task(task.get('id'))
                    if delete_result.get('success'):
                        print(f"✅ 删除测试任务: {task.get('name')}")
                    else:
                        print(f"❌ 删除测试任务失败: {task.get('name')}")
            
            print("\n🎉 后端定时任务系统测试完成!")
            print("\n📋 测试总结:")
            print("   ✅ 数据库表创建正常")
            print("   ✅ 后端定时任务服务正常")
            print("   ✅ 任务创建、暂停、恢复、删除功能正常")
            print("   ✅ 数据库存储和查询正常")
            print("   ✅ 前端API接口兼容性正常")
            print("\n🔄 替换方案:")
            print("   1. 前端localStorage操作 → 后端API调用")
            print("   2. 前端JavaScript定时器 → 后端APScheduler")
            print("   3. 浏览器页面依赖 → 后端服务独立运行")
            print("   4. 本地存储 → MySQL数据库存储")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_backend_scheduled_tasks() 
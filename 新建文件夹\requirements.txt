# Core Framework
Flask==2.3.3
Flask-SocketIO==5.3.5  # WebSocket支持
Werkzeug==2.3.7
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3

# Database
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
greenlet==2.0.2
PyMySQL==1.1.0

# Date and Time
python-dateutil==2.8.2
six==1.16.0

# Development Tools
colorama==0.4.6  # Windows 终端支持

# Deployment
pyinstaller==6.1.0  # 打包成可执行文件

# Additional Dependencies
altgraph==0.17.4  # PyInstaller 依赖
pefile==2023.2.7  # Windows PE文件处理
pywin32-ctypes==0.2.2  # Windows 系统调用支持
setuptools==65.5.1  # 安装工具
typing-extensions  # 类型提示支持
openai==1.12.0  # OpenAI API 客户端

# Excel Handling
pandas==2.2.1
openpyxl==3.1.2  # Excel file support for pandas

# Email Handling
email-validator==2.1.0  # 邮箱验证

# Additional Dependencies
numpy
python-dotenv==1.0.0
blinker==1.6.3
et-xmlfile==1.1.0
cffi==1.16.0
pycparser==2.21
cryptography==41.0.7

# APScheduler 相关
APScheduler==3.10.4
redis>=4.0.0  # 可选用于存储
psutil==5.9.6  # 系统监控 
ortools==9.14.6206
# -*- coding: utf-8 -*-
"""
兼容层包

提供传统模型到统一模型的透明兼容，确保现有代码无需修改即可使用新的统一数据模型。

兼容层设计原则：
1. 透明性：现有代码无需修改
2. 渐进性：支持功能开关控制
3. 性能：最小化性能损失
4. 完整性：覆盖所有传统模型接口

Author: AI Assistant
Date: 2025-01-14
"""

from flask import current_app
from .legacy_model_proxy import (
    ET_WAIT_LOT_Proxy,
    WIP_LOT_Proxy, 
    LOT_WIP_Proxy,
    Test_Spec_Proxy
)

def is_unified_models_enabled():
    """检查是否启用统一模型"""
    return current_app.config.get('ENABLE_UNIFIED_MODELS', False)

def get_compatible_model(model_name):
    """获取兼容的模型类"""
    if not is_unified_models_enabled():
        # 如果未启用统一模型，返回传统模型
        from app.models import ET_WAIT_LOT, WIP_LOT, LOT_WIP, Test_Spec
        model_map = {
            'v_et_wait_lot_unified': ET_WAIT_LOT,
            'v_wip_lot_unified': WIP_LOT,
            'LOT_WIP': LOT_WIP,
            'Test_Spec': Test_Spec
        }
        return model_map.get(model_name)
    else:
        # 如果启用统一模型，返回兼容代理
        proxy_map = {
            'v_et_wait_lot_unified': ET_WAIT_LOT_Proxy,
            'v_wip_lot_unified': WIP_LOT_Proxy,
            'LOT_WIP': LOT_WIP_Proxy,
            'Test_Spec': Test_Spec_Proxy
        }
        return proxy_map.get(model_name)

# 导出兼容接口
__all__ = [
    'is_unified_models_enabled',
    'get_compatible_model',
    'ET_WAIT_LOT_Proxy',
    'WIP_LOT_Proxy',
    'LOT_WIP_Proxy',
    'Test_Spec_Proxy'
] 
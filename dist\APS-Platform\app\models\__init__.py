"""
数据模型包

包含所有数据模型定义，包括传统模型和统一模型
"""

# 导入传统模型（从app/models.py）
import sys
import os

# 导入传统模型
try:
    # 使用exec从models.py文件导入
    import importlib.util
    import sys
    
    models_path = os.path.join(os.path.dirname(__file__), '..', 'models.py')
    if os.path.exists(models_path):
        spec = importlib.util.spec_from_file_location("models", models_path)
        models_module = importlib.util.module_from_spec(spec)
        sys.modules['models'] = models_module
        spec.loader.exec_module(models_module)
        
        # 导入核心模型
        for model_name in ['User', 'UserPermission', 'UserActionLog', 'MenuSetting', 'SystemSetting', 
                          'Product', 'ProductionOrder', 'ProductionSchedule', 'CustomerOrder', 'OrderItem',
                          'Resource', 'TestSpec', 'MaintenanceRecord', 'ResourceUsageLog', 'WIPRecord',
                          'ET_WAIT_LOT', 'WIP_LOT', 'CT', 'ET_FT_TEST_SPEC', 'EQP_STATUS', 
                          'ET_UPH_EQP', 'TCC_INV', 'ET_RECIPE_FILE', 'DevicePriorityConfig', 'LotPriorityConfig',
                          'AISettings', 'ProductPriorityConfig', 'UserFilterPresets', 'Settings',
                          'SchedulingTasks', 'DatabaseInfo', 'MigrationLog', 'EmailConfig', 'ExcelMapping',
                          'EmailAttachment', 'OrderData', 'LotTypeClassificationRule', 'SchedulerJob', 'SchedulerJobLog', 'SchedulerConfig', 'SchedulingConfig']:
            if hasattr(models_module, model_name):
                globals()[model_name] = getattr(models_module, model_name)
        
        TRADITIONAL_MODELS_AVAILABLE = True
        print("传统模型导入成功")
    else:
        print("models.py文件不存在")
        TRADITIONAL_MODELS_AVAILABLE = False
except Exception as e:
    print(f"传统模型导入失败: {e}")
    TRADITIONAL_MODELS_AVAILABLE = False

# 导入统一模型
try:
    from app.models.unified.unified_lot_model import UnifiedLotModel
    from app.models.unified.unified_test_spec import UnifiedTestSpec
    from app.models.unified.migration_services import (
        LotDataMigrationService,
        TestSpecMigrationService, 
        DataMigrationManager
    )
    
    # 导入数据库配置模型
    from app.models.system.database_config import DatabaseConfig, DatabaseMapping
    
    # 统一模型可用标志
    UNIFIED_MODELS_AVAILABLE = True
    
except ImportError as e:
    # 如果统一模型不可用，设置标志
    UNIFIED_MODELS_AVAILABLE = False
    print(f"警告: 统一模型导入失败: {e}")
    
    # 创建空的类以避免导入错误
    class UnifiedLotModel:
        pass
    class UnifiedTestSpec:
        pass
    class LotDataMigrationService:
        pass
    class TestSpecMigrationService:
        pass
    class DataMigrationManager:
        pass

# 导出所有模型
__all__ = [
    # 统一模型
    'UnifiedLotModel', 'UnifiedTestSpec',
    'LotDataMigrationService', 'TestSpecMigrationService', 'DataMigrationManager',
    
    # 数据库配置模型
    'DatabaseConfig', 'DatabaseMapping',
    
    # 标志
    'UNIFIED_MODELS_AVAILABLE', 'TRADITIONAL_MODELS_AVAILABLE'
]

# 如果传统模型可用，添加常用模型到导出列表
if TRADITIONAL_MODELS_AVAILABLE:
    # 添加核心模型
    core_models = [
        'User', 'UserPermission', 'ET_WAIT_LOT', 'WIP_LOT', 'LOT_WIP', 'TestSpec',
        'EQP_STATUS', 'ET_FT_TEST_SPEC', 'ET_UPH_EQP', 'CT',
        'TCC_INV', 'DevicePriorityConfig', 'LotPriorityConfig'
    ]
    
    for model_name in core_models:
        if model_name in globals():
            __all__.append(model_name) 
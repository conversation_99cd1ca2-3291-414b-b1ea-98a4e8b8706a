<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FontAwesome图标测试</title>
    <link rel="stylesheet" href="/static/vendor/fontawesome/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; }
        .icon-item { padding: 10px; text-align: center; border: 1px solid #eee; }
        .icon-item i { font-size: 24px; margin-bottom: 5px; }
    </style>
</head>
<body>
    <h1>FontAwesome图标测试页面</h1>
    
    <div class="test-section">
        <h2>常用图标测试</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <i class="fas fa-home"></i>
                <div>首页 (fas fa-home)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-user"></i>
                <div>用户 (fas fa-user)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-cog"></i>
                <div>设置 (fas fa-cog)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-chart-bar"></i>
                <div>图表 (fas fa-chart-bar)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-database"></i>
                <div>数据库 (fas fa-database)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-bell"></i>
                <div>通知 (fas fa-bell)</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>菜单图标测试</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <i class="fas fa-tachometer-alt"></i>
                <div>仪表盘 (fas fa-tachometer-alt)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-industry"></i>
                <div>生产 (fas fa-industry)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-clipboard-list"></i>
                <div>订单 (fas fa-clipboard-list)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-tools"></i>
                <div>资源 (fas fa-tools)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-boxes"></i>
                <div>WIP (fas fa-boxes)</div>
            </div>
            <div class="icon-item">
                <i class="fas fa-cogs"></i>
                <div>系统 (fas fa-cogs)</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>调试信息</h2>
        <div id="debug-info">
            <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
            <p><strong>时间戳:</strong> <span id="timestamp"></span></p>
            <div id="font-status"></div>
        </div>
    </div>
    
    <script>
        // 显示调试信息
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // 检查字体加载状态
        function checkFontLoading() {
            const fontStatusDiv = document.getElementById('font-status');
            
            if (document.fonts) {
                document.fonts.ready.then(() => {
                    let loadedFonts = [];
                    document.fonts.forEach(font => {
                        if (font.family.includes('Font Awesome')) {
                            loadedFonts.push(font.family + ' - ' + font.status);
                        }
                    });
                    
                    if (loadedFonts.length > 0) {
                        fontStatusDiv.innerHTML = '<p><strong>已加载的字体:</strong></p><ul>' + 
                            loadedFonts.map(f => '<li>' + f + '</li>').join('') + '</ul>';
                    } else {
                        fontStatusDiv.innerHTML = '<p style="color: red;"><strong>未检测到FontAwesome字体</strong></p>';
                    }
                });
            } else {
                fontStatusDiv.innerHTML = '<p>浏览器不支持字体API检测</p>';
            }
        }
        
        // 页面加载完成后检查字体
        window.addEventListener('load', checkFontLoading);
    </script>
</body>
</html>
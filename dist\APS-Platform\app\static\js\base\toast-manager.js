/**
 * 统一的Toast通知管理器
 * 提供标准化的提示信息样式和位置
 * 
 * 使用方法:
 * ToastManager.show('操作成功', 'success');
 * ToastManager.show('操作失败', 'error');
 * ToastManager.show('警告信息', 'warning');
 * ToastManager.show('提示信息', 'info');
 */
class ToastManager {
    constructor() {
        this.container = null;
        this.toastCounter = 0;
        this.init();
    }

    /**
     * 初始化Toast容器
     */
    init() {
        // 检查是否已存在容器
        this.container = document.getElementById('global-toast-container');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'global-toast-container';
            this.container.className = 'toast-container position-fixed top-0 end-0 p-3';
            this.container.style.zIndex = '9999';
            document.body.appendChild(this.container);
        }
    }

    /**
     * 显示Toast通知
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)，默认5000ms
     * @param {boolean} closable - 是否可手动关闭，默认true
     */
    show(message, type = 'info', duration = 5000, closable = true) {
        if (!message) return;

        // 确保容器存在
        this.init();

        const toastId = `toast-${++this.toastCounter}-${Date.now()}`;
        const toastElement = this.createToastElement(toastId, message, type, closable);
        
        // 添加到容器
        this.container.appendChild(toastElement);
        
        // 初始化Bootstrap Toast
        const bsToast = new bootstrap.Toast(toastElement, {
            autohide: duration > 0,
            delay: duration
        });
        
        // 显示Toast
        bsToast.show();
        
        // 监听隐藏事件，移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', () => {
            if (toastElement.parentNode) {
                toastElement.parentNode.removeChild(toastElement);
            }
        });

        return toastId;
    }

    /**
     * 创建Toast元素
     */
    createToastElement(toastId, message, type, closable) {
        const toastElement = document.createElement('div');
        toastElement.id = toastId;
        toastElement.className = 'toast align-items-center border-0';
        toastElement.setAttribute('role', 'alert');
        toastElement.setAttribute('aria-live', 'assertive');
        toastElement.setAttribute('aria-atomic', 'true');

        // 设置颜色主题
        const colorClass = this.getColorClass(type);
        toastElement.className += ` ${colorClass}`;

        // 创建内容
        const icon = this.getIcon(type);
        const closeButton = closable ? 
            '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="关闭"></button>' : 
            '';

        toastElement.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${icon} me-2"></i>${this.escapeHtml(message)}
                </div>
                ${closeButton}
            </div>
        `;

        return toastElement;
    }

    /**
     * 获取颜色类
     */
    getColorClass(type) {
        const colorMap = {
            'success': 'text-bg-success',
            'error': 'text-bg-danger',
            'danger': 'text-bg-danger',
            'warning': 'text-bg-warning',
            'info': 'text-bg-info',
            'primary': 'text-bg-primary',
            'secondary': 'text-bg-secondary'
        };
        return colorMap[type] || 'text-bg-info';
    }

    /**
     * 获取图标
     */
    getIcon(type) {
        const iconMap = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'danger': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle',
            'primary': 'fa-info-circle',
            'secondary': 'fa-info-circle'
        };
        return iconMap[type] || 'fa-info-circle';
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 快捷方法 - 成功消息
     */
    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    /**
     * 快捷方法 - 错误消息
     */
    error(message, duration = 5000) {
        return this.show(message, 'error', duration);
    }

    /**
     * 快捷方法 - 警告消息
     */
    warning(message, duration = 4000) {
        return this.show(message, 'warning', duration);
    }

    /**
     * 快捷方法 - 信息消息
     */
    info(message, duration = 3000) {
        return this.show(message, 'info', duration);
    }

    /**
     * 关闭指定的Toast
     */
    hide(toastId) {
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
            const bsToast = bootstrap.Toast.getInstance(toastElement);
            if (bsToast) {
                bsToast.hide();
            }
        }
    }

    /**
     * 关闭所有Toast
     */
    hideAll() {
        const toasts = this.container.querySelectorAll('.toast');
        toasts.forEach(toast => {
            const bsToast = bootstrap.Toast.getInstance(toast);
            if (bsToast) {
                bsToast.hide();
            }
        });
    }

    /**
     * 清理容器
     */
    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// 创建全局实例
const toastManager = new ToastManager();

// 提供全局访问
window.ToastManager = toastManager;

// 兼容性别名
window.showToast = function(message, type = 'info', duration = 5000) {
    return toastManager.show(message, type, duration);
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保ToastManager已初始化
    toastManager.init();
}); 
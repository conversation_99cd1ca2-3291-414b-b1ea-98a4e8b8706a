"""
生产相关模型 - 模块化结构

包含生产订单、排产计划、产品优先级等生产相关的数据模型
"""

# 暂时禁用生产模型导入以避免循环导入和表重复定义问题
# 这些模型可以直接从主models.py导入使用
try:
    import logging
    logger = logging.getLogger(__name__)
    logger.info("生产模型包已禁用，请直接从app.models导入")
    
    # 只导入新的批次模型
    __all__ = []
    try:
        from .wait_lots import WaitLot
        from .done_lots import DoneLot
        __all__.extend(['WaitLot', 'DoneLot'])
    except ImportError:
        pass
    
except Exception as e:
    # 如果导入失败，记录错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"生产模型包初始化失败: {e}")
    
    __all__ = [] 
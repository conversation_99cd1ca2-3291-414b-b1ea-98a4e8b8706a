{% extends "base.html" %}

{% block title %}车规芯片终测智能调度平台 - AI助手
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block page_title %}AI智能助手{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 左侧对话历史 -->
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">对话历史</h5>
                    <button class="btn btn-sm btn-light" id="delete-history-btn" title="删除所有对话历史">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="chat-history-list">
                        <!-- 对话历史将通过JavaScript动态添加 -->
                        <div class="list-group-item list-group-item-action text-center text-muted">
                            暂无对话历史
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">AI助手信息</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-robot fa-2x me-3 text-danger"></i>
                        <div>
                            <h6 class="mb-0" id="model-name">Deepseek Chat</h6>
                            <small class="text-muted" id="api-status">API状态: 正在检查...</small>

                            <div id="r1-status-container" class="mt-1" style="display: inline-block;">
                                <button class="btn btn-sm btn-outline-secondary" id="check-r1-btn">
                                    <i class="fas fa-sync-alt me-1"></i>检查R1状态
                                </button>
                                <span id="r1-status-badge" class="badge bg-secondary text-white ms-2" style="display: none;">R1状态: 未知</span>
                            </div>
                        </div>
                    </div>
                    
                    <h6>功能特点:</h6>
                    <ul class="mb-0" id="features-list">
                        <li>智能问答</li>
                        <li>生产排程辅助</li>
                        <li>数据分析支持</li>
                        <li>工艺优化建议</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 右侧对话区域 -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">AI对话</h5>
                    <button class="btn btn-sm btn-light" id="new-chat-btn">
                        <i class="fas fa-plus me-1"></i>新对话
                    </button>
                </div>
                <div class="card-body">
                    <div class="chat-container" id="chat-messages">
                        <!-- 欢迎消息 -->
                        <div class="chat-message assistant-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-header">
                                    <span class="message-sender">AI助手</span>
                                    <span class="message-time">刚刚</span>
                                </div>
                                <div class="message-text">
                                    <p>您好！我是车规芯片终测智能调度平台的AI助手，可以帮助您解答关于芯片终测调度、订单管理、资源分配等方面的问题。请问有什么可以帮助您的吗？</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 思考指示器 - 放在聊天容器和模型选择按钮之间 -->
                    <div id="typing-indicator" class="thinking-indicator" style="display: none;">
                        <div class="thinking-bubble">
                            <div class="thinking-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="thinking-content">
                                <span>AI正在思考</span>
                                <span class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 模型选择按钮 -->
                    <div class="model-selection-buttons d-flex justify-content-start mb-2">
                        <button class="btn btn-sm btn-outline-danger me-2 model-btn" id="standard-btn">
                            <i class="fas fa-comment-dots me-1"></i>标准对话
                        </button>
                        <button class="btn btn-sm btn-outline-danger me-2 model-btn" id="deepseek-r1-btn">
                            <i class="fas fa-brain me-1"></i>深度思考 (R1)
                        </button>
                        <button class="btn btn-sm btn-outline-danger me-2 model-btn" id="web-search-btn">
                            <i class="fas fa-globe me-1"></i>联网搜索
                        </button>
                        <button class="btn btn-sm btn-outline-danger me-2 model-btn" id="database-btn">
                            <i class="fas fa-database me-1"></i>数据库查询
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" id="refresh-dify-btn" style="display: none;">
                            <i class="fas fa-sync-alt me-1"></i>刷新聊天窗口
                        </button>
                    </div>
                    
                    <!-- Dify聊天窗口容器 -->
                    <div id="dify-chat-container" style="display: none; height: 60vh; margin-bottom: 1rem; background-color: #f8f9fa; border-radius: 0.25rem; position: relative; overflow: hidden;">
                        <!-- Dify聊天窗口将在这里显示 -->
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="chat-input-container mt-3" id="aps-chat-input">
                        <form id="chat-form">
                            <div class="input-group">
                                <textarea class="form-control" id="user-input" placeholder="请输入您的问题..." rows="2"></textarea>
                                <button class="btn btn-primary" type="submit" id="send-button">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                        <div class="d-flex justify-content-between mt-2">
                            <small class="text-muted">提示: 您可以询问关于生产排程、订单管理等方面的问题</small>
                            <small class="text-muted" id="typing-indicator" style="display: none;">
                                AI正在思考···
                                <span class="typing-indicator">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </span>
                            </small>
                        </div>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted" id="model-indicator">当前模式: 标准对话</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="delete-confirm-message">
                确定要删除这个对话吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 撤销删除提示 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
    <div id="undoToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="5000">
        <div class="toast-header">
            <i class="fas fa-info-circle me-2 text-primary"></i>
            <strong class="me-auto">提示</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            <span id="toast-message">对话已删除</span>
            <button class="btn btn-sm btn-primary ms-2" id="undo-delete-btn">撤销</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* 对话容器样式 */
    .chat-container {
        height: 60vh;
        overflow-y: auto;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
        position: relative; /* 添加相对定位 */
    }
    
    /* 消息样式 */
    .chat-message {
        display: flex;
        margin-bottom: 1.5rem;
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.8rem;
        flex-shrink: 0;
    }
    
    .user-message .message-avatar {
        background-color: #007bff;
        color: white;
    }
    
    .assistant-message .message-avatar {
        background-color: #dc3545;
        color: white;
    }
    
    .message-content {
        flex-grow: 1;
        background-color: white;
        border-radius: 0.5rem;
        padding: 0.8rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        max-width: 85%;
    }
    
    .user-message .message-content {
        background-color: #e9f5ff;
    }
    
    .message-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 0.5rem;
    }
    
    .message-sender {
        font-weight: bold;
    }
    
    .message-time {
        color: #6c757d;
        font-size: 0.8rem;
    }
    
    .message-text {
        line-height: 1.5;
        word-break: break-word;
    }
    
    /* 输入区样式 */
    .chat-input-container {
        position: relative;
    }
    
    #user-input {
        resize: none;
        border-radius: 0.25rem;
    }
    
    /* 模型选择按钮样式 */
    .model-selection-buttons {
        margin-top: -0.5rem;
    }
    
    .model-selection-buttons .btn.active {
        background-color: #dc3545;
        color: white;
    }
    
    /* 打字指示器 */
    .typing-indicator {
        display: inline-flex;
        align-items: center;
    }
    
    .typing-indicator span {
        height: 5px;
        width: 5px;
        background-color: #dc3545;
        border-radius: 50%;
        display: inline-block;
        margin: 0 2px;
        animation: blink 1.4s infinite both;
    }
    
    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes blink {
        0% { opacity: 0.1; }
        20% { opacity: 1; }
        100% { opacity: 0.1; }
    }
    
    /* 自定义滚动条 */
    .chat-container::-webkit-scrollbar {
        width: 6px;
    }
    
    .chat-container::-webkit-scrollbar-track {
        background: #f1f1f1;
    }
    
    .chat-container::-webkit-scrollbar-thumb {
        background: #dc3545;
        border-radius: 3px;
    }
    
    /* 代码块样式 */
    .code-block {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 10px;
        margin: 10px 0;
        border-left: 4px solid #dc3545;
        font-family: 'Courier New', monospace;
        overflow-x: auto;
    }
    
    /* 数据库查询结果样式 */
    .text-primary {
        color: #dc3545 !important;
        font-size: 1.1em;
    }
    
    /* 查询结果标题样式 */
    strong.text-primary {
        display: block;
        margin-top: 15px;
        margin-bottom: 5px;
        background-color: #f8f9fa;
        padding: 5px;
        border-radius: 5px;
    }
    
    /* 数据库表格样式 */
    .db-table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
        font-size: 0.9em;
    }
    
    .db-table th, .db-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .db-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .db-table tr:hover {
        background-color: #f8f9fa;
    }
    
    /* 数据库值高亮 */
    .db-value {
        font-weight: bold;
    }
    
    /* 状态指示器 */
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-running { background-color: #28a745; } /* 绿色 - 运行 */
    .status-idle { background-color: #6c757d; } /* 灰色 - 闲置 */
    .status-error { background-color: #dc3545; } /* 红色 - 故障 */
    .status-down { background-color: #ffc107; } /* 黄色 - 停机 */
    
    /* 思考指示器样式 */
    .thinking-indicator {
        width: 100%;
        padding: 5px 0;
        margin-bottom: 10px;
        animation: fadeIn 0.3s ease-in-out;
    }
    
    .thinking-bubble {
        display: flex;
        align-items: center;
        background-color: #f8f9fa;
        border-radius: 20px;
        padding: 8px 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        width: fit-content;
        margin-left: 10px;
        border: 1px solid #e9ecef;
    }
    
    .thinking-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #dc3545;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
    }
    
    .thinking-content {
        display: flex;
        align-items: center;
        font-weight: 500;
        color: #333;
    }
    
    .typing-dots {
        display: inline-flex;
        margin-left: 8px;
    }
    
    .typing-dots span {
        height: 6px;
        width: 6px;
        background-color: #dc3545;
        border-radius: 50%;
        display: inline-block;
        margin: 0 2px;
        animation: pulse 1.4s infinite ease-in-out both;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(0.6); opacity: 0.6; }
        50% { transform: scale(1); opacity: 1; }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 清空localStorage中的对话历史
        localStorage.removeItem('aps_chat_sessions');
        
        // 元素引用
        const chatForm = document.getElementById('chat-form');
        const userInput = document.getElementById('user-input');
        const chatMessages = document.getElementById('chat-messages');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');
        const newChatBtn = document.getElementById('new-chat-btn');
        const chatHistoryList = document.getElementById('chat-history-list');
        const modelName = document.getElementById('model-name');
        const apiStatus = document.getElementById('api-status');
        const featuresList = document.getElementById('features-list');
        const standardBtn = document.getElementById('standard-btn');
        const deepseekR1Btn = document.getElementById('deepseek-r1-btn');
        const webSearchBtn = document.getElementById('web-search-btn');
        const databaseBtn = document.getElementById('database-btn');
        const modelIndicator = document.getElementById('model-indicator');
        const modelBtns = document.querySelectorAll('.model-btn');
        const deleteHistoryBtn = document.getElementById('delete-history-btn');
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
        const deleteConfirmMessage = document.getElementById('delete-confirm-message');
        const undoToast = new bootstrap.Toast(document.getElementById('undoToast'));
        const undoDeleteBtn = document.getElementById('undo-delete-btn');
        const toastMessage = document.getElementById('toast-message');
        const chatContainer = document.getElementById('chat-messages');
        const apsChatInput = document.getElementById('aps-chat-input');
        const refreshDifyBtn = document.getElementById('refresh-dify-btn');
        
        // 存储对话历史
        let messageHistory = [];
        let chatSessions = [];
        let currentSessionId = generateSessionId();
        let currentMode = 'standard'; // 'standard', 'r1', 'web_search', 'database'
        let deletedSessions = []; // 存储被删除的会话，用于撤销
        let deleteTarget = null; // 当前要删除的目标
        let difyChatLoaded = false; // 标记Dify聊天是否已加载
        
        // 获取AI助手配置
        fetchAIConfig();
        
        // 初始化模式按钮状态
        updateModelButtons();
        
        // 表单提交处理
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const message = userInput.value.trim();
            if (message) {
                sendMessage(message);
                userInput.value = '';
            }
        });
        
        // 键盘Enter键提交
        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const message = userInput.value.trim();
                if (message) {
                    sendMessage(message);
                    userInput.value = '';
                }
            }
        });
        
        // 新对话按钮点击处理
        newChatBtn.addEventListener('click', function() {
            // 保存当前会话
            if (messageHistory.length > 0) {
                saveChatSession();
            }
            
            // 创建新会话
            messageHistory = [];
            currentSessionId = generateSessionId();
            currentMode = 'standard';
            updateModelIndicator();
            updateModelButtons();
            
            // 清空聊天区域，只保留欢迎消息
            chatMessages.innerHTML = '';
            addAssistantMessage('您好！我是车规芯片终测智能调度平台的AI助手，可以帮助您解答关于芯片终测调度、订单管理、资源分配等方面的问题。请问有什么可以帮助您的吗？');
            
            // 关闭Dify聊天（如果处于打开状态）
            if (currentMode === 'database') {
                switchToDatabaseMode(false);
                currentMode = 'standard';
            }
            
            // 更新UI
            updateChatHistoryUI();
        });
        
        // 删除历史按钮点击处理
        deleteHistoryBtn.addEventListener('click', function() {
            deleteTarget = 'all';
            deleteConfirmMessage.textContent = '确定要删除所有对话历史吗？此操作不可恢复。';
            deleteConfirmModal.show();
        });
        
        // 确认删除按钮点击处理
        confirmDeleteBtn.addEventListener('click', function() {
            if (deleteTarget === 'all') {
                // 备份当前会话，用于可能的撤销
                deletedSessions = [...chatSessions];
                
                // 清空会话数据
                chatSessions = [];
                localStorage.removeItem('aps_chat_sessions');
                
                // 创建新会话
                messageHistory = [];
                currentSessionId = generateSessionId();
                currentMode = 'standard';
                updateModelIndicator();
                updateModelButtons();
                
                // 清空聊天区域，只保留欢迎消息
                chatMessages.innerHTML = '';
                addAssistantMessage('您好！我是车规芯片终测智能调度平台的AI助手，可以帮助您解答关于芯片终测调度、订单管理、资源分配等方面的问题。请问有什么可以帮助您的吗？');
                
                // 更新UI
                updateChatHistoryUI();
                
                // 显示提示消息
                addAssistantMessage('所有对话历史已清空。');
                
                // 显示撤销提示
                toastMessage.textContent = '所有对话历史已删除';
                undoToast.show();
            } else if (deleteTarget) {
                // 删除单个会话
                deleteSessionById(deleteTarget);
            }
            
            // 隐藏模态框
            deleteConfirmModal.hide();
        });
        
        // 撤销删除按钮点击处理
        undoDeleteBtn.addEventListener('click', function() {
            if (deletedSessions.length > 0) {
                if (deleteTarget === 'all') {
                    // 恢复所有会话
                    chatSessions = [...deletedSessions];
                    localStorage.setItem('aps_chat_sessions', JSON.stringify(chatSessions));
                    updateChatHistoryUI();
                    addAssistantMessage('已恢复所有删除的对话历史。');
                } else {
                    // 恢复单个会话
                    const deletedSession = deletedSessions.find(s => s.id === deleteTarget);
                    if (deletedSession) {
                        chatSessions.unshift(deletedSession);
                        chatSessions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                        localStorage.setItem('aps_chat_sessions', JSON.stringify(chatSessions));
                        updateChatHistoryUI();
                        addAssistantMessage('已恢复删除的对话。');
                    }
                }
                
                // 清空删除记录
                deletedSessions = [];
                deleteTarget = null;
                
                // 隐藏撤销提示
                undoToast.hide();
            }
        });
        
        // 标准模式按钮点击处理
        standardBtn.addEventListener('click', function() {
            if (currentMode !== 'standard') {
                currentMode = 'standard';
                updateModelIndicator();
                updateModelButtons();
                addAssistantMessage('已切换到标准对话模式。');
            }
        });
        
        // R1模式按钮点击处理
        deepseekR1Btn.addEventListener('click', function() {
            if (currentMode !== 'r1') {
                // 如果模式按钮被禁用了，提示用户
                if (deepseekR1Btn.disabled) {
                    addAssistantMessage('抱歉，深度思考(R1)模式当前不可用。请检查R1模型状态或稍后再试。');
                    return;
                }
                
                // 设置一个标记，表示我们尝试切换到R1模式
                const tryingR1Mode = true;
                
                // 先添加一个临时的加载消息
                const loadingMsgId = 'r1-loading-' + Date.now();
                const loadingMsg = '正在切换到深度思考(R1)模式，正在检查模型可用性...';
                addAssistantMessage(loadingMsg, { tempId: loadingMsgId });
                
                // 发送测试请求以检查R1模型可用性
                fetch('/api/ai/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: "这是一个测试R1模型可用性的请求，请回复'可用'一词。",
                        history: [],
                        mode: 'r1'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // 移除加载消息
                    const loadingElement = document.getElementById(loadingMsgId);
                    if (loadingElement) {
                        loadingElement.remove();
                    }
                    
                    if (data.model_fallback) {
                        // R1模型不可用
                        addAssistantMessage('抱歉，深度思考(R1)模式当前不可用。系统已检测到DeepSeek-R1模型无法访问，请稍后再试。');
                        
                        // 更新R1状态指示器
                        const r1StatusBadge = document.getElementById('r1-status-badge');
                        r1StatusBadge.style.display = 'inline-block';
                        r1StatusBadge.className = 'badge bg-danger text-white ms-2';
                        r1StatusBadge.textContent = 'R1状态: 不可用';
                        
                        // 禁用R1按钮
                        deepseekR1Btn.disabled = true;
                    } else {
                        // R1模型可用，切换模式
                        currentMode = 'r1';
                        updateModelIndicator();
                        updateModelButtons();
                        addAssistantMessage('已切换到深度思考(R1)模式，我将使用更强大的推理能力来回答您的问题。');
                        
                        // 更新R1状态指示器
                        const r1StatusBadge = document.getElementById('r1-status-badge');
                        r1StatusBadge.style.display = 'inline-block';
                        r1StatusBadge.className = 'badge bg-success text-white ms-2';
                        r1StatusBadge.textContent = 'R1状态: 可用';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    
                    // 移除加载消息
                    const loadingElement = document.getElementById(loadingMsgId);
                    if (loadingElement) {
                        loadingElement.remove();
                    }
                    
                    // 显示错误消息
                    addAssistantMessage('切换模式时出现错误，请稍后再试。');
                });
            } else {
                currentMode = 'standard';
                updateModelIndicator();
                updateModelButtons();
                addAssistantMessage('已切换回标准对话模式。');
            }
        });
        
        // 联网搜索按钮点击处理
        webSearchBtn.addEventListener('click', function() {
            if (currentMode !== 'web_search') {
                currentMode = 'web_search';
                updateModelIndicator();
                updateModelButtons();
                addAssistantMessage('已切换到联网搜索模式，我将使用网络搜索来获取最新信息回答您的问题。');
            } else {
                currentMode = 'standard';
                updateModelIndicator();
                updateModelButtons();
                addAssistantMessage('已切换回标准对话模式。');
            }
        });
        
        // 数据库查询按钮点击处理
        databaseBtn.addEventListener('click', function() {
            if (currentMode !== 'database') {
                // 切换到数据库查询模式
                currentMode = 'database';
                updateModelIndicator();
                updateModelButtons();
                
                // 添加提示消息
                addAssistantMessage('已切换到数据库查询模式，我将直接从系统数据库中获取信息来回答您的问题。');
                
                // 显示数据库连接状态信息
                addAssistantMessage('正在尝试连接到数据库...');
                
                // 发起一个测试查询以检查数据库连接
                fetch('/api/ai/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: "请检查数据库连接状态",
                        history: [],
                        mode: 'database'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.db_queried) {
                        addAssistantMessage('已成功连接到数据库。您现在可以询问关于数据库中的信息，例如设备状态、批次信息、工程配方等。');
                    } else {
                        addAssistantMessage('警告：数据库连接可能存在问题。我会尝试回答您的问题，但可能无法获取完整的数据库信息。');
                    }
                })
                .catch(error => {
                    console.error('数据库检查错误:', error);
                    addAssistantMessage('连接数据库时出现错误。请稍后再试或联系系统管理员。');
                    // 出错时自动切换回标准模式
                    currentMode = 'standard';
                    updateModelIndicator();
                    updateModelButtons();
                });
            } else {
                // 切换回标准模式
                currentMode = 'standard';
                updateModelIndicator();
                updateModelButtons();
                
                addAssistantMessage('已切换回标准对话模式。');
            }
        });
        
        // 切换到数据库模式（加载Dify聊天）
        function switchToDatabaseMode(enable) {
            if (enable) {
                // 隐藏标准聊天界面，显示Dify聊天容器
                chatContainer.style.display = 'none';
                apsChatInput.style.display = 'none';
                
                // 清空之前的Dify聊天容器内容（如果有）
                apsChatInput.innerHTML = '';
                
                // 如果全局已存在Dify实例，先尝试清理
                if (window.difyChatbot) {
                    try {
                        // 尝试移除旧的实例
                        const oldButton = document.getElementById('uV72gGRdNz0eP7ac');
                        if (oldButton) {
                            oldButton.remove();
                        }
                        delete window.difyChatbot;
                    } catch (e) {
                        console.warn('清理旧的Dify聊天实例失败:', e);
                    }
                }
                
                try {
                    // 为Dify加载显示加载状态
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'text-center p-5';
                    loadingDiv.id = 'dify-loading-indicator';
                    loadingDiv.innerHTML = `
                        <div class="spinner-border text-danger mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5>正在加载Dify数据库查询助手...</h5>
                        <p class="text-muted">如果长时间未加载完成，请检查网络连接或点击刷新按钮重试</p>
                    `;
                    apsChatInput.appendChild(loadingDiv);
                    
                    // 加载Dify聊天脚本 - 使用延迟加载避免立即连接
                    setTimeout(() => {
                        loadDifyChat();
                    }, 500);
                    
                    // 添加提示信息
                    console.log('正在切换到Dify数据库查询模式...');
                } catch (err) {
                    console.error('初始化Dify容器失败:', err);
                    addAssistantMessage('初始化数据库查询模式时出现错误，请重试。');
                    // 切换回标准模式
                    currentMode = 'standard';
                    updateModelIndicator();
                    updateModelButtons();
                    
                    // 显示标准聊天界面，隐藏Dify聊天容器
                    chatContainer.style.display = 'block';
                    apsChatInput.style.display = 'block';
                }
            } else {
                // 显示标准聊天界面，隐藏Dify聊天容器
                chatContainer.style.display = 'block';
                apsChatInput.style.display = 'block';
                
                // 尝试移除Dify对象
                try {
                    const scriptElement = document.getElementById('uV72gGRdNz0eP7ac');
                    if (scriptElement) {
                        scriptElement.remove();
                    }
                    
                    // 删除可能的全局对象
                    if (window.difyChatbot) {
                        delete window.difyChatbot;
                    }
                } catch (e) {
                    console.warn('清理Dify聊天对象失败:', e);
                }
                
                // 清空Dify聊天容器
                apsChatInput.innerHTML = '';
                
                console.log('已切换回标准对话模式');
            }
        }
        
        // 加载Dify聊天脚本
        function loadDifyChat() {
            try {
                // 查找加载指示器
                const loadingDiv = document.getElementById('dify-loading-indicator') || apsChatInput;
                
                // 先获取Dify配置
                fetch('/api/v2/system/ai-settings')
                    .then(response => response.json())
                    .then(config => {
                        if (!config.chatbot || !config.chatbot.enabled) {
                            throw new Error('Dify聊天机器人未启用');
                        }
                        
                        // 将Dify配置添加到全局
                        window.difyChatbotConfig = {
                            token: config.chatbot.token,
                            baseUrl: '/api/dify-proxy' // 使用代理API路径
                        };
                        
                        // 创建脚本元素
                        const script = document.createElement('script');
                        script.src = '/api/dify-proxy/embed.min.js';
                        script.id = config.chatbot.token;
                        script.defer = true;
                        
                        // 脚本加载成功时的处理
                        script.onload = function() {
                            console.log('Dify聊天脚本加载成功');
                            // 移除加载提示
                            const loadingIndicator = document.getElementById('dify-loading-indicator');
                            if (loadingIndicator) {
                                loadingIndicator.remove();
                            }
                            
                            // 为了确保Dify聊天窗口正确显示，添加一个小延迟
                            setTimeout(() => {
                                // 自定义Dify窗口样式
                                const customStyle = document.createElement('style');
                                customStyle.textContent = `
                                    #dify-chatbot-bubble-button {
                                        display: none !important;
                                        background-color: #1C64F2 !important;
                                    }
                                    #dify-chatbot-bubble-window {
                                        width: 100% !important;
                                        height: 100% !important;
                                        position: relative !important;
                                        right: auto !important;
                                        bottom: auto !important;
                                        max-height: none !important;
                                        box-shadow: none !important;
                                    }
                                `;
                                document.head.appendChild(customStyle);
                                
                                // 添加成功加载的消息到对话
                                addAssistantMessage('数据库查询模式已加载完成，您可以开始查询数据库信息了。现在您的问题将通过Dify智能助手来回答。');
                            }, 500);
                        };
                        
                        // 脚本加载失败时的处理
                        script.onerror = function() {
                            console.error('Dify聊天脚本加载失败');
                            
                            // 清理加载指示器
                            if (document.getElementById('dify-loading-indicator')) {
                                document.getElementById('dify-loading-indicator').remove();
                            }
                            
                            // 显示错误信息
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'alert alert-danger m-3';
                            errorDiv.innerHTML = `
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>连接失败：</strong>无法连接到Dify智能助手服务器
                                <hr>
                                <p>可能的原因:</p>
                                <ul>
                                    <li>网络连接问题</li>
                                    <li>Dify服务器未启动或不可用 (${config.chatbot.server})</li>
                                    <li>服务器配置或Token错误</li>
                                </ul>
                                <div class="mt-3">
                                    <button class="btn btn-outline-danger me-2" onclick="window.location.reload()">
                                        <i class="fas fa-sync-alt me-1"></i>刷新页面
                                    </button>
                                    <button class="btn btn-outline-secondary" id="manual-dify-retry">
                                        <i class="fas fa-redo me-1"></i>重试连接
                                    </button>
                                </div>
                            `;
                            apsChatInput.appendChild(errorDiv);
                            
                            // 添加重试按钮事件处理
                            document.getElementById('manual-dify-retry').addEventListener('click', function() {
                                // 先清空容器
                                apsChatInput.innerHTML = '';
                                // 再次尝试加载
                                switchToDatabaseMode(true);
                            });
                            
                            // 添加错误消息到对话
                            addAssistantMessage('连接到Dify数据库查询服务时出现错误，请检查系统设置中的Dify配置或稍后再试。');
                            
                            // 显示重试按钮
                            refreshDifyBtn.style.display = 'inline-block';
                        };
                        
                        // 添加到文档中
                        document.body.appendChild(script);
                        
                        difyChatLoaded = true;
                    })
                    .catch(error => {
                        console.error('获取Dify配置失败:', error);
                        
                        // 清理加载指示器
                        if (document.getElementById('dify-loading-indicator')) {
                            document.getElementById('dify-loading-indicator').remove();
                        }
                        
                        // 显示配置错误信息
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'alert alert-warning m-3';
                        errorDiv.innerHTML = `
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>配置错误：</strong>无法获取Dify配置信息
                            <hr>
                            <p>请检查：</p>
                            <ul>
                                <li>系统设置中的Dify聊天机器人配置</li>
                                <li>确保已正确设置Token和服务器地址</li>
                                <li>确保已启用Dify聊天机器人功能</li>
                            </ul>
                            <div class="mt-3">
                                <a href="/system/settings" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-cog me-1"></i>前往系统设置
                                </a>
                                <button class="btn btn-outline-secondary" onclick="location.reload()">
                                    <i class="fas fa-sync-alt me-1"></i>刷新页面
                                </button>
                            </div>
                        `;
                        apsChatInput.appendChild(errorDiv);
                        
                        // 添加错误消息到对话
                        addAssistantMessage('无法获取Dify配置信息，请检查系统设置后重试。');
                        
                        // 显示重试按钮
                        refreshDifyBtn.style.display = 'inline-block';
                    });
                
            } catch (err) {
                console.error('加载Dify聊天时发生错误:', err);
                // 添加错误消息到对话
                addAssistantMessage('加载数据库查询模式时出现错误，请刷新页面后重试。');
                // 切换回标准模式
                currentMode = 'standard';
                updateModelIndicator();
                updateModelButtons();
                // 显示聊天界面
                chatContainer.style.display = 'block';
                apsChatInput.style.display = 'block';
            }
        }
        
        // 更新模式按钮状态
        function updateModelButtons() {
            modelBtns.forEach(btn => btn.classList.remove('active'));
            
            if (currentMode === 'standard') {
                standardBtn.classList.add('active');
            } else if (currentMode === 'r1') {
                deepseekR1Btn.classList.add('active');
            } else if (currentMode === 'web_search') {
                webSearchBtn.classList.add('active');
            } else if (currentMode === 'database') {
                databaseBtn.classList.add('active');
            }
        }
        
        // 刷新Dify按钮点击处理
        refreshDifyBtn.addEventListener('click', function() {
            if (currentMode === 'database') {
                // 显示提示消息
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info mb-3';
                alertDiv.innerHTML = '<i class="fas fa-sync-alt me-2"></i>正在刷新数据库查询窗口...';
                apsChatInput.prepend(alertDiv);
                
                // 3秒后自动移除提示
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
                
                // 重新加载Dify聊天
                switchToDatabaseMode(false);
                setTimeout(() => {
                    switchToDatabaseMode(true);
                }, 500);
            }
        });
        
        // 更新模式指示器
        function updateModelIndicator() {
            if (currentMode === 'standard') {
                modelIndicator.textContent = '当前模式: 标准对话';
            } else if (currentMode === 'r1') {
                modelIndicator.textContent = '当前模式: 深度思考(R1)';
            } else if (currentMode === 'web_search') {
                modelIndicator.textContent = '当前模式: 联网搜索';
            } else if (currentMode === 'database') {
                modelIndicator.textContent = '当前模式: 数据库查询 (Dify)';
            }
        }
        
        // 发送消息到服务器
        function sendMessage(message) {
            // 添加用户消息到UI
            addUserMessage(message);
            
            // 添加到历史记录
            messageHistory.push({
                role: 'user',
                content: message
            });
            
            // 显示正在输入指示器
            typingIndicator.style.display = 'block';
            sendButton.disabled = true;
            
            // 确保滚动到底部以显示思考指示器
            scrollToBottom();
            
            // 发送API请求
            fetch('/api/ai/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    history: messageHistory.slice(0, -1), // 不包括刚刚添加的消息
                    mode: currentMode // 添加当前模式
                })
            })
            .then(response => {
                if (!response.ok && response.status !== 200) {
                    throw new Error(`API请求失败: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 隐藏正在输入指示器
                typingIndicator.style.display = 'none';
                sendButton.disabled = false;
                
                // 检查是否有错误消息
                if (data.error) {
                    console.warn('API返回错误:', data.error);
                }
                
                // 检查是否是模拟模式
                if (data.simulation) {
                    console.info('使用模拟模式回复');
                }
                
                // 添加模型信息到选项中
                const options = {
                    webSearch: data.web_search,
                    dbQueried: data.db_queried,
                    modelUsed: data.model_used,
                    model_fallback: data.model_fallback,
                    original_model_requested: data.original_model_requested
                };
                
                // 如果发生了模型回退，在控制台记录
                if (data.model_fallback) {
                    console.warn(`模型回退: 从 ${data.original_model_requested} 回退到 ${data.model_used}`);
                }
                
                // 添加AI回复到UI
                addAssistantMessage(data.response, options);
                
                // 更新历史记录
                messageHistory.push({
                    role: 'assistant',
                    content: data.response
                });
                
                // 保存会话
                saveChatSession();
                updateChatHistoryUI();
            })
            .catch(error => {
                console.error('Error:', error);
                typingIndicator.style.display = 'none';
                sendButton.disabled = false;
                
                // 显示错误消息
                addAssistantMessage('抱歉，处理您的请求时出现了错误。请稍后再试。');
                
                // 添加到历史记录以保持对话连贯性
                messageHistory.push({
                    role: 'assistant',
                    content: '抱歉，处理您的请求时出现了错误。请稍后再试。'
                });
                
                // 保存会话
                saveChatSession();
                updateChatHistoryUI();
            });
        }
        
        // 添加用户消息到UI
        function addUserMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message user-message';
            
            const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            messageElement.innerHTML = `
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-time">${currentTime}</span>
                        <span class="message-sender">${currentUser}</span>
                    </div>
                    <div class="message-text">
                        <p>${formatMessage(message)}</p>
                    </div>
                </div>
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
            
            chatMessages.appendChild(messageElement);
            scrollToBottom();
        }
        
        // 添加AI助手消息到UI
        function addAssistantMessage(message, options = {}) {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message assistant-message';
            
            // 如果提供了临时ID，设置元素ID
            if (options.tempId) {
                messageElement.id = options.tempId;
            }
            
            const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            // 根据不同模式添加不同的图标和徽章
            let modeIcon = '';
            let modeBadge = '';
            
            if (options.webSearch) {
                modeIcon = '<i class="fas fa-globe me-1"></i>';
                modeBadge = '<span class="badge bg-info text-white ms-2">联网搜索</span>';
            } else if (options.dbQueried) {
                modeIcon = '<i class="fas fa-database me-1"></i>';
                modeBadge = '<span class="badge bg-success text-white ms-2">数据库查询</span>';
            } else if (currentMode === 'r1') {
                modeIcon = '<i class="fas fa-brain me-1"></i>';
                modeBadge = '<span class="badge bg-warning text-dark ms-2">深度思考</span>';
            }
            
            // 如果有模型信息，添加到徽章中
            if (options.modelUsed) {
                let modelName = "未知模型";
                let modelBadgeClass = "bg-secondary";
                
                // 检查是否发生了模型回退
                const modelFallback = options.model_fallback === true;
                
                if (options.modelUsed.includes('distill')) {
                    modelName = 'Distill-32B';
                    modelBadgeClass = "bg-secondary";
                } else if (options.modelUsed.includes('deepseek-r1-250120')) {
                    modelName = 'DeepSeek-R1';
                    modelBadgeClass = "bg-primary";
                } else {
                    // 提取模型名称的最后部分作为显示
                    const parts = options.modelUsed.split('-');
                    if (parts.length > 0) {
                        modelName = parts[parts.length - 2] || parts[parts.length - 1];
                    }
                }
                
                // 如果发生模型回退，在徽章中标明
                if (modelFallback && currentMode === 'r1') {
                    modeBadge = `<span class="badge bg-warning text-dark ms-2">深度思考</span>
                                <span class="badge bg-danger text-white ms-1">回退</span>`;
                }
                
                modeBadge += `<span class="badge ${modelBadgeClass} text-white ms-2">${modelName}</span>`;
            }
            
            messageElement.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-sender">${modeIcon}AI助手${modeBadge}</span>
                        <span class="message-time">${currentTime}</span>
                    </div>
                    <div class="message-text">
                        ${formatMessage(message)}
                    </div>
                </div>
            `;
            
            chatMessages.appendChild(messageElement);
            
            // 滚动到最新消息
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 格式化消息（将纯文本的换行符转换为HTML换行）
        function formatMessage(message) {
            // 检查是否已经是HTML格式（包含标签）
            if (/<[a-z][\s\S]*>/i.test(message)) {
                return message;
            }
            
            // 将普通文本中的换行符转换为<br>
            let formatted = message.replace(/\n/g, '<br>');
            
            // 识别数据库查询结果并设置样式
            if (formatted.includes('【') && formatted.includes('】')) {
                formatted = formatted.replace(/【([^】]+)】/g, '<strong class="text-primary">【$1】</strong>');
            }
            
            // 识别关键数据项并高亮
            const dataPatterns = [
                { pattern: /(批次号|LOT_ID|批次ID)[:：]\s*([^,，\s]+)/g, replacement: '$1: <span class="badge bg-info">$2</span>' },
                { pattern: /(设备ID|EQUIPMENT_ID|HANDLER_ID|TESTER_ID)[:：]\s*([^,，\s]+)/g, replacement: '$1: <span class="badge bg-warning">$2</span>' },
                { pattern: /(状态|STATUS|状态码)[:：]\s*([^,，\s]+)/g, replacement: '$1: <span class="badge bg-' + 
                   (text => {
                     if (text.includes('运行') || text.includes('RUN')) return 'success">$2</span>';
                     if (text.includes('闲置') || text.includes('IDLE')) return 'secondary">$2</span>';
                     if (text.includes('故障') || text.includes('ERROR')) return 'danger">$2</span>';
                     if (text.includes('停机') || text.includes('DOWN')) return 'warning">$2</span>';
                     return 'info">$2</span>';
                   })(formatted) },
                { pattern: /(产品ID|PROD_ID|产品型号|DEVICE)[:：]\s*([^,，\s]+)/g, replacement: '$1: <span class="badge bg-primary">$2</span>' }
            ];
            
            // 应用所有数据模式进行高亮替换
            dataPatterns.forEach(dp => {
                formatted = formatted.replace(dp.pattern, dp.replacement);
            });
            
            // 识别代码块
            if (formatted.includes('```')) {
                formatted = formatted.replace(/```(\w*)\n([\s\S]*?)```/g, function(match, language, code) {
                    return `<pre class="code-block"><code class="${language || ''}">${code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
                });
            }
            
            return formatted;
        }
        
        // 滚动到底部
        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 生成会话ID
        function generateSessionId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }
        
        // 保存当前会话
        function saveChatSession() {
            if (messageHistory.length < 2) return; // 至少有一问一答
            
            const firstUserMessage = messageHistory.find(msg => msg.role === 'user');
            const title = firstUserMessage ? firstUserMessage.content.substring(0, 30) : '新对话';
            
            const session = {
                id: currentSessionId,
                title: title + (title.length >= 30 ? '...' : ''),
                timestamp: new Date().toISOString(),
                messages: [...messageHistory]
            };
            
            // 检查是否已存在
            const existingIndex = chatSessions.findIndex(s => s.id === currentSessionId);
            if (existingIndex >= 0) {
                chatSessions[existingIndex] = session;
            } else {
                chatSessions.unshift(session); // 添加到开头
            }
            
            // 最多保存10个会话
            if (chatSessions.length > 10) {
                chatSessions = chatSessions.slice(0, 10);
            }
            
            // 保存到localStorage
            localStorage.setItem('aps_chat_sessions', JSON.stringify(chatSessions));
        }
        
        // 加载会话
        function loadChatSessions() {
            const savedSessions = localStorage.getItem('aps_chat_sessions');
            if (savedSessions) {
                chatSessions = JSON.parse(savedSessions);
                updateChatHistoryUI();
            }
        }
        
        // 更新聊天历史UI
        function updateChatHistoryUI() {
            chatHistoryList.innerHTML = '';
            
            if (chatSessions.length === 0) {
                chatHistoryList.innerHTML = `
                    <div class="list-group-item list-group-item-action text-center text-muted">
                        暂无对话历史
                    </div>
                `;
                return;
            }
            
            chatSessions.forEach(session => {
                const item = document.createElement('div');
                item.className = 'list-group-item list-group-item-action';
                if (session.id === currentSessionId) {
                    item.classList.add('active');
                }
                
                const date = new Date(session.timestamp);
                const formattedDate = `${date.getMonth()+1}/${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
                
                item.innerHTML = `
                    <div class="d-flex w-100 justify-content-between align-items-center">
                        <div class="chat-item-content" style="cursor: pointer; flex-grow: 1;">
                            <h6 class="mb-1 text-truncate">${session.title}</h6>
                            <small class="text-muted">${session.messages.length / 2}轮对话</small>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <small>${formattedDate}</small>
                            <button class="btn btn-sm btn-outline-danger delete-chat-btn mt-1" data-id="${session.id}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                chatHistoryList.appendChild(item);
                
                // 为整个项添加点击事件（除了删除按钮）
                const contentEl = item.querySelector('.chat-item-content');
                contentEl.addEventListener('click', () => loadSession(session.id));
                
                // 为删除按钮添加点击事件
                const deleteBtn = item.querySelector('.delete-chat-btn');
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    deleteTarget = session.id;
                    deleteConfirmMessage.textContent = '确定要删除这个对话吗？';
                    deleteConfirmModal.show();
                });
            });
        }
        
        // 删除特定会话
        function deleteSessionById(sessionId) {
            // 备份被删除的会话
            const sessionToDelete = chatSessions.find(s => s.id === sessionId);
            if (sessionToDelete) {
                deletedSessions = [sessionToDelete];
                
                // 从数组中移除
                chatSessions = chatSessions.filter(s => s.id !== sessionId);
                
                // 保存到localStorage
                localStorage.setItem('aps_chat_sessions', JSON.stringify(chatSessions));
                
                // 如果删除的是当前会话，创建新会话
                if (sessionId === currentSessionId) {
                    // 创建新会话
                    messageHistory = [];
                    currentSessionId = generateSessionId();
                    
                    // 清空聊天区域，只保留欢迎消息
                    chatMessages.innerHTML = '';
                    addAssistantMessage('您好！我是车规芯片终测智能调度平台的AI助手，可以帮助您解答关于芯片终测调度、订单管理、资源分配等方面的问题。请问有什么可以帮助您的吗？');
                }
                
                // 更新UI
                updateChatHistoryUI();
                
                // 显示撤销提示
                toastMessage.textContent = '对话已删除';
                undoToast.show();
            }
        }
        
        // 加载特定会话
        function loadSession(sessionId) {
            const session = chatSessions.find(s => s.id === sessionId);
            if (!session) return;
            
            // 如果当前是数据库查询模式，先切换回标准模式
            if (currentMode === 'database') {
                switchToDatabaseMode(false);
            }
            
            currentSessionId = sessionId;
            messageHistory = [...session.messages];
            currentMode = 'standard'; // 总是以标准模式加载会话
            updateModelIndicator();
            updateModelButtons();
            
            // 更新UI
            chatMessages.innerHTML = '';
            
            // 重建消息UI
            messageHistory.forEach(msg => {
                if (msg.role === 'user') {
                    addUserMessage(msg.content);
                } else if (msg.role === 'assistant') {
                    addAssistantMessage(msg.content);
                }
            });
            
            updateChatHistoryUI();
        }
        
        // 获取AI助手配置
        function fetchAIConfig() {
            fetch('/api/v2/system/ai-config')
                .then(response => response.json())
                .then(data => {
                    // 更新API状态指示器
                    const apiStatus = document.getElementById('api-status');
                    const modelName = document.getElementById('model-name');
                    const simulationBadge = document.getElementById('simulation-badge');
                    const checkR1Btn = document.getElementById('check-r1-btn');
                    
                    if (data.api_configured) {
                        apiStatus.textContent = 'API状态: 已连接';
                        apiStatus.className = 'text-success';
                        modelName.textContent = data.model || 'Deepseek Chat';
                        checkR1Btn.disabled = false;
                    } else {
                        apiStatus.textContent = 'API状态: 未连接';
                        apiStatus.className = 'text-danger';
                        modelName.textContent = '离线模式';
                        checkR1Btn.disabled = true;
                    }
                    
                    if (data.simulation_mode) {
                        simulationBadge.style.display = 'inline-block';
                    } else {
                        simulationBadge.style.display = 'none';
                    }
                    
                    // 不再自动检查R1模型状态，改为用户手动点击按钮检查
                    
                    // 设置R1检查按钮点击处理
                    checkR1Btn.addEventListener('click', function() {
                        checkR1Availability();
                    });
                    
                    // 更新功能列表
                    const featuresList = document.getElementById('features-list');
                    featuresList.innerHTML = '';
                    
                    // 添加Dify数据库功能
                    let difyFeatureAdded = false;
                    
                    // 检查是否有模式数据
                    if (data.modes && Array.isArray(data.modes)) {
                        data.modes.forEach(mode => {
                            if (mode.available) {
                                const li = document.createElement('li');
                                li.textContent = mode.name + (mode.description ? ' - ' + mode.description : '');
                                featuresList.appendChild(li);
                                
                                // 标记数据库查询功能已添加
                                if (mode.id === 'database') {
                                    difyFeatureAdded = true;
                                }
                            }
                        });
                        
                        // 设置按钮可用性
                        const standardMode = data.modes.find(m => m.id === 'standard');
                        const r1Mode = data.modes.find(m => m.id === 'r1');
                        const webSearchMode = data.modes.find(m => m.id === 'web_search');
                        const databaseMode = data.modes.find(m => m.id === 'database');
                        
                        standardBtn.disabled = standardMode ? !standardMode.available : false;
                        deepseekR1Btn.disabled = r1Mode ? !r1Mode.available : false;
                        webSearchBtn.disabled = webSearchMode ? !webSearchMode.available : false;
                        databaseBtn.disabled = databaseMode ? !databaseMode.available : false;
                    } else {
                        // 旧版API响应格式兼容
                        if (data.features && Array.isArray(data.features)) {
                            data.features.forEach(feature => {
                                const li = document.createElement('li');
                                li.textContent = feature;
                                featuresList.appendChild(li);
                                
                                // 标记数据库查询功能已添加
                                if (feature.toLowerCase().includes('数据库')) {
                                    difyFeatureAdded = true;
                                }
                            });
                        }
                    }
                    
                    // 如果没有数据库查询功能，添加一个
                    if (!difyFeatureAdded) {
                        const li = document.createElement('li');
                        li.textContent = 'Dify数据库查询 - 专业数据库信息检索';
                        featuresList.appendChild(li);
                    }
                })
                .catch(error => {
                    console.error('获取AI配置失败:', error);
                });
        }
        
        // 检查R1模型可用性
        function checkR1Availability() {
            // 显示检查中状态
            const r1StatusBadge = document.getElementById('r1-status-badge');
            r1StatusBadge.style.display = 'inline-block';
            r1StatusBadge.className = 'badge bg-secondary text-white mt-1 ms-2';
            r1StatusBadge.textContent = 'R1状态: 检查中...';
            
            // 发送一个简单的测试请求
            fetch('/api/ai/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: "这是一个测试R1模型可用性的请求，请回复'可用'一词。",
                    history: [],
                    mode: 'r1'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.model_fallback) {
                    // R1模型不可用
                    r1StatusBadge.className = 'badge bg-danger text-white mt-1 ms-2';
                    r1StatusBadge.textContent = 'R1状态: 不可用';
                    
                    // 禁用R1按钮
                    deepseekR1Btn.disabled = true;
                } else if (data.model_used && data.model_used.includes('deepseek-r1-250120')) {
                    // R1模型可用
                    r1StatusBadge.className = 'badge bg-success text-white mt-1 ms-2';
                    r1StatusBadge.textContent = 'R1状态: 可用';
                    
                    // 启用R1按钮
                    deepseekR1Btn.disabled = false;
                } else {
                    // 状态不明确
                    r1StatusBadge.className = 'badge bg-warning text-dark mt-1 ms-2';
                    r1StatusBadge.textContent = 'R1状态: 未知';
                }
            })
            .catch(error => {
                console.error('检查R1模型可用性出错:', error);
                r1StatusBadge.className = 'badge bg-danger text-white mt-1 ms-2';
                r1StatusBadge.textContent = 'R1状态: 检查失败';
            });
        }
        
        // 获取当前用户名
        const currentUser = '{{ current_user.username }}';
        
        // 初始化
        loadChatSessions();
    });
</script>
{% endblock %} 
# -*- coding: utf-8 -*-
"""
API v2 资源管理路由
提供统一的资源数据访问接口，支持向后兼容
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required
import logging
from datetime import datetime
from app.config.feature_flags import is_feature_enabled
from app.services.data_source_manager import DataSourceManager

logger = logging.getLogger(__name__)

# 创建蓝图（避免循环导入）
resources_bp = Blueprint('resources_v2', __name__, url_prefix='/api/v2/resources')

# 延迟初始化数据源管理器
data_source_manager = None

def get_data_source_manager():
    """获取数据源管理器单例"""
    global data_source_manager
    if data_source_manager is None:
        data_source_manager = DataSourceManager()
    return data_source_manager

@resources_bp.route('/data/<table_name>')
@login_required
def get_table_data_v2(table_name):
    """
    获取表格数据 (API v2)
    重用现有逻辑，提供统一响应格式
    """
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))
        
        # 高级筛选参数
        advanced_filters = request.args.get('advanced_filters')
        if advanced_filters:
            try:
                import json
                advanced_filters = json.loads(advanced_filters)
            except:
                advanced_filters = []
        else:
            advanced_filters = []
        
        # 使用数据源管理器获取数据
        result = get_data_source_manager().get_table_data(
            table_name=table_name,
            page=page,
            per_page=per_page,
            filters=advanced_filters
        )
        
        if result['success']:
            # API v2 统一响应格式
            return jsonify({
                'success': True,
                'data': result['data'],
                'columns': result.get('columns', []),
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': result.get('total', 0),
                    'pages': result.get('pages', 1),
                    'has_prev': page > 1,
                    'has_next': page < result.get('pages', 1)
                },
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'data_source': result.get('data_source', 'unknown'),
                    'timestamp': result.get('timestamp')
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '获取数据失败'),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name
                }
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 获取表格数据失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {
                'api_version': '2.0',
                'table_name': table_name
            }
        }), 500

@resources_bp.route('/columns/<table_name>')
@login_required  
def get_table_columns_v2(table_name):
    """获取表格列信息 (API v2)"""
    try:
        result = get_data_source_manager().get_table_columns(table_name)
        
        if result['success']:
            return jsonify({
                'success': True,
                'columns': result['columns'],
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'data_source': result.get('data_source', 'unknown')
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '获取列信息失败'),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name
                }
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 获取列信息失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {
                'api_version': '2.0',
                'table_name': table_name
            }
        }), 500

@resources_bp.route('/tables')
@login_required
def get_available_tables_v2():
    """获取可用表格列表 (API v2)"""
    try:
        result = get_data_source_manager().get_available_tables()
        
        if result['success']:
            return jsonify({
                'success': True,
                'tables': result['tables'],
                'meta': {
                    'api_version': '2.0',
                    'data_source': result.get('data_source', 'unknown'),
                    'count': len(result.get('tables', []))
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '获取表格列表失败'),
                'meta': {
                    'api_version': '2.0'
                }
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 获取表格列表失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {
                'api_version': '2.0'
            }
        }), 500

@resources_bp.route('/data/<table_name>/export')
@login_required
def export_table_data_v2(table_name):
    """导出表格数据 (API v2)"""
    try:
        from flask import send_file, redirect
        import os
        
        # 获取导出格式
        format_type = request.args.get('format', 'excel')
        
        # 获取筛选条件
        advanced_filters = request.args.get('advanced_filters')
        if advanced_filters:
            try:
                import json
                advanced_filters = json.loads(advanced_filters)
            except:
                advanced_filters = []
        else:
            advanced_filters = []
        
        result = get_data_source_manager().export_table_data(
            table_name=table_name,
            format_type=format_type,
            filters=advanced_filters
        )
        
        if result['success']:
            # 检查是否请求直接下载
            if request.args.get('export') == 'true':
                # 获取文件路径
                export_url = result.get('export_url', '')
                if export_url.startswith('/static/'):
                    # 构建文件的绝对路径
                    from flask import current_app
                    file_path = os.path.join(current_app.root_path, export_url.lstrip('/'))
                    
                    if os.path.exists(file_path):
                        return send_file(
                            file_path,
                            as_attachment=True,
                            download_name=result.get('filename', f'{table_name}_export.xlsx'),
                            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        )
                    else:
                        logger.error(f"导出文件不存在: {file_path}")
                        return jsonify({
                            'success': False,
                            'error': '导出文件生成失败'
                        }), 500
                else:
                    # 如果是外部URL，重定向到该URL
                    return redirect(export_url)
            else:
                # 返回JSON响应（用于AJAX调用）
                return jsonify({
                    'success': True,
                    'export_url': result.get('export_url'),
                    'filename': result.get('filename'),
                    'meta': {
                        'api_version': '2.0',
                        'table_name': table_name,
                        'format': format_type,
                        'records_count': result.get('records_count', 0)
                    }
                })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '导出数据失败'),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'format': format_type
                }
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 导出数据失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {
                'api_version': '2.0',
                'table_name': table_name
            }
        }), 500

@resources_bp.route('/data/<table_name>', methods=['POST'])
@login_required
def create_record_v2(table_name):
    """新增记录 (API v2)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供记录数据',
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 400
        
        result = get_data_source_manager().create_record(table_name, data)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '记录创建成功',
                'record_id': result.get('record_id'),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'operation': 'create'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '创建记录失败'),
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 创建记录失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {'api_version': '2.0', 'table_name': table_name}
        }), 500

@resources_bp.route('/data/<table_name>', methods=['PUT'])
@login_required
def update_record_v2(table_name):
    """更新记录 (API v2)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供记录数据',
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 400
        
        result = get_data_source_manager().update_record(table_name, data)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '记录更新成功',
                'affected_rows': result.get('affected_rows', 1),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'operation': 'update'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '更新记录失败'),
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 更新记录失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {'api_version': '2.0', 'table_name': table_name}
        }), 500

@resources_bp.route('/data/<table_name>/<record_id>', methods=['DELETE'])
@login_required
def delete_record_v2(table_name, record_id):
    """删除单条记录 (API v2)"""
    try:
        result = get_data_source_manager().delete_record(table_name, record_id)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '记录删除成功',
                'affected_rows': result.get('affected_rows', 1),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'operation': 'delete',
                    'record_id': record_id
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '删除记录失败'),
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 删除记录失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {'api_version': '2.0', 'table_name': table_name}
        }), 500

@resources_bp.route('/data/<table_name>/batch', methods=['DELETE'])
@login_required
def batch_delete_records_v2(table_name):
    """批量删除记录 (API v2)"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': '请提供要删除的记录ID列表',
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 400
        
        ids = data['ids']
        if not isinstance(ids, list) or len(ids) == 0:
            return jsonify({
                'success': False,
                'error': 'ID列表不能为空',
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 400
        
        result = get_data_source_manager().batch_delete_records(table_name, ids)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': f'成功删除 {result.get("deleted_count", len(ids))} 条记录',
                'deleted_count': result.get('deleted_count', len(ids)),
                'meta': {
                    'api_version': '2.0',
                    'table_name': table_name,
                    'operation': 'batch_delete'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '批量删除失败'),
                'meta': {'api_version': '2.0', 'table_name': table_name}
            }), 500
            
    except Exception as e:
        logger.error(f"API v2 批量删除记录失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'meta': {'api_version': '2.0', 'table_name': table_name}
        }), 500

@resources_bp.route('/health')
def resources_health_v2():
    """资源管理模块健康检查 (API v2)"""
    # 极度简化的health检查
    return jsonify({
        'status': 'ok',
        'service': 'resources_v2',
        'timestamp': datetime.now().isoformat()
    })

@resources_bp.route('/validate/<table_name>', methods=['GET'])
def validate_table_fields(table_name):
    """🔍 验证表字段映射一致性"""
    try:
        from app.services.data_source_manager import DataSourceManager
        
        # 获取数据源管理器
        manager = DataSourceManager()
        
        # 执行字段映射验证
        validation_result = manager.validate_field_mapping(table_name)
        
        if validation_result['success']:
            # 添加修复状态标识
            validation_result['fixed_mapping'] = True
            validation_result['fix_timestamp'] = datetime.now().isoformat()
            
            return jsonify({
                'success': True,
                'message': f'字段映射验证完成 - 表:{table_name}',
                'validation': validation_result
            })
        else:
            return jsonify({
                'success': False,
                'error': validation_result.get('error', '验证失败')
            }), 400
            
    except Exception as e:
        logger.error(f"字段映射验证失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'验证失败: {str(e)}'
        }), 500

@resources_bp.route('/validate/all', methods=['GET'])
def validate_all_tables():
    """🔍 验证所有表的字段映射一致性"""
    try:
        from app.services.data_source_manager import DataSourceManager
        
        # 需要验证的所有表
        tables_to_validate = [
            'ET_WAIT_LOT', 'WIP_LOT', 'EQP_STATUS', 'ET_UPH_EQP', 
            'ET_FT_TEST_SPEC', 'TCC_INV', 'CT', 'devicepriorityconfig', 
            'lotpriorityconfig', 'lotprioritydone', 'ET_RECIPE_FILE'
        ]
        
        manager = DataSourceManager()
        validation_results = {}
        
        total_tables = len(tables_to_validate)
        perfect_matches = 0
        partial_matches = 0
        poor_matches = 0
        
        for table_name in tables_to_validate:
            try:
                result = manager.validate_field_mapping(table_name)
                if result['success']:
                    validation_results[table_name] = result
                    
                    # 统计匹配情况
                    if result['status'] == 'perfect':
                        perfect_matches += 1
                    elif result['status'] == 'partial':
                        partial_matches += 1
                    else:
                        poor_matches += 1
                else:
                    validation_results[table_name] = {
                        'success': False,
                        'error': result.get('error', '验证失败')
                    }
                    poor_matches += 1
            except Exception as e:
                validation_results[table_name] = {
                    'success': False,
                    'error': str(e)
                }
                poor_matches += 1
        
        # 计算总体修复效果
        perfect_rate = (perfect_matches / total_tables) * 100
        partial_rate = (partial_matches / total_tables) * 100
        poor_rate = (poor_matches / total_tables) * 100
        
        summary = {
            'total_tables': total_tables,
            'perfect_matches': perfect_matches,
            'partial_matches': partial_matches,
            'poor_matches': poor_matches,
            'perfect_rate': round(perfect_rate, 2),
            'partial_rate': round(partial_rate, 2),
            'poor_rate': round(poor_rate, 2),
            'overall_status': 'excellent' if perfect_rate >= 90 else 'good' if perfect_rate >= 70 else 'needs_improvement',
            'fix_timestamp': datetime.now().isoformat()
        }
        
        return jsonify({
            'success': True,
            'message': f'字段映射修复验证完成 - 完美匹配率:{perfect_rate}%',
            'summary': summary,
            'details': validation_results
        })
        
    except Exception as e:
        logger.error(f"批量字段映射验证失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'批量验证失败: {str(e)}'
        }), 500 
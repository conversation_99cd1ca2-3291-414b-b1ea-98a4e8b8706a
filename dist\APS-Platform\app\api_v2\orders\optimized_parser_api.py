#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化解析器API v3.0
提供实时进度跟踪、重复检测和并发解析功能
"""

import logging
from flask import Blueprint, request, jsonify
from app.decorators import login_required
from app.services.optimized_excel_parser import OptimizedExcelParser, get_global_progress_tracker

logger = logging.getLogger(__name__)

bp = Blueprint('optimized_parser', __name__)

@bp.route('/parse-excel-optimized', methods=['POST'])
@login_required
def parse_excel_optimized():
    """
    优化版Excel解析 - 支持并发处理、实时进度跟踪和重复检测
    """
    try:
        # 获取请求参数
        data = request.get_json() or {}
        source_dir = data.get('source_dir', 'downloads')
        search_subdirs = data.get('search_subdirs', True)
        max_workers = data.get('max_workers', 4)  # 并发线程数
        file_paths = data.get('file_paths')  # 指定文件路径列表
        check_duplicates = data.get('check_duplicates', True)  # 是否检查重复
        
        # 创建优化解析器
        parser = OptimizedExcelParser(
            downloads_dir=source_dir,
            search_subdirs=search_subdirs,
            max_workers=max_workers
        )
        
        logger.info(f"🚀 启动优化Excel解析：目录={source_dir}, 并发数={max_workers}")
        
        # 执行批量解析
        result = parser.batch_parse_files_optimized(file_paths=file_paths)
        
        # 如果需要检查重复且发现重复项
        if check_duplicates and result.get('duplicates', {}).get('found'):
            duplicates = result['duplicates']['details']
            duplicate_check = parser.check_duplicates_interactive([
                type('DuplicateRecord', (), dup)() for dup in duplicates
            ])
            result['duplicate_check'] = duplicate_check
        
        return jsonify({
            'success': result['status'] == 'success',
            'task_id': result.get('task_id'),
            'data': result,
            'message': result.get('message', '优化解析完成')
        })
        
    except Exception as e:
        logger.error(f"❌ 优化Excel解析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '优化Excel解析失败'
        }), 500

@bp.route('/parse-progress/<task_id>', methods=['GET'])
@login_required
def get_parse_progress(task_id: str):
    """
    获取解析进度 - 实时进度跟踪
    """
    try:
        # 使用全局进度跟踪器
        tracker = get_global_progress_tracker()
        progress = tracker.get_progress(task_id)
        
        if progress is None:
            return jsonify({
                'success': False,
                'message': f'任务 {task_id} 不存在或已过期'
            }), 404
        
        progress_data = progress.to_dict()
        
        return jsonify({
            'success': True,
            'data': progress_data,
            'message': f'任务进度: {progress_data["progress_percentage"]:.1f}%'
        })
        
    except Exception as e:
        logger.error(f"❌ 获取解析进度失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取解析进度失败'
        }), 500

@bp.route('/duplicate-confirm', methods=['POST'])
@login_required
def confirm_duplicates():
    """
    重复项确认处理 - 人工确认重复项的处理方式
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '缺少请求数据'
            }), 400
        
        task_id = data.get('task_id')
        action = data.get('action')  # 'ignore', 'merge', 'replace', 'manual'
        duplicate_decisions = data.get('decisions', [])  # 每个重复项的处理决定
        
        if not task_id or not action:
            return jsonify({
                'success': False,
                'message': '缺少必要参数: task_id 和 action'
            }), 400
        
        # 处理重复项决定
        result = {
            'task_id': task_id,
            'action': action,
            'processed_duplicates': len(duplicate_decisions),
            'decisions': duplicate_decisions
        }
        
        if action == 'ignore':
            result['message'] = '✅ 已忽略所有重复项，保留原始数据'
        elif action == 'merge':
            result['message'] = '🔄 已合并重复项，去除重复数据'
        elif action == 'replace':
            result['message'] = '🔄 已替换重复项，使用最新数据'
        elif action == 'manual':
            result['message'] = f'👤 已手动处理 {len(duplicate_decisions)} 个重复项'
        
        logger.info(f"重复项确认完成: {result['message']}")
        
        return jsonify({
            'success': True,
            'data': result,
            'message': result['message']
        })
        
    except Exception as e:
        logger.error(f"❌ 重复项确认处理失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '重复项确认处理失败'
        }), 500

@bp.route('/performance-stats', methods=['GET'])
@login_required
def get_performance_stats():
    """
    获取解析性能统计
    """
    try:
        task_id = request.args.get('task_id')
        
        if not task_id:
            return jsonify({
                'success': False,
                'message': '缺少 task_id 参数'
            }), 400
        
        # 使用全局进度跟踪器
        tracker = get_global_progress_tracker()
        progress = tracker.get_progress(task_id)
        
        if progress is None:
            return jsonify({
                'success': False,
                'message': f'任务 {task_id} 不存在或已过期'
            }), 404
        
        # 计算性能指标
        elapsed = progress.elapsed_time
        processed = progress.processed_files
        total = progress.total_files
        
        stats = {
            'task_id': task_id,
            'elapsed_time': elapsed,
            'processed_files': processed,
            'total_files': total,
            'files_per_second': processed / elapsed if elapsed > 0 else 0,
            'estimated_remaining_time': (total - processed) * (elapsed / processed) if processed > 0 else 0,
            'progress_percentage': progress.progress_percentage,
            'status': progress.status,
            'total_records': progress.total_records,
            'duplicates_found': progress.duplicates_found
        }
        
        return jsonify({
            'success': True,
            'data': stats,
            'message': f'性能统计: {stats["files_per_second"]:.2f} 文件/秒'
        })
        
    except Exception as e:
        logger.error(f"❌ 获取性能统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取性能统计失败'
        }), 500

@bp.route('/cleanup-tasks', methods=['POST'])
@login_required
def cleanup_old_tasks():
    """
    清理过期任务
    """
    try:
        max_age_hours = request.json.get('max_age_hours', 24) if request.json else 24
        
        # 使用全局进度跟踪器
        tracker = get_global_progress_tracker()
        tracker.cleanup_old_tasks(max_age_hours)
        
        logger.info(f"清理了超过 {max_age_hours} 小时的过期任务")
        
        return jsonify({
            'success': True,
            'message': f'✅ 已清理超过 {max_age_hours} 小时的过期任务'
        })
        
    except Exception as e:
        logger.error(f"❌ 清理过期任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '清理过期任务失败'
        }), 500 
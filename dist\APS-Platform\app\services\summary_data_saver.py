#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单汇总数据保存服务
将解析的数据保存到对应的汇总表中
"""

import os
import logging
from typing import Dict, Any, List
from datetime import datetime

from app import db
from app.models.ft_order_summary import FtOrderSummary
from app.models.cp_order_summary import CpOrderSummary

logger = logging.getLogger(__name__)

class SummaryDataSaver:
    """订单汇总数据保存器"""
    
    def __init__(self):
        """初始化保存器"""
        self.saved_count = 0
        self.errors = []
    
    def save_ft_orders(self, parsed_data_list: List[Dict[str, Any]], source_file: str = None) -> Dict[str, Any]:
        """保存FT订单到汇总表"""
        
        saved_count = 0
        errors = []
        skipped_count = 0
        
        try:
            for record_data in parsed_data_list:
                try:
                    # 检查是否已存在
                    order_number = record_data.get('订单号', '')
                    if order_number:
                        existing = FtOrderSummary.query.filter_by(order_number=order_number).first()
                        if existing:
                            logger.info(f"FT订单 {order_number} 已存在，跳过")
                            skipped_count += 1
                            continue
                    
                    # 创建新记录
                    ft_order = FtOrderSummary.create_from_parsed_data(
                        record_data,
                        source_file=os.path.basename(source_file) if source_file else None
                    )
                    
                    if ft_order.order_number:  # 只保存有订单号的记录
                        db.session.add(ft_order)
                        saved_count += 1
                        logger.info(f"✅ 准备保存FT订单: {ft_order.order_number}")
                    
                except Exception as e:
                    error_msg = f"保存FT订单记录失败: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            # 批量提交
            if saved_count > 0:
                db.session.commit()
                logger.info(f"💾 成功保存 {saved_count} 条FT订单到汇总表")
            
            return {
                'success': True,
                'saved_count': saved_count,
                'skipped_count': skipped_count,
                'errors': errors,
                'message': f'FT订单保存完成: {saved_count}条新增, {skipped_count}条跳过'
            }
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"FT订单批量保存失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'saved_count': 0,
                'skipped_count': skipped_count,
                'errors': [error_msg],
                'message': error_msg
            }
    
    def save_cp_orders(self, parsed_data_list: List[Dict[str, Any]], source_file: str = None) -> Dict[str, Any]:
        """保存CP订单到汇总表"""
        
        saved_count = 0
        errors = []
        skipped_count = 0
        
        try:
            for record_data in parsed_data_list:
                try:
                    # 检查是否已存在
                    order_number = record_data.get('订单号', '')
                    if order_number:
                        existing = CpOrderSummary.query.filter_by(order_number=order_number).first()
                        if existing:
                            logger.info(f"CP订单 {order_number} 已存在，跳过")
                            skipped_count += 1
                            continue
                    
                    # 创建新记录
                    cp_order = CpOrderSummary.create_from_parsed_data(
                        record_data,
                        source_file=os.path.basename(source_file) if source_file else None
                    )
                    
                    if cp_order.order_number:  # 只保存有订单号的记录
                        db.session.add(cp_order)
                        saved_count += 1
                        logger.info(f"✅ 准备保存CP订单: {cp_order.order_number}")
                    
                except Exception as e:
                    error_msg = f"保存CP订单记录失败: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            # 批量提交
            if saved_count > 0:
                db.session.commit()
                logger.info(f"💾 成功保存 {saved_count} 条CP订单到汇总表")
            
            return {
                'success': True,
                'saved_count': saved_count,
                'skipped_count': skipped_count,
                'errors': errors,
                'message': f'CP订单保存完成: {saved_count}条新增, {skipped_count}条跳过'
            }
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"CP订单批量保存失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'saved_count': 0,
                'skipped_count': skipped_count,
                'errors': [error_msg],
                'message': error_msg
            }
    
    def save_orders_by_template(self, parse_result: Dict[str, Any], source_file: str = None) -> Dict[str, Any]:
        """根据模板类型保存订单到对应汇总表"""
        
        template_type = parse_result.get('template_type', 'standard_template')
        data_list = parse_result.get('data', [])
        
        if not data_list:
            return {
                'success': True,
                'saved_count': 0,
                'skipped_count': 0,
                'errors': [],
                'message': '没有数据需要保存'
            }
        
        # 根据模板类型选择保存方法
        if template_type == 'cp_template':
            return self.save_cp_orders(data_list, source_file)
        else:
            return self.save_ft_orders(data_list, source_file)
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """获取汇总表统计信息"""
        
        try:
            ft_count = FtOrderSummary.query.count()
            cp_count = CpOrderSummary.query.count()
            
            # 获取最新记录
            latest_ft = FtOrderSummary.query.order_by(FtOrderSummary.created_at.desc()).first()
            latest_cp = CpOrderSummary.query.order_by(CpOrderSummary.created_at.desc()).first()
            
            return {
                'ft_order_count': ft_count,
                'cp_order_count': cp_count,
                'total_count': ft_count + cp_count,
                'latest_ft_order': latest_ft.to_dict() if latest_ft else None,
                'latest_cp_order': latest_cp.to_dict() if latest_cp else None,
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取汇总统计失败: {e}")
            return {
                'ft_order_count': 0,
                'cp_order_count': 0,
                'total_count': 0,
                'latest_ft_order': None,
                'latest_cp_order': None,
                'last_updated': datetime.utcnow().isoformat(),
                'error': str(e)
            }

# 创建全局实例
summary_data_saver = SummaryDataSaver() 
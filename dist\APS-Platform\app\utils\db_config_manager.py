# -*- coding: utf-8 -*-
"""
数据库配置管理器
统一管理数据库连接配置，支持动态配置、缓存机制和多层回退

Author: AI Assistant
Date: 2025-01-26
"""

import logging
import time
from typing import Dict, Optional
from flask import current_app
import pymysql
from cryptography.fernet import Fernet
import base64
import os
import json

logger = logging.getLogger(__name__)

class DatabaseConfigManager:
    """数据库配置管理器
    
    功能：
    1. 从DatabaseConfig表动态读取配置
    2. 实现配置缓存机制（TTL=300秒）
    3. 支持密码解密
    4. 多层回退机制：缓存->数据库->默认值
    5. 连接测试和验证
    """
    
    def __init__(self):
        self._config_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        self._last_refresh = {}
        
    def get_mysql_config(self, config_name: Optional[str] = None) -> Dict:
        """获取MySQL配置
        
        Args:
            config_name: 配置名称，None表示获取默认配置
            
        Returns:
            Dict: 数据库连接配置
        """
        cache_key = f"mysql_{config_name or 'default'}"
        
        try:
            # 1. 尝试从缓存获取
            cached_config = self.get_cached_config(cache_key)
            if cached_config:
                logger.debug(f"从缓存获取数据库配置: {cache_key}")
                return cached_config
            
            # 2. 从数据库获取配置
            db_config = self._load_config_from_database(config_name)
            if db_config:
                # 更新缓存
                self._update_cache(cache_key, db_config)
                logger.info(f"从数据库加载配置成功: {cache_key}")
                return db_config
            
            # 3. 回退到默认配置
            logger.warning(f"数据库配置不可用，使用默认配置: {cache_key}")
            default_config = self._get_default_config()
            self._update_cache(cache_key, default_config)
            return default_config
            
        except Exception as e:
            logger.error(f"获取数据库配置失败: {e}")
            # 最终回退
            return self._get_default_config()
    
    def get_cached_config(self, cache_key: str) -> Optional[Dict]:
        """从缓存获取配置"""
        if cache_key not in self._config_cache:
            return None
            
        cache_data = self._config_cache[cache_key]
        cache_time = self._last_refresh.get(cache_key, 0)
        
        # 检查缓存是否过期
        if time.time() - cache_time > self._cache_ttl:
            logger.debug(f"缓存已过期: {cache_key}")
            return None
            
        return cache_data
    
    def _load_config_from_database(self, config_name: Optional[str] = None) -> Optional[Dict]:
        """从数据库加载配置"""
        try:
            # 使用默认配置连接到系统数据库
            system_conn = self._get_system_connection()
            cursor = system_conn.cursor()
            
            if config_name:
                # 获取指定名称的配置
                query = """
                    SELECT host, port, username, password_encrypted, database_name, 
                           charset, timezone, ssl_enabled, connection_timeout
                    FROM database_configs 
                    WHERE name = %s AND is_active = 1
                """
                cursor.execute(query, (config_name,))
            else:
                # 获取默认MySQL配置
                query = """
                    SELECT host, port, username, password_encrypted, database_name, 
                           charset, timezone, ssl_enabled, connection_timeout
                    FROM database_configs 
                    WHERE db_type = 'mysql' AND is_default = 1 AND is_active = 1
                    LIMIT 1
                """
                cursor.execute(query)
            
            row = cursor.fetchone()
            cursor.close()
            system_conn.close()
            
            if row:
                # 🔧 修复：密码处理逻辑优化
                password = row['password_encrypted'] if row['password_encrypted'] else ''
                
                # 如果密码看起来是加密的（长度>20且包含特殊字符），尝试解密
                if password and len(password) > 20 and any(c in password for c in ['=', '+', '/']):
                    try:
                        password = self._decrypt_password(password)
                    except Exception:
                        # 解密失败，使用默认密码
                        logger.warning("密码解密失败，使用默认密码")
                        password = 'WWWwww123!'
                elif not password:
                    # 密码为空，使用默认密码
                    password = 'WWWwww123!'
                
                config = {
                    'host': row['host'],
                    'port': int(row['port']),
                    'user': row['username'],
                    'password': password,
                    'database': row['database_name'],
                    'charset': row['charset'] or 'utf8mb4',
                    'timezone': row['timezone'] or 'Asia/Shanghai',
                    'ssl_enabled': bool(row['ssl_enabled']),
                    'connect_timeout': int(row['connection_timeout']) if row['connection_timeout'] else 30
                }
                
                # 🔧 测试配置是否可用
                try:
                    test_conn = pymysql.connect(
                        host=config['host'],
                        port=config['port'],
                        user=config['user'],
                        password=config['password'],
                        database=config['database'],
                        charset=config['charset'],
                        connect_timeout=5
                    )
                    test_conn.close()
                    logger.debug(f"数据库配置测试成功: {config['user']}@{config['host']}")
                    return config
                except Exception as e:
                    logger.warning(f"数据库配置测试失败: {e}，回退到默认配置")
                    return None
            
            return None
            
        except Exception as e:
            logger.error(f"从数据库加载配置失败: {e}")
            return None
    
    def _get_system_connection(self):
        """获取系统数据库连接（使用默认配置）"""
        default_config = self._get_default_config()
        
        return pymysql.connect(
            host=default_config['host'],
            port=default_config['port'],
            user=default_config['user'],
            password=default_config['password'],
            database='aps_system',  # 系统数据库
            charset=default_config['charset'],
            cursorclass=pymysql.cursors.DictCursor,
            connect_timeout=30
        )
    
    def _decrypt_password(self, encrypted_password: str) -> str:
        """解密数据库密码"""
        try:
            if not encrypted_password:
                return ''
            
            # 获取加密密钥（从环境变量或配置文件）
            encryption_key = os.environ.get('DB_ENCRYPTION_KEY')
            if not encryption_key:
                # 如果没有设置加密密钥，假设密码未加密
                logger.warning("未设置DB_ENCRYPTION_KEY，假设密码未加密")
                return encrypted_password
            
            fernet = Fernet(encryption_key.encode())
            decrypted = fernet.decrypt(encrypted_password.encode())
            return decrypted.decode()
            
        except Exception as e:
            logger.error(f"密码解密失败: {e}")
            # 假设密码未加密，直接返回
            return encrypted_password
    
    def _get_default_config(self) -> Dict:
        """获取默认数据库配置（从Flask配置）"""
        config = current_app.config if current_app else {}
        
        return {
            'host': config.get('MYSQL_HOST', '127.0.0.1'),
            'port': int(config.get('MYSQL_PORT', 3306)),
            'user': config.get('MYSQL_USER', 'root'),
            'password': config.get('MYSQL_PASSWORD', 'WWWwww123!'),
            'database': config.get('MYSQL_DATABASE', 'aps'),
            'charset': config.get('MYSQL_CHARSET', 'utf8mb4'),
            'timezone': 'Asia/Shanghai',
            'ssl_enabled': False,
            'connect_timeout': 30
        }
    
    def _update_cache(self, cache_key: str, config: Dict):
        """更新配置缓存"""
        self._config_cache[cache_key] = config.copy()
        self._last_refresh[cache_key] = time.time()
        logger.debug(f"配置缓存已更新: {cache_key}")
    
    def test_connection(self, config: Dict) -> bool:
        """测试数据库连接
        
        Args:
            config: 数据库配置字典
            
        Returns:
            bool: 连接是否成功
        """
        try:
            conn = pymysql.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                charset=config['charset'],
                connect_timeout=config.get('connect_timeout', 30)
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            
            logger.info(f"数据库连接测试成功: {config['host']}:{config['port']}/{config['database']}")
            return True
            
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def refresh_cache(self) -> None:
        """刷新所有配置缓存"""
        try:
            # 清空缓存
            self._config_cache.clear()
            self._last_refresh.clear()
            logger.info("配置缓存已刷新")
            
        except Exception as e:
            logger.error(f"刷新配置缓存失败: {e}")
    
    def get_all_configs(self) -> Dict[str, Dict]:
        """获取所有活跃的数据库配置"""
        try:
            system_conn = self._get_system_connection()
            cursor = system_conn.cursor()
            
            query = """
                SELECT name, host, port, username, password_encrypted, database_name, 
                       charset, timezone, ssl_enabled, connection_timeout, is_default
                FROM database_configs 
                WHERE is_active = 1
                ORDER BY is_default DESC, name
            """
            cursor.execute(query)
            rows = cursor.fetchall()
            cursor.close()
            system_conn.close()
            
            configs = {}
            for row in rows:
                password = self._decrypt_password(row['password_encrypted']) if row['password_encrypted'] else ''
                
                configs[row['name']] = {
                    'host': row['host'],
                    'port': int(row['port']),
                    'user': row['username'],
                    'password': password,
                    'database': row['database_name'],
                    'charset': row['charset'] or 'utf8mb4',
                    'timezone': row['timezone'] or 'Asia/Shanghai',
                    'ssl_enabled': bool(row['ssl_enabled']),
                    'connect_timeout': int(row['connection_timeout']) if row['connection_timeout'] else 30,
                    'is_default': bool(row['is_default'])
                }
            
            return configs
            
        except Exception as e:
            logger.error(f"获取所有配置失败: {e}")
            return {}


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> DatabaseConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = DatabaseConfigManager()
    return _config_manager 
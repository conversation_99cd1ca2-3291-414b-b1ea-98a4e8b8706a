-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `real_time_events`
--

DROP TABLE IF EXISTS `real_time_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `real_time_events` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `event_timestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
  `event_type` enum('equipment_status','lot_status','quality_alert','schedule_change','system_alert') NOT NULL,
  `event_source` varchar(100) DEFAULT NULL,
  `event_severity` enum('INFO','WARNING','ERROR','CRITICAL') DEFAULT 'INFO',
  `equipment_id` varchar(50) DEFAULT NULL,
  `lot_id` varchar(50) DEFAULT NULL,
  `event_data` json DEFAULT NULL,
  `event_message` text,
  `is_processed` tinyint(1) DEFAULT '0',
  `processed_by` varchar(100) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `processing_result` text,
  PRIMARY KEY (`id`),
  KEY `idx_event_time` (`event_timestamp`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_equipment` (`equipment_id`),
  KEY `idx_processed` (`is_processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时事件日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `real_time_events`
--

LOCK TABLES `real_time_events` WRITE;
/*!40000 ALTER TABLE `real_time_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `real_time_events` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-29 21:13:39

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端定时任务API接口 - 替代前端localStorage操作
提供完整的定时任务管理功能
"""

import logging
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user

logger = logging.getLogger(__name__)

# 创建蓝图
scheduled_tasks_api = Blueprint('scheduled_tasks_api', __name__)

@scheduled_tasks_api.route('/scheduled-tasks', methods=['GET'])
@login_required
def get_scheduled_tasks():
    """
    获取所有定时任务
    替代前端 loadScheduledTasks() 函数
    
    Returns:
        {
            "success": true,
            "tasks": [
                {
                    "id": "task_id",
                    "name": "任务名称",
                    "type": "once|daily|weekly|interval",
                    "status": "active|paused|completed|error",
                    "nextExecution": "2025-01-01T09:00:00",
                    "config": {...}
                }
            ]
        }
    """
    try:
        # 获取后端调度器服务
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动',
                'tasks': []
            })
        
        # 获取所有任务
        tasks = scheduler_service.get_all_tasks()
        
        # 转换为前端兼容格式
        formatted_tasks = []
        for task in tasks:
            config = task.get('config', {})
            formatted_task = {
                'id': task.get('id'),
                'name': task.get('name'),
                'type': task.get('type'),
                'status': task.get('status'),
                'nextExecution': task.get('next_run_time'),
                'createdAt': config.get('createdAt'),
                'lastExecuted': config.get('lastExecuted'),
                # 前端需要的配置字段
                'date': config.get('date'),
                'hour': config.get('hour'),
                'minute': config.get('minute'),
                'strategy': config.get('strategy'),
                'target': config.get('target'),
                'autoImport': config.get('autoImport', False),
                'emailNotification': config.get('emailNotification', False),
                'weekdays': config.get('weekdays', []),
                'intervalValue': config.get('intervalValue'),
                'intervalUnit': config.get('intervalUnit'),
                'endTime': config.get('endTime')
            }
            formatted_tasks.append(formatted_task)
        
        return jsonify({
            'success': True,
            'tasks': formatted_tasks
        })
        
    except Exception as e:
        logger.error(f"❌ 获取定时任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取任务失败: {str(e)}',
            'tasks': []
        }), 500

@scheduled_tasks_api.route('/scheduled-tasks', methods=['POST'])
@login_required
def create_scheduled_task():
    """
    创建定时任务
    替代前端 saveScheduledTask() 函数
    
    Request Body:
        {
            "name": "任务名称",
            "type": "once|daily|weekly|interval",
            "date": "2025-01-01",
            "hour": 9,
            "minute": 0,
            "strategy": "intelligent|deadline|product|value",
            "target": "balanced|makespan|efficiency",
            "autoImport": true,
            "emailNotification": true,
            "weekdays": ["monday", "tuesday"],
            "intervalValue": 30,
            "intervalUnit": "minutes|hours|days",
            "endTime": "2025-12-31"
        }
    
    Returns:
        {
            "success": true,
            "message": "任务创建成功",
            "task_id": "task_id",
            "nextExecution": "2025-01-01T09:00:00"
        }
    """
    try:
        data = request.get_json()
        
        # 验证必要字段
        required_fields = ['name', 'type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        # 获取后端调度器服务
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动'
            }), 500
        
        # 添加创建时间戳和用户信息
        data['createdAt'] = datetime.now().isoformat()
        data['lastExecuted'] = None
        data['created_by'] = current_user.username  # 🔧 新增：记录创建用户
        
        # 创建任务
        result = scheduler_service.create_scheduled_task(data)
        
        if result.get('success'):
            logger.info(f"✅ 用户 {current_user.username} 创建定时任务: {data.get('name')}")
            return jsonify({
                'success': True,
                'message': result.get('message'),
                'task_id': result.get('task_id'),
                'nextExecution': result.get('next_run_time')
            })
        else:
            return jsonify({
                'success': False,
                'message': result.get('message')
            }), 400
            
    except Exception as e:
        logger.error(f"❌ 创建定时任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建任务失败: {str(e)}'
        }), 500

@scheduled_tasks_api.route('/scheduled-tasks/<task_id>/pause', methods=['POST'])
@login_required
def pause_scheduled_task(task_id):
    """
    暂停定时任务
    替代前端 pauseTask() 函数
    """
    try:
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动'
            }), 500
        
        result = scheduler_service.pause_task(task_id)
        
        if result.get('success'):
            logger.info(f"✅ 用户 {current_user.username} 暂停任务: {task_id}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ 暂停任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'暂停任务失败: {str(e)}'
        }), 500

@scheduled_tasks_api.route('/scheduled-tasks/<task_id>/resume', methods=['POST'])
@login_required
def resume_scheduled_task(task_id):
    """
    恢复定时任务
    替代前端 resumeTask() 函数
    """
    try:
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动'
            }), 500
        
        result = scheduler_service.resume_task(task_id)
        
        if result.get('success'):
            logger.info(f"✅ 用户 {current_user.username} 恢复任务: {task_id}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ 恢复任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'恢复任务失败: {str(e)}'
        }), 500

@scheduled_tasks_api.route('/scheduled-tasks/<task_id>', methods=['DELETE'])
@login_required
def delete_scheduled_task(task_id):
    """
    删除定时任务
    替代前端 deleteTask() 函数
    """
    try:
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动'
            }), 500
        
        result = scheduler_service.delete_task(task_id)
        
        if result.get('success'):
            logger.info(f"✅ 用户 {current_user.username} 删除任务: {task_id}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ 删除任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除任务失败: {str(e)}'
        }), 500

@scheduled_tasks_api.route('/scheduled-tasks/status', methods=['GET'])
@login_required
def get_tasks_status():
    """
    获取定时任务状态概览
    替代前端 updateTaskStatus() 函数
    
    Returns:
        {
            "success": true,
            "status": {
                "activeTasks": 3,
                "nextTask": {
                    "name": "每日自动排产",
                    "nextExecution": "2025-01-01T09:00:00"
                },
                "statusText": "下次执行: 每日自动排产"
            }
        }
    """
    try:
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动',
                'status': {
                    'activeTasks': 0,
                    'statusText': '调度器服务未启动'
                }
            })
        
        # 获取所有任务
        tasks = scheduler_service.get_all_tasks()
        active_tasks = [task for task in tasks if task.get('status') == 'active']
        
        status_info = {
            'activeTasks': len(active_tasks),
            'totalTasks': len(tasks)
        }
        
        if not active_tasks:
            status_info['statusText'] = '当前无定时任务'
        else:
            # 找到下次执行的任务
            next_task = None
            earliest_time = None
            
            for task in active_tasks:
                next_run_time = task.get('next_run_time')
                if next_run_time:
                    try:
                        run_time = datetime.fromisoformat(next_run_time.replace('Z', '+00:00'))
                        if earliest_time is None or run_time < earliest_time:
                            earliest_time = run_time
                            next_task = task
                    except:
                        continue
            
            if next_task:
                status_info['nextTask'] = {
                    'name': next_task.get('name'),
                    'nextExecution': next_task.get('next_run_time')
                }
                status_info['statusText'] = f"下次执行: {next_task.get('name')}"
            else:
                status_info['statusText'] = '任务调度中...'
        
        return jsonify({
            'success': True,
            'status': status_info
        })
        
    except Exception as e:
        logger.error(f"❌ 获取任务状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}',
            'status': {
                'activeTasks': 0,
                'statusText': '状态获取失败'
            }
        }), 500

@scheduled_tasks_api.route('/scheduled-tasks/migrate-from-frontend', methods=['POST'])
@login_required
def migrate_from_frontend():
    """
    从前端localStorage迁移任务到后端
    一次性迁移接口，帮助用户从前端定时任务迁移到后端
    
    Request Body:
        {
            "tasks": [
                // 前端localStorage中的任务数据
            ]
        }
    
    Returns:
        {
            "success": true,
            "message": "迁移完成",
            "migrated": 3,
            "failed": 0,
            "details": [...]
        }
    """
    try:
        data = request.get_json()
        frontend_tasks = data.get('tasks', [])
        
        if not frontend_tasks:
            return jsonify({
                'success': False,
                'message': '没有需要迁移的任务'
            })
        
        scheduler_service = current_app.extensions.get('background_scheduler')
        if not scheduler_service:
            return jsonify({
                'success': False,
                'message': '调度器服务未启动'
            }), 500
        
        migrated_count = 0
        failed_count = 0
        migration_details = []
        
        for task in frontend_tasks:
            try:
                # 只迁移活跃状态的任务
                if task.get('status') != 'active':
                    continue
                
                # 转换前端任务格式为后端格式
                backend_task = {
                    'name': task.get('name'),
                    'type': task.get('type'),
                    'date': task.get('date'),
                    'hour': task.get('hour'),
                    'minute': task.get('minute'),
                    'strategy': task.get('strategy'),
                    'target': task.get('target'),
                    'autoImport': task.get('autoImport', False),
                    'emailNotification': task.get('emailNotification', False),
                    'weekdays': task.get('weekdays', []),
                    'intervalValue': task.get('intervalValue'),
                    'intervalUnit': task.get('intervalUnit'),
                    'endTime': task.get('endTime'),
                    'createdAt': task.get('createdAt'),
                    'lastExecuted': task.get('lastExecuted')
                }
                
                # 创建后端任务
                result = scheduler_service.create_scheduled_task(backend_task)
                
                if result.get('success'):
                    migrated_count += 1
                    migration_details.append({
                        'name': task.get('name'),
                        'status': 'success',
                        'task_id': result.get('task_id')
                    })
                else:
                    failed_count += 1
                    migration_details.append({
                        'name': task.get('name'),
                        'status': 'failed',
                        'error': result.get('message')
                    })
                    
            except Exception as e:
                failed_count += 1
                migration_details.append({
                    'name': task.get('name', '未知任务'),
                    'status': 'error',
                    'error': str(e)
                })
        
        logger.info(f"✅ 用户 {current_user.username} 迁移定时任务: 成功 {migrated_count}, 失败 {failed_count}")
        
        return jsonify({
            'success': True,
            'message': f'迁移完成: 成功 {migrated_count} 个，失败 {failed_count} 个',
            'migrated': migrated_count,
            'failed': failed_count,
            'details': migration_details
        })
        
    except Exception as e:
        logger.error(f"❌ 迁移任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'迁移失败: {str(e)}'
        }), 500

2025-06-28 20:57:34,465 - INFO - 🚀 开始运行算法验证...
2025-06-28 20:57:34,466 - INFO - 🔄 加载验证数据...
2025-06-28 20:57:34,466 - INFO - 加载待排产批次从: Excellist2025.06.05/排产验证/ET_WAIT_LOT.xlsx
2025-06-28 20:57:34,797 - INFO - ✅ 成功加载 409 条待排产批次
2025-06-28 20:57:34,803 - INFO - 加载期望结果从: Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx
2025-06-28 20:57:34,914 - INFO - ✅ 成功加载 409 条期望结果
2025-06-28 20:57:34,919 - INFO - ✅ 验证数据加载成功。
2025-06-28 20:57:34,919 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:57:34,920 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:57:35,730 - INFO - 系统模型包已禁用，请直接从app.models导入
2025-06-28 20:57:35,771 - ERROR - ❌ 运行排产算法时出错: RealSchedulingService.execute_real_scheduling() got an unexpected keyword argument 'lots_data'
Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.26\comprehensive_scheduling_algorithm_validation.py", line 85, in run_scheduling
    results = scheduler.execute_real_scheduling(lots_data=wait_lots)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RealSchedulingService.execute_real_scheduling() got an unexpected keyword argument 'lots_data'
2025-06-28 20:57:35,772 - ERROR - ❌ 排产算法未返回任何结果，验证终止。

"""
FT订单汇总模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Date
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class FTOrderSummary(Base):
    """FT订单汇总表模型"""
    __tablename__ = 'ft_summary'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    order_number = Column(String(100), comment='订单号')
    order_date = Column(Date, comment='下单日期')
    label_name = Column(String(200), comment='标签名称')
    circuit_name = Column(String(200), comment='电路名称')
    chip_name = Column(String(200), comment='芯片名称')
    delivery_date = Column(Date, comment='交期')
    classification_result = Column(String(50), comment='分类结果')
    lot_type1 = Column(String(100), comment='Lot类型')
    pin_dot_position = Column(String(50), comment='Pin点位置')
    wafer_size = Column(String(50), comment='晶圆尺寸')
    package_form = Column(String(100), comment='封装形式')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f'<FTOrderSummary {self.order_number}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'order_date': self.order_date.strftime('%Y-%m-%d') if self.order_date else '',
            'label_name': self.label_name,
            'circuit_name': self.circuit_name,
            'chip_name': self.chip_name,
            'delivery_date': self.delivery_date.strftime('%Y-%m-%d') if self.delivery_date else '',
            'classification_result': self.classification_result,
            'lot_type1': self.lot_type1,
            'pin_dot_position': self.pin_dot_position,
            'wafer_size': self.wafer_size,
            'package_form': self.package_form,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else '',
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else ''
        } 
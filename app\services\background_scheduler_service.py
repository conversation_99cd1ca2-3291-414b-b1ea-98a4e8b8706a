#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端定时任务服务 - 替代前端定时任务
基于APScheduler实现的智能排产定时任务管理
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import current_app
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class BackgroundSchedulerService:
    """后端定时任务服务"""
    
    def __init__(self, app=None):
        self.scheduler = None
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        self.app = app
        
        # 配置APScheduler
        jobstores = {
            'default': SQLAlchemyJobStore(url=app.config.get('SQLALCHEMY_DATABASE_URI'))
        }
        
        executors = {
            'default': ThreadPoolExecutor(20),
        }
        
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        # 创建调度器
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 注册应用上下文
        app.extensions['background_scheduler'] = self
    
    def start(self):
        """启动调度器"""
        if self.scheduler and not self.scheduler.running:
            try:
                self.scheduler.start()
                logger.info("✅ 后端定时任务调度器启动成功")
                return True
            except Exception as e:
                logger.error(f"❌ 后端定时任务调度器启动失败: {e}")
                return False
        elif self.scheduler and self.scheduler.running:
            logger.info("✅ 后端定时任务调度器已在运行")
            return True
        return False
    
    def shutdown(self):
        """关闭调度器"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("🔄 后端定时任务调度器已关闭")
    
    def create_scheduled_task(self, task_data: Dict) -> Dict:
        """
        创建定时任务
        
        Args:
            task_data: 任务配置数据
            {
                "name": "任务名称",
                "type": "once|daily|weekly|interval",
                "date": "2025-01-01",
                "hour": 9,
                "minute": 0,
                "strategy": "intelligent|deadline|product|value",
                "target": "balanced|makespan|efficiency",
                "autoImport": true,
                "emailNotification": true,
                "weekdays": ["monday", "tuesday"],  # weekly类型
                "intervalValue": 30,  # interval类型
                "intervalUnit": "minutes|hours|days"  # interval类型
            }
        
        Returns:
            Dict: 创建结果
        """
        try:
            # 确保调度器已启动
            if not self.scheduler:
                return {'success': False, 'message': '调度器未初始化'}
            
            if not self.scheduler.running:
                self.start()
            
            task_id = f"scheduled_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{task_data.get('name', 'task')}"
            
            # 根据任务类型创建触发器
            trigger = self._create_trigger(task_data)
            if not trigger:
                return {'success': False, 'message': '无效的任务配置'}
            
            # 添加任务到调度器 - 使用静态函数避免序列化问题
            job = self.scheduler.add_job(
                func=execute_scheduled_task_static,
                trigger=trigger,
                args=[task_data],
                id=task_id,
                name=task_data.get('name', '未命名任务'),
                replace_existing=True
            )
            
            # 保存任务配置到数据库
            self._save_task_config(task_id, task_data)
            
            logger.info(f"✅ 定时任务创建成功: {task_data.get('name')} (ID: {task_id})")
            
            # 安全地获取next_run_time
            next_run_time = None
            try:
                if hasattr(job, 'next_run_time') and job.next_run_time:
                    next_run_time = job.next_run_time.isoformat()
            except Exception as e:
                logger.warning(f"⚠️ 获取任务下次运行时间失败: {e}")
            
            return {
                'success': True,
                'message': '任务创建成功',
                'task_id': task_id,
                'next_run_time': next_run_time
            }
            
        except Exception as e:
            logger.error(f"❌ 创建定时任务失败: {e}")
            return {'success': False, 'message': f'创建失败: {str(e)}'}
    
    def _create_trigger(self, task_data: Dict):
        """根据任务类型创建触发器"""
        task_type = task_data.get('type')
        
        if task_type == 'once':
            # 一次性任务
            run_date = datetime.strptime(
                f"{task_data.get('date')} {task_data.get('hour', 9):02d}:{task_data.get('minute', 0):02d}",
                '%Y-%m-%d %H:%M'
            )
            return DateTrigger(run_date=run_date)
            
        elif task_type == 'daily':
            # 每日重复
            return CronTrigger(
                hour=task_data.get('hour', 9),
                minute=task_data.get('minute', 0)
            )
            
        elif task_type == 'weekly':
            # 每周重复
            weekdays = task_data.get('weekdays', [])
            if not weekdays:
                return None
            
            # 转换星期名称为cron格式
            weekday_map = {
                'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
                'friday': 4, 'saturday': 5, 'sunday': 6
            }
            cron_weekdays = [str(weekday_map[day]) for day in weekdays if day in weekday_map]
            
            return CronTrigger(
                day_of_week=','.join(cron_weekdays),
                hour=task_data.get('hour', 9),
                minute=task_data.get('minute', 0)
            )
            
        elif task_type == 'interval':
            # 间隔重复
            interval_value = task_data.get('intervalValue', 1)
            interval_unit = task_data.get('intervalUnit', 'hours')
            
            kwargs = {}
            if interval_unit == 'minutes':
                kwargs['minutes'] = interval_value
            elif interval_unit == 'hours':
                kwargs['hours'] = interval_value
            elif interval_unit == 'days':
                kwargs['days'] = interval_value
            
            return IntervalTrigger(**kwargs)
        
        return None
    
    def _execute_scheduled_task(self, task_data: Dict):
        """执行定时任务"""
        task_name = task_data.get('name', '未命名任务')
        
        try:
            with self.app.app_context():
                logger.info(f"🚀 开始执行定时任务: {task_name}")
                
                # 记录任务开始执行
                execution_id = self._log_task_execution(task_data, 'started')
                
                # 如果启用自动导入，先导入数据
                if task_data.get('autoImport', False):
                    logger.info("📥 执行自动数据导入...")
                    # TODO: 实现自动导入逻辑
                
                # 执行排产
                result = self._execute_scheduling(task_data)
                
                if result.get('success'):
                    logger.info(f"✅ 定时任务执行成功: {task_name}")
                    self._log_task_execution(task_data, 'completed', execution_id, result)
                    
                    # 更新任务的最后执行时间
                    self._update_last_executed(task_data.get('id'), datetime.now())
                    
                    # 发送邮件通知（如果启用）
                    if task_data.get('emailNotification', False):
                        self._send_notification(task_data, result)
                else:
                    logger.error(f"❌ 定时任务执行失败: {task_name} - {result.get('message')}")
                    self._log_task_execution(task_data, 'failed', execution_id, result)
                
        except Exception as e:
            logger.error(f"💥 定时任务执行异常: {task_name} - {e}")
            self._log_task_execution(task_data, 'error', None, {'error': str(e)})
    
    def _execute_scheduling(self, task_data: Dict) -> Dict:
        """执行排产逻辑"""
        try:
            import time
            start_time = time.time()
            
            from app.services.real_scheduling_service import RealSchedulingService
            
            # 创建排产服务实例
            rs = RealSchedulingService()
            
            # 🔧 修复：正确获取用户设置的排产策略
            strategy = task_data.get('strategy', 'intelligent')
            
            # 🔧 修复：根据策略选择正确的执行方法，传递创建任务的用户ID
            user_id = task_data.get('created_by')  # 获取创建任务的用户ID
            
            if strategy == 'deadline':
                # 交期优先策略
                scheduled_lots = rs.execute_real_scheduling('deadline', user_id=user_id)
            elif strategy == 'product':
                # 产品优先策略
                scheduled_lots = rs.execute_real_scheduling('product', user_id=user_id)
            elif strategy == 'value':
                # 产值优先策略
                scheduled_lots = rs.execute_real_scheduling('value', user_id=user_id)
            else:
                # 智能综合策略（默认）
                scheduled_lots = rs.execute_real_scheduling('intelligent', user_id=user_id)
            
            execution_time = time.time() - start_time
            
            # 🔧 修复：保存排产历史记录
            if scheduled_lots:
                self._save_schedule_history(task_data, scheduled_lots, strategy, execution_time)
            
            return {
                'success': True,
                'message': f'定时任务排产完成，使用策略：{strategy}，处理了 {len(scheduled_lots)} 个批次',
                'schedule': scheduled_lots,
                'metrics': {
                    'total_batches': len(scheduled_lots),
                    'strategy': strategy,
                    'execution_time': execution_time
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'排产执行失败: {str(e)}',
                'error': str(e)
            }
    
    def _save_task_config(self, task_id: str, task_data: Dict):
        """保存任务配置到数据库"""
        try:
            from app import db
            from sqlalchemy import text
            
            # 插入任务配置记录
            insert_sql = text("""
                INSERT INTO scheduled_tasks (
                    task_id, name, type, config_data, status, created_at, updated_at
                ) VALUES (
                    :task_id, :name, :type, :config_data, :status, :created_at, :updated_at
                )
            """)
            
            db.session.execute(insert_sql, {
                'task_id': task_id,
                'name': task_data.get('name', '未命名任务'),
                'type': task_data.get('type', 'once'),
                'config_data': json.dumps(task_data, ensure_ascii=False),
                'status': 'active',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            })
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ 保存任务配置失败: {e}")
    
    def _log_task_execution(self, task_data: Dict, status: str, execution_id: str = None, result: Dict = None) -> str:
        """记录任务执行日志"""
        try:
            from app import db
            from sqlalchemy import text
            import uuid
            
            if not execution_id:
                execution_id = str(uuid.uuid4())
            
            if status == 'started':
                # 记录开始执行
                insert_sql = text("""
                    INSERT INTO task_execution_logs (
                        execution_id, task_name, status, started_at, config_data
                    ) VALUES (
                        :execution_id, :task_name, :status, :started_at, :config_data
                    )
                """)
                
                db.session.execute(insert_sql, {
                    'execution_id': execution_id,
                    'task_name': task_data.get('name', '未命名任务'),
                    'status': status,
                    'started_at': datetime.now(),
                    'config_data': json.dumps(task_data, ensure_ascii=False)
                })
            else:
                # 更新执行结果
                update_sql = text("""
                    UPDATE task_execution_logs 
                    SET status = :status, finished_at = :finished_at, result_data = :result_data
                    WHERE execution_id = :execution_id
                """)
                
                db.session.execute(update_sql, {
                    'execution_id': execution_id,
                    'status': status,
                    'finished_at': datetime.now(),
                    'result_data': json.dumps(result, ensure_ascii=False) if result else None
                })
            
            db.session.commit()
            return execution_id
            
        except Exception as e:
            logger.error(f"❌ 记录任务执行日志失败: {e}")
            return execution_id or str(uuid.uuid4())
    
    def _save_schedule_history(self, task_data: Dict, scheduled_lots: List[Dict], strategy: str, execution_time: float):
        """保存排产历史记录"""
        try:
            from app.utils.db_helper import get_mysql_connection
            import json
            
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 保存到schedule_history表
            insert_sql = """
                INSERT INTO schedule_history (
                    timestamp, strategy, batch_count, execution_time, 
                    schedule_results, metrics, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            # 策略名称映射
            strategy_names = {
                'intelligent': '智能综合',
                'deadline': '交期优先', 
                'product': '产品优先',
                'value': '产值优先'
            }
            
            strategy_display = strategy_names.get(strategy, strategy)
            
            # 构建历史记录数据
            history_data = (
                datetime.now(),  # timestamp
                f"{strategy_display} (定时任务)",  # strategy
                len(scheduled_lots),  # batch_count
                execution_time,  # execution_time
                json.dumps(scheduled_lots, ensure_ascii=False),  # schedule_results
                json.dumps({
                    'strategy': strategy,
                    'task_name': task_data.get('name', '未命名任务'),
                    'execution_type': 'scheduled_task',
                    'total_batches': len(scheduled_lots),
                    'execution_time': execution_time
                }, ensure_ascii=False),  # metrics
                datetime.now()  # created_at
            )
            
            cursor.execute(insert_sql, history_data)
            conn.commit()
            
            logger.info(f"✅ 定时任务排产历史记录已保存: {strategy_display}, {len(scheduled_lots)} 个批次")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 保存定时任务排产历史记录失败: {e}")

    def _send_notification(self, task_data: Dict, result: Dict):
        """发送邮件通知"""
        try:
            # TODO: 实现邮件通知逻辑
            logger.info(f"📧 发送邮件通知: {task_data.get('name')} 执行完成")
        except Exception as e:
            logger.error(f"❌ 发送邮件通知失败: {e}")
    
    def get_all_tasks(self) -> List[Dict]:
        """获取所有定时任务"""
        try:
            tasks = []
            
            # 从调度器获取任务
            if self.scheduler:
                for job in self.scheduler.get_jobs():
                    try:
                        # 安全地获取next_run_time属性
                        next_run_time = None
                        if hasattr(job, 'next_run_time') and job.next_run_time:
                            next_run_time = job.next_run_time.isoformat()
                        
                        task_info = {
                            'id': job.id,
                            'name': getattr(job, 'name', job.id),
                            'next_run_time': next_run_time,
                            'trigger': str(job.trigger) if hasattr(job, 'trigger') else 'unknown',
                            'status': 'active'
                        }
                        tasks.append(task_info)
                    except Exception as job_error:
                        logger.warning(f"⚠️ 获取任务信息失败: {job.id} - {job_error}")
                        # 即使单个任务有问题，也添加基本信息
                        tasks.append({
                            'id': job.id,
                            'name': getattr(job, 'name', job.id),
                            'next_run_time': None,
                            'trigger': 'unknown',
                            'status': 'error'
                        })
            
            # 从数据库获取更详细的配置信息
            from app import db
            from sqlalchemy import text
            
            config_sql = text("SELECT task_id, name, type, config_data, status, last_executed FROM scheduled_tasks")
            config_results = db.session.execute(config_sql).fetchall()
            
            config_map = {row[0]: {
                'name': row[1],
                'type': row[2], 
                'config': json.loads(row[3]) if row[3] else {},
                'status': row[4],
                'lastExecuted': row[5].isoformat() if row[5] else None
            } for row in config_results}
            
            # 合并信息
            for task in tasks:
                if task['id'] in config_map:
                    task.update(config_map[task['id']])
            
            return tasks
            
        except Exception as e:
            logger.error(f"❌ 获取任务列表失败: {e}")
            return []
    
    def pause_task(self, task_id: str) -> Dict:
        """暂停任务"""
        try:
            self.scheduler.pause_job(task_id)
            self._update_task_status(task_id, 'paused')
            return {'success': True, 'message': '任务已暂停'}
        except Exception as e:
            return {'success': False, 'message': f'暂停失败: {str(e)}'}
    
    def resume_task(self, task_id: str) -> Dict:
        """恢复任务"""
        try:
            self.scheduler.resume_job(task_id)
            self._update_task_status(task_id, 'active')
            return {'success': True, 'message': '任务已恢复'}
        except Exception as e:
            return {'success': False, 'message': f'恢复失败: {str(e)}'}
    
    def delete_task(self, task_id: str) -> Dict:
        """删除任务"""
        try:
            self.scheduler.remove_job(task_id)
            self._delete_task_config(task_id)
            return {'success': True, 'message': '任务已删除'}
        except Exception as e:
            return {'success': False, 'message': f'删除失败: {str(e)}'}
    
    def _update_task_status(self, task_id: str, status: str):
        """更新任务状态"""
        try:
            from app import db
            from sqlalchemy import text
            
            update_sql = text("""
                UPDATE scheduled_tasks 
                SET status = :status, updated_at = :updated_at 
                WHERE task_id = :task_id
            """)
            
            db.session.execute(update_sql, {
                'task_id': task_id,
                'status': status,
                'updated_at': datetime.now()
            })
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: {e}")
    
    def _delete_task_config(self, task_id: str):
        """删除任务配置"""
        try:
            from app import db
            from sqlalchemy import text
            
            delete_sql = text("DELETE FROM scheduled_tasks WHERE task_id = :task_id")
            db.session.execute(delete_sql, {'task_id': task_id})
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ 删除任务配置失败: {e}")
    
    def _update_last_executed(self, task_id: str, execution_time: datetime):
        """更新任务的最后执行时间"""
        try:
            from app import db
            from sqlalchemy import text
            
            # 更新数据库中的最后执行时间
            update_sql = text("""
                UPDATE scheduled_tasks 
                SET last_executed = :last_executed, updated_at = :updated_at
                WHERE task_id = :task_id
            """)
            
            db.session.execute(update_sql, {
                'task_id': task_id,
                'last_executed': execution_time,
                'updated_at': datetime.now()
            })
            db.session.commit()
            
            logger.info(f"✅ 更新任务 {task_id} 最后执行时间: {execution_time}")
            
        except Exception as e:
            logger.error(f"❌ 更新任务最后执行时间失败: {e}")

def execute_scheduled_task_static(task_data: Dict):
    """
    静态执行函数 - 避免调度器序列化问题
    这个函数不依赖类实例，可以被APScheduler安全地序列化
    """
    task_name = task_data.get('name', '未命名任务')
    
    # 🔧 修复：获取Flask应用实例
    try:
        # 尝试从全局导入获取应用实例
        from app import app as flask_app
        app = flask_app
    except ImportError:
        try:
            # 如果上面失败，尝试创建新的应用实例
            from app import create_app
            app = create_app()
        except Exception as e:
            logger.error(f"❌ 无法获取Flask应用实例，定时任务 {task_name} 执行失败: {e}")
            return
    
    # 在应用上下文中执行任务
    with app.app_context():
        try:
            logger.info(f"🚀 开始执行定时任务: {task_name}")
            
            # 记录任务开始执行
            execution_id = _log_task_execution_static(task_data, 'started')
            
            # 如果启用自动导入，先导入数据
            if task_data.get('autoImport', False):
                logger.info("📥 执行自动数据导入...")
                # TODO: 实现自动导入逻辑
            
            # 执行排产
            result = _execute_scheduling_static(task_data)
            
            if result.get('success'):
                logger.info(f"✅ 定时任务执行成功: {task_name}")
                _log_task_execution_static(task_data, 'completed', execution_id, result)
                
                # 发送邮件通知（如果启用）
                if task_data.get('emailNotification', False):
                    _send_notification_static(task_data, result)
            else:
                logger.error(f"❌ 定时任务执行失败: {task_name} - {result.get('message')}")
                _log_task_execution_static(task_data, 'failed', execution_id, result)
            
        except Exception as e:
            logger.error(f"💥 定时任务执行异常: {task_name} - {e}")
            _log_task_execution_static(task_data, 'error', None, {'error': str(e)})

def _execute_scheduling_static(task_data: Dict) -> Dict:
    """静态排产执行函数"""
    try:
        import time
        start_time = time.time()
        
        from app.services.real_scheduling_service import RealSchedulingService
        
        # 创建排产服务实例
        rs = RealSchedulingService()
        
        # 🔧 修复：正确获取用户设置的排产策略
        strategy = task_data.get('strategy', 'intelligent')
        
        # 🔧 修复：根据策略选择正确的执行方法
        if strategy == 'deadline':
            # 交期优先策略
            scheduled_lots = rs.execute_real_scheduling('deadline')  # 使用交期优先策略
        elif strategy == 'product':
            # 产品优先策略
            scheduled_lots = rs.execute_real_scheduling('product')  # 使用产品优先策略
        elif strategy == 'value':
            # 产值优先策略
            scheduled_lots = rs.execute_real_scheduling('value')  # 使用产值优先策略
        else:
            # 智能综合策略（默认）
            scheduled_lots = rs.execute_real_scheduling('intelligent')  # 使用智能综合策略
        
        execution_time = time.time() - start_time
        
        # 🔧 修复：保存排产历史记录
        if scheduled_lots:
            _save_schedule_history_static(task_data, scheduled_lots, strategy, execution_time)
        
        return {
            'success': True,
            'message': f'定时任务排产完成，使用策略：{strategy}，处理了 {len(scheduled_lots)} 个批次',
            'schedule': scheduled_lots,
            'metrics': {
                'total_batches': len(scheduled_lots),
                'strategy': strategy,
                'execution_time': execution_time
            }
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'排产执行失败: {str(e)}',
            'error': str(e)
        }

def _log_task_execution_static(task_data: Dict, status: str, execution_id: str = None, result: Dict = None) -> str:
    """静态任务执行日志记录函数"""
    try:
        from app import db
        from sqlalchemy import text
        import uuid
        
        if not execution_id:
            execution_id = str(uuid.uuid4())
        
        if status == 'started':
            # 记录开始执行
            insert_sql = text("""
                INSERT INTO task_execution_logs (
                    execution_id, task_name, status, started_at, config_data
                ) VALUES (
                    :execution_id, :task_name, :status, :started_at, :config_data
                )
            """)
            
            db.session.execute(insert_sql, {
                'execution_id': execution_id,
                'task_name': task_data.get('name', '未命名任务'),
                'status': status,
                'started_at': datetime.now(),
                'config_data': json.dumps(task_data, ensure_ascii=False)
            })
        else:
            # 更新执行结果
            update_sql = text("""
                UPDATE task_execution_logs 
                SET status = :status, finished_at = :finished_at, result_data = :result_data
                WHERE execution_id = :execution_id
            """)
            
            db.session.execute(update_sql, {
                'execution_id': execution_id,
                'status': status,
                'finished_at': datetime.now(),
                'result_data': json.dumps(result, ensure_ascii=False) if result else None
            })
        
        db.session.commit()
        return execution_id
        
    except Exception as e:
        logger.error(f"❌ 记录任务执行日志失败: {e}")
        return execution_id or str(uuid.uuid4())

def _save_schedule_history_static(task_data: Dict, scheduled_lots: List[Dict], strategy: str, execution_time: float):
    """静态保存排产历史记录函数"""
    try:
        from app.utils.db_helper import get_mysql_connection
        import json
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # 保存到schedule_history表
        insert_sql = """
            INSERT INTO schedule_history (
                timestamp, strategy, batch_count, execution_time, 
                schedule_results, metrics, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        # 策略名称映射
        strategy_names = {
            'intelligent': '智能综合',
            'deadline': '交期优先', 
            'product': '产品优先',
            'value': '产值优先'
        }
        
        strategy_display = strategy_names.get(strategy, strategy)
        
        # 构建历史记录数据
        history_data = (
            datetime.now(),  # timestamp
            f"{strategy_display} (定时任务)",  # strategy
            len(scheduled_lots),  # batch_count
            execution_time,  # execution_time
            json.dumps(scheduled_lots, ensure_ascii=False),  # schedule_results
            json.dumps({
                'strategy': strategy,
                'task_name': task_data.get('name', '未命名任务'),
                'execution_type': 'scheduled_task',
                'total_batches': len(scheduled_lots),
                'execution_time': execution_time
            }, ensure_ascii=False),  # metrics
            datetime.now()  # created_at
        )
        
        cursor.execute(insert_sql, history_data)
        conn.commit()
        
        logger.info(f"✅ 定时任务排产历史记录已保存: {strategy_display}, {len(scheduled_lots)} 个批次")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"❌ 保存定时任务排产历史记录失败: {e}")

def _send_notification_static(task_data: Dict, result: Dict):
    """静态邮件通知函数"""
    try:
        # TODO: 实现邮件通知逻辑
        logger.info(f"📧 发送邮件通知: {task_data.get('name')} 执行完成")
    except Exception as e:
        logger.error(f"❌ 发送邮件通知失败: {e}")

# 全局实例
background_scheduler = BackgroundSchedulerService() 
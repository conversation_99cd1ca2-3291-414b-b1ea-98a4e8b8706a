-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `scheduling_config`
--

DROP TABLE IF EXISTS `scheduling_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scheduling_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户ID，NULL表示默认配置',
  `strategy_name` varchar(50) NOT NULL DEFAULT 'intelligent' COMMENT '排产策略名称',
  `tech_match_weight` decimal(5,2) DEFAULT '25.00' COMMENT '技术匹配度权重(%)',
  `load_balance_weight` decimal(5,2) DEFAULT '20.00' COMMENT '负载均衡权重(%)',
  `deadline_weight` decimal(5,2) DEFAULT '25.00' COMMENT '交期紧迫度权重(%)',
  `value_efficiency_weight` decimal(5,2) DEFAULT '20.00' COMMENT '产值效率权重(%)',
  `business_priority_weight` decimal(5,2) DEFAULT '10.00' COMMENT '业务优先级权重(%)',
  `minor_changeover_time` int DEFAULT '45' COMMENT '小改机时间(分钟)',
  `major_changeover_time` int DEFAULT '120' COMMENT '大改机时间(分钟)',
  `urgent_threshold` int DEFAULT '8' COMMENT '紧急阈值(小时)',
  `normal_threshold` int DEFAULT '24' COMMENT '正常阈值(小时)',
  `critical_threshold` int DEFAULT '72' COMMENT '关键阈值(小时)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_strategy_config` (`user_id`,`strategy_name`,`config_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='排产算法权重配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scheduling_config`
--

LOCK TABLES `scheduling_config` WRITE;
/*!40000 ALTER TABLE `scheduling_config` DISABLE KEYS */;
INSERT INTO `scheduling_config` VALUES (1,'default_config',NULL,'intelligent',25.00,20.00,25.00,20.00,10.00,45,120,8,24,72,1,'2025-06-29 03:04:04','2025-06-29 03:04:04'),(2,'default_intelligent_config',NULL,'intelligent',25.00,20.00,25.00,20.00,10.00,45,120,8,24,72,1,'2025-06-29 03:05:45','2025-06-29 03:05:45'),(3,'default_deadline_config',NULL,'deadline',15.00,10.00,50.00,15.00,10.00,45,120,8,24,72,1,'2025-06-29 03:05:45','2025-06-29 03:05:45'),(4,'default_product_config',NULL,'product',40.00,15.00,20.00,15.00,10.00,45,120,8,24,72,1,'2025-06-29 03:05:45','2025-06-29 03:05:45'),(5,'default_value_config',NULL,'value',15.00,15.00,20.00,40.00,10.00,45,120,8,24,72,1,'2025-06-29 03:05:45','2025-06-29 03:05:45'),(6,'admin_intelligent_config','admin','intelligent',50.00,20.00,10.00,10.00,10.00,45,120,8,24,72,1,'2025-06-28 19:59:58','2025-06-28 20:00:26'),(7,'admin_value_config','admin','value',15.00,15.00,20.00,40.00,10.00,45,120,8,24,72,1,'2025-06-28 20:04:00','2025-06-28 20:04:00');
/*!40000 ALTER TABLE `scheduling_config` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-29 21:13:39

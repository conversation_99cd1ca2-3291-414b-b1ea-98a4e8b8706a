#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产算法全面验证实验
验证 real_scheduling_service.py 的算法逻辑是否正确

实验设计：
1. 使用验证数据中的待排产批次作为输入
2. 通过我们的算法生成排产结果
3. 与验证数据中的已排产结果进行多维度对比
4. 分析差异原因，验证算法正确性
"""

import pandas as pd
import numpy as np
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/algorithm_validation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SchedulingAlgorithmValidator:
    """排产算法验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.validation_results = {}
        self.test_metrics = {}
        self.error_log = []
        
        # 验证数据路径
        self.wait_lot_file = 'Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx'
        self.done_lot_file = 'Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx'
        
        # 加载验证数据
        self.load_validation_data()
        
    def load_validation_data(self):
        """加载验证数据"""
        try:
            logger.info("🔄 加载验证数据...")
            
            # 加载待排产数据
            self.wait_lots_df = pd.read_excel(self.wait_lot_file)
            logger.info(f"✅ 加载待排产数据: {len(self.wait_lots_df)} 条记录")
            
            # 加载已排产数据（期望结果）
            self.expected_results_df = pd.read_excel(self.done_lot_file)
            logger.info(f"✅ 加载期望结果: {len(self.expected_results_df)} 条记录")
            
            # 数据预处理
            self._preprocess_validation_data()
            
        except Exception as e:
            logger.error(f"❌ 加载验证数据失败: {e}")
            raise
    
    def _preprocess_validation_data(self):
        """预处理验证数据"""
        logger.info("🔄 预处理验证数据...")
        
        # 转换为字典格式（与算法接口兼容）
        self.wait_lots = self.wait_lots_df.to_dict('records')
        self.expected_results = self.expected_results_df.to_dict('records')
        
        # 创建期望结果的索引
        self.expected_index = {}
        for result in self.expected_results:
            key = (result.get('LOT_ID'), result.get('DEVICE'), result.get('STAGE'))
            self.expected_index[key] = result
        
        logger.info(f"✅ 预处理完成，创建期望结果索引: {len(self.expected_index)} 个键值对")
    
    def run_algorithm_test(self) -> List[Dict]:
        """运行算法测试"""
        logger.info("🚀 开始运行算法测试...")
        
        try:
            # 导入排产服务
            from app.services.real_scheduling_service import RealSchedulingService
            
            # 创建服务实例
            scheduling_service = RealSchedulingService()
            
            # 模拟数据源管理器，直接提供验证数据
            class MockDataManager:
                def __init__(self, wait_lots):
                    self.wait_lots = wait_lots
                
                def get_wait_lot_data(self):
                    return self.wait_lots, "validation_data"
            
            # 替换数据管理器
            scheduling_service.data_manager = MockDataManager(self.wait_lots)
            
            # 执行排产算法
            start_time = time.time()
            algorithm_results = scheduling_service.execute_real_scheduling('intelligent')
            execution_time = time.time() - start_time
            
            logger.info(f"✅ 算法执行完成，耗时: {execution_time:.2f}秒")
            logger.info(f"📊 生成排产结果: {len(algorithm_results)} 条记录")
            
            # 保存执行指标
            self.test_metrics['execution_time'] = execution_time
            self.test_metrics['input_count'] = len(self.wait_lots)
            self.test_metrics['output_count'] = len(algorithm_results)
            self.test_metrics['algorithm_version'] = 'real_scheduling_service_v1.0'
            
            return algorithm_results
            
        except Exception as e:
            logger.error(f"❌ 算法执行失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def compare_results(self, algorithm_results: List[Dict]) -> Dict[str, Any]:
        """对比算法结果与期望结果"""
        logger.info("🔍 开始结果对比分析...")
        
        comparison_report = {
            'total_matches': 0,
            'total_mismatches': 0,
            'field_comparisons': {},
            'missing_lots': [],
            'extra_lots': [],
            'detailed_differences': [],
            'statistics': {}
        }
        
        # 创建算法结果索引
        algorithm_index = {}
        for result in algorithm_results:
            key = (result.get('LOT_ID'), result.get('DEVICE'), result.get('STAGE'))
            algorithm_index[key] = result
        
        # 1. 检查记录完整性
        expected_keys = set(self.expected_index.keys())
        algorithm_keys = set(algorithm_index.keys())
        
        missing_keys = expected_keys - algorithm_keys
        extra_keys = algorithm_keys - expected_keys
        common_keys = expected_keys & algorithm_keys
        
        comparison_report['missing_lots'] = list(missing_keys)
        comparison_report['extra_lots'] = list(extra_keys)
        
        logger.info(f"📈 记录完整性分析:")
        logger.info(f"  期望记录数: {len(expected_keys)}")
        logger.info(f"  算法输出数: {len(algorithm_keys)}")
        logger.info(f"  匹配记录数: {len(common_keys)}")
        logger.info(f"  缺失记录数: {len(missing_keys)}")
        logger.info(f"  多余记录数: {len(extra_keys)}")
        
        # 2. 详细字段对比
        field_stats = defaultdict(lambda: {'matches': 0, 'mismatches': 0, 'differences': []})
        
        for key in common_keys:
            expected = self.expected_index[key]
            actual = algorithm_index[key]
            
            lot_id = expected.get('LOT_ID')
            device = expected.get('DEVICE')
            stage = expected.get('STAGE')
            
            # 比较关键字段
            key_fields = ['LOT_ID', 'DEVICE', 'STAGE', 'HANDLER_ID', 'PRIORITY', 'GOOD_QTY']
            
            lot_differences = {}
            for field in key_fields:
                expected_value = expected.get(field)
                actual_value = actual.get(field)
                
                if expected_value == actual_value:
                    field_stats[field]['matches'] += 1
                else:
                    field_stats[field]['mismatches'] += 1
                    field_stats[field]['differences'].append({
                        'lot_id': lot_id,
                        'device': device,
                        'stage': stage,
                        'expected': expected_value,
                        'actual': actual_value
                    })
                    lot_differences[field] = {
                        'expected': expected_value,
                        'actual': actual_value
                    }
            
            if lot_differences:
                comparison_report['detailed_differences'].append({
                    'lot_id': lot_id,
                    'device': device,
                    'stage': stage,
                    'differences': lot_differences
                })
                comparison_report['total_mismatches'] += 1
            else:
                comparison_report['total_matches'] += 1
        
        comparison_report['field_comparisons'] = dict(field_stats)
        
        # 3. 统计分析
        comparison_report['statistics'] = {
            'completeness_rate': len(common_keys) / len(expected_keys) if expected_keys else 0,
            'accuracy_rate': comparison_report['total_matches'] / len(common_keys) if common_keys else 0,
            'field_accuracy': {
                field: stats['matches'] / (stats['matches'] + stats['mismatches']) 
                if (stats['matches'] + stats['mismatches']) > 0 else 0
                for field, stats in field_stats.items()
            }
        }
        
        return comparison_report
    
    def analyze_handler_selection_logic(self, algorithm_results: List[Dict]) -> Dict[str, Any]:
        """分析HANDLER_ID选择逻辑"""
        logger.info("🔍 分析HANDLER_ID选择逻辑...")
        
        analysis = {
            'device_stage_mappings': {},
            'handler_distribution': {},
            'selection_patterns': {},
            'load_balance_analysis': {}
        }
        
        # 分析DEVICE+STAGE到HANDLER_ID的映射
        device_stage_handlers = defaultdict(set)
        handler_counts = defaultdict(int)
        
        for result in algorithm_results:
            device = result.get('DEVICE')
            stage = result.get('STAGE')
            handler_id = result.get('HANDLER_ID')
            
            key = f"{device}+{stage}"
            device_stage_handlers[key].add(handler_id)
            handler_counts[handler_id] += 1
        
        # 统计映射复杂度
        one_to_one_mappings = sum(1 for handlers in device_stage_handlers.values() if len(handlers) == 1)
        one_to_many_mappings = sum(1 for handlers in device_stage_handlers.values() if len(handlers) > 1)
        
        analysis['device_stage_mappings'] = {
            'total_combinations': len(device_stage_handlers),
            'one_to_one': one_to_one_mappings,
            'one_to_many': one_to_many_mappings,
            'complexity_ratio': one_to_many_mappings / len(device_stage_handlers) if device_stage_handlers else 0
        }
        
        analysis['handler_distribution'] = dict(handler_counts)
        
        # 分析负载均衡效果
        if handler_counts:
            loads = list(handler_counts.values())
            analysis['load_balance_analysis'] = {
                'min_load': min(loads),
                'max_load': max(loads),
                'avg_load': np.mean(loads),
                'std_load': np.std(loads),
                'load_variance': np.var(loads),
                'balance_coefficient': np.std(loads) / np.mean(loads) if np.mean(loads) > 0 else 0
            }
        
        return analysis
    
    def validate_business_rules(self, algorithm_results: List[Dict]) -> Dict[str, Any]:
        """验证业务规则遵循情况"""
        logger.info("🔍 验证业务规则遵循情况...")
        
        validation = {
            'unique_lot_stage_assignment': True,
            'priority_sequence_validation': True,
            'field_completeness': {},
            'rule_violations': []
        }
        
        # 1. 验证LOT_ID+STAGE唯一性
        lot_stage_combinations = []
        for result in algorithm_results:
            lot_id = result.get('LOT_ID')
            stage = result.get('STAGE')
            handler_id = result.get('HANDLER_ID')
            
            combination = (lot_id, stage)
            if combination in lot_stage_combinations:
                validation['unique_lot_stage_assignment'] = False
                validation['rule_violations'].append({
                    'rule': 'unique_lot_stage_assignment',
                    'violation': f"LOT_ID {lot_id} + STAGE {stage} 出现多次"
                })
            lot_stage_combinations.append(combination)
        
        # 2. 验证PRIORITY序列的合理性
        handler_priorities = defaultdict(list)
        for result in algorithm_results:
            handler_id = result.get('HANDLER_ID')
            priority = result.get('PRIORITY', result.get('EXECUTION_PRIORITY'))
            
            if priority is not None:
                handler_priorities[handler_id].append(priority)
        
        # 检查每个HANDLER的PRIORITY是否连续
        for handler_id, priorities in handler_priorities.items():
            priorities.sort()
            expected_sequence = list(range(1, len(priorities) + 1))
            
            if priorities != expected_sequence:
                validation['priority_sequence_validation'] = False
                validation['rule_violations'].append({
                    'rule': 'priority_sequence_validation',
                    'violation': f"HANDLER {handler_id} 的PRIORITY序列不连续: {priorities}"
                })
        
        # 3. 验证字段完整性
        required_fields = ['LOT_ID', 'DEVICE', 'STAGE', 'HANDLER_ID', 'GOOD_QTY']
        field_completeness = {}
        
        for field in required_fields:
            complete_count = sum(1 for result in algorithm_results if result.get(field) is not None and result.get(field) != '')
            field_completeness[field] = {
                'complete_count': complete_count,
                'total_count': len(algorithm_results),
                'completeness_rate': complete_count / len(algorithm_results) if algorithm_results else 0
            }
        
        validation['field_completeness'] = field_completeness
        
        return validation
    
    def generate_comprehensive_report(self, 
                                    algorithm_results: List[Dict], 
                                    comparison_report: Dict[str, Any],
                                    handler_analysis: Dict[str, Any],
                                    business_validation: Dict[str, Any]) -> str:
        """生成综合验证报告"""
        
        report_lines = []
        report_lines.append("=" * 100)
        report_lines.append("排产算法全面验证报告")
        report_lines.append("=" * 100)
        report_lines.append(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"算法版本: {self.test_metrics.get('algorithm_version', 'unknown')}")
        report_lines.append("")
        
        # 1. 执行性能指标
        report_lines.append("=== 1. 执行性能指标 ===")
        report_lines.append(f"输入批次数量: {self.test_metrics['input_count']}")
        report_lines.append(f"输出结果数量: {self.test_metrics['output_count']}")
        report_lines.append(f"执行时间: {self.test_metrics['execution_time']:.2f} 秒")
        report_lines.append(f"处理速度: {self.test_metrics['input_count']/self.test_metrics['execution_time']:.1f} 批次/秒")
        report_lines.append("")
        
        # 2. 数据完整性验证
        report_lines.append("=== 2. 数据完整性验证 ===")
        stats = comparison_report['statistics']
        report_lines.append(f"记录完整率: {stats['completeness_rate']:.2%}")
        report_lines.append(f"结果准确率: {stats['accuracy_rate']:.2%}")
        report_lines.append(f"缺失记录数: {len(comparison_report['missing_lots'])}")
        report_lines.append(f"多余记录数: {len(comparison_report['extra_lots'])}")
        report_lines.append("")
        
        # 3. 字段准确性分析
        report_lines.append("=== 3. 字段准确性分析 ===")
        for field, accuracy in stats['field_accuracy'].items():
            report_lines.append(f"{field:<15}: {accuracy:.2%}")
        report_lines.append("")
        
        # 4. HANDLER选择逻辑分析
        report_lines.append("=== 4. HANDLER选择逻辑分析 ===")
        mappings = handler_analysis['device_stage_mappings']
        report_lines.append(f"设备阶段组合数: {mappings['total_combinations']}")
        report_lines.append(f"一对一映射: {mappings['one_to_one']} ({mappings['one_to_one']/mappings['total_combinations']:.1%})")
        report_lines.append(f"一对多映射: {mappings['one_to_many']} ({mappings['one_to_many']/mappings['total_combinations']:.1%})")
        report_lines.append(f"映射复杂度: {mappings['complexity_ratio']:.2%}")
        report_lines.append("")
        
        # 5. 负载均衡分析
        if 'load_balance_analysis' in handler_analysis:
            load_analysis = handler_analysis['load_balance_analysis']
            report_lines.append("=== 5. 负载均衡分析 ===")
            report_lines.append(f"最小负载: {load_analysis['min_load']}")
            report_lines.append(f"最大负载: {load_analysis['max_load']}")
            report_lines.append(f"平均负载: {load_analysis['avg_load']:.1f}")
            report_lines.append(f"负载标准差: {load_analysis['std_load']:.1f}")
            report_lines.append(f"负载均衡系数: {load_analysis['balance_coefficient']:.3f} (越小越均衡)")
            report_lines.append("")
        
        # 6. 业务规则验证
        report_lines.append("=== 6. 业务规则验证 ===")
        report_lines.append(f"LOT+STAGE唯一性: {'✅ 通过' if business_validation['unique_lot_stage_assignment'] else '❌ 失败'}")
        report_lines.append(f"PRIORITY序列正确性: {'✅ 通过' if business_validation['priority_sequence_validation'] else '❌ 失败'}")
        
        if business_validation['rule_violations']:
            report_lines.append("\n业务规则违反详情:")
            for violation in business_validation['rule_violations'][:5]:  # 只显示前5个
                report_lines.append(f"  - {violation['rule']}: {violation['violation']}")
        report_lines.append("")
        
        # 7. 字段完整性检查
        report_lines.append("=== 7. 字段完整性检查 ===")
        for field, completeness in business_validation['field_completeness'].items():
            rate = completeness['completeness_rate']
            status = "✅" if rate >= 0.95 else "⚠️" if rate >= 0.8 else "❌"
            report_lines.append(f"{field:<15}: {rate:.2%} {status}")
        report_lines.append("")
        
        # 8. 主要差异分析
        if comparison_report['detailed_differences']:
            report_lines.append("=== 8. 主要差异分析 (前10个) ===")
            for diff in comparison_report['detailed_differences'][:10]:
                report_lines.append(f"LOT_ID: {diff['lot_id']}, DEVICE: {diff['device']}, STAGE: {diff['stage']}")
                for field, values in diff['differences'].items():
                    report_lines.append(f"  {field}: 期望={values['expected']}, 实际={values['actual']}")
                report_lines.append("")
        
        # 9. 总体评估
        report_lines.append("=== 9. 总体评估 ===")
        
        # 计算综合得分
        completeness_score = stats['completeness_rate'] * 30  # 30%权重
        accuracy_score = stats['accuracy_rate'] * 40  # 40%权重
        business_rules_score = (business_validation['unique_lot_stage_assignment'] + 
                               business_validation['priority_sequence_validation']) * 15  # 30%权重，每个15%
        
        total_score = completeness_score + accuracy_score + business_rules_score
        
        report_lines.append(f"数据完整性得分: {completeness_score:.1f}/30")
        report_lines.append(f"结果准确性得分: {accuracy_score:.1f}/40")
        report_lines.append(f"业务规则得分: {business_rules_score:.1f}/30")
        report_lines.append(f"综合得分: {total_score:.1f}/100")
        
        if total_score >= 90:
            report_lines.append("🎉 算法验证结果: 优秀")
        elif total_score >= 80:
            report_lines.append("✅ 算法验证结果: 良好")
        elif total_score >= 70:
            report_lines.append("⚠️ 算法验证结果: 一般，需要优化")
        else:
            report_lines.append("❌ 算法验证结果: 不合格，需要重大改进")
        
        report_lines.append("")
        report_lines.append("=" * 100)
        
        return "\n".join(report_lines)
    
    def save_detailed_results(self, 
                            algorithm_results: List[Dict], 
                            comparison_report: Dict[str, Any],
                            report_content: str):
        """保存详细验证结果"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存算法输出结果
        results_df = pd.DataFrame(algorithm_results)
        results_file = f'validation_results_{timestamp}.xlsx'
        results_df.to_excel(results_file, index=False)
        logger.info(f"💾 算法结果已保存: {results_file}")
        
        # 保存对比分析结果
        comparison_file = f'comparison_analysis_{timestamp}.json'
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_report, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"💾 对比分析已保存: {comparison_file}")
        
        # 保存验证报告
        report_file = f'validation_report_{timestamp}.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        logger.info(f"💾 验证报告已保存: {report_file}")
    
    def run_comprehensive_validation(self):
        """运行全面验证"""
        logger.info("🚀 开始运行全面验证实验...")
        
        try:
            # 1. 运行算法测试
            algorithm_results = self.run_algorithm_test()
            
            if not algorithm_results:
                logger.error("❌ 算法执行失败，无法继续验证")
                return
            
            # 2. 结果对比分析
            comparison_report = self.compare_results(algorithm_results)
            
            # 3. HANDLER选择逻辑分析
            handler_analysis = self.analyze_handler_selection_logic(algorithm_results)
            
            # 4. 业务规则验证
            business_validation = self.validate_business_rules(algorithm_results)
            
            # 5. 生成综合报告
            report_content = self.generate_comprehensive_report(
                algorithm_results, comparison_report, handler_analysis, business_validation
            )
            
            # 6. 输出报告
            print(report_content)
            
            # 7. 保存详细结果
            self.save_detailed_results(algorithm_results, comparison_report, report_content)
            
            logger.info("🎉 全面验证实验完成！")
            
        except Exception as e:
            logger.error(f"❌ 验证实验失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("🚀 启动排产算法全面验证实验...")
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 创建验证器
    validator = SchedulingAlgorithmValidator()
    
    # 运行全面验证
    validator.run_comprehensive_validation()

if __name__ == "__main__":
    main()
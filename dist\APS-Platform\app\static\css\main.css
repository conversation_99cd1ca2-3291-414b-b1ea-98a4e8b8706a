/*
 * AEC-FT ICP 主样式表
 * 车规芯片终测智能调度平台
 */

:root {
    --primary-color: #b72424;
    --primary-hover: #9a1f1f;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--gray-100);
}

/* 主题色调整 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* 导航栏样式 */
.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: bold;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

/* 侧边栏样式 */
.sidebar {
    background-color: var(--primary-color);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.25rem 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1.2rem;
    text-align: center;
}

/* 卡片样式 */
.card {
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-300);
    font-weight: 600;
}

/* 表格样式 */
.table {
    --bs-table-bg: transparent;
    --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
    --bs-table-hover-bg: rgba(183, 36, 36, 0.05);
}

.table thead th {
    background-color: var(--gray-100);
    border-bottom: 2px solid var(--gray-300);
    font-weight: 600;
    vertical-align: bottom;
}

/* 按钮样式增强 */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.btn:focus {
    box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.25);
}

/* 表单样式 */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid var(--gray-400);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.25);
}

/* 分页样式 */
.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link {
    color: var(--primary-color);
}

.page-link:hover {
    color: var(--primary-hover);
    background-color: var(--gray-200);
}

/* 进度条样式 */
.progress-bar {
    background-color: var(--primary-color);
}

/* 徽章样式 */
.badge.bg-primary {
    background-color: var(--primary-color) !important;
}

/* 警告框样式 */
.alert-primary {
    color: #722828;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--danger-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--gray-900);
    color: white;
    border-radius: 0.375rem;
}

/* 加载动画 */
.spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

.spinner-border-sm {
    width: 0.75rem;
    height: 0.75rem;
    border-width: 0.1em;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-200);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 打印样式 */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

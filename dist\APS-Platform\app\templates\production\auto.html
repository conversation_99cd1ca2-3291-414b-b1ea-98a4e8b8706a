{% extends "base.html" %}

{% block title %}AEC-FT ICP - 自动排产
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
    /* 保持与系统一致的样式 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #9a1f1f;
        border-color: #9a1f1f;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.25);
    }

    .btn-info {
        background-color: #b72424;
        border-color: #b72424;
        color: white;
    }
    .btn-info:hover {
        background-color: #9a1f1f;
        border-color: #9a1f1f;
        color: white;
    }

    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 状态卡片样式 */
    .status-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        text-align: center;
    }

    .status-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .status-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .status-icon.primary { color: #b72424; }
    .status-icon.success { color: #28a745; }
    .status-icon.info { color: #17a2b8; }
    .status-icon.warning { color: #ffc107; }

    .status-value {
        font-size: 2rem;
        font-weight: 700;
        margin: 5px 0;
    }

    .status-label {
        color: #666;
        font-size: 0.9rem;
        margin: 0;
    }

    /* 进度条样式 */
    .progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .progress-container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        width: 400px;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .progress {
        height: 25px;
        margin: 20px 0;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar {
        background-color: #b72424 !important;
        height: 100%;
        position: relative;
    }

    .progress-text {
        margin-bottom: 15px;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .progress-percent {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        line-height: 25px;
        color: white;
        text-align: center;
        font-weight: bold;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }

    /* 表格样式 */
    .table {
        font-size: 0.9rem;
    }
    
    .table td, 
    .table th {
        padding: 0.5rem;
        vertical-align: middle;
    }
    
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
    }
    
    .table tbody tr:hover {
        background-color: #fff1f0;
    }

    /* 历史记录样式 */
    .history-item {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        background-color: #fff;
        transition: all 0.3s ease;
    }

    .history-item:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-color: #b72424;
    }

    .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .history-title {
        font-weight: 600;
        color: #333;
    }

    .history-time {
        color: #666;
        font-size: 0.9rem;
    }

    .history-stats {
        display: flex;
        gap: 20px;
        font-size: 0.9rem;
        color: #666;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .status-card {
            margin-bottom: 15px;
        }
        
        .progress-container {
            width: 90%;
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-cogs fa-2x text-primary"></i>
                        </div>
                        <div>
                            <h4 class="mb-1">APS智能排产调度平台</h4>
                            <p class="text-muted mb-0">车规芯片终测智能调度系统 - 集成APS Scheduler统一调度方案</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
2025-06-28 20:58:04,731 - INFO - 🚀 开始运行算法验证...
2025-06-28 20:58:04,732 - INFO - 🔄 加载验证数据...
2025-06-28 20:58:04,732 - INFO - 加载待排产批次从: Excellist2025.06.05/排产验证/ET_WAIT_LOT.xlsx
2025-06-28 20:58:05,053 - INFO - ✅ 成功加载 409 条待排产批次
2025-06-28 20:58:05,058 - INFO - 加载期望结果从: Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx
2025-06-28 20:58:05,161 - INFO - ✅ 成功加载 409 条期望结果
2025-06-28 20:58:05,165 - INFO - ✅ 验证数据加载成功。
2025-06-28 20:58:05,166 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:58:05,166 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:58:05,953 - INFO - 系统模型包已禁用，请直接从app.models导入
2025-06-28 20:58:06,001 - INFO - 🚀 开始执行真实排产，算法: intelligent
2025-06-28 20:58:06,001 - INFO - 📦 使用传入的 409 条批次数据进行排产
2025-06-28 20:58:06,007 - INFO - ✅ MySQL数据源可用
2025-06-28 20:58:06,007 - INFO - 🔄 缓存更新: test_spec_data
2025-06-28 20:58:06,153 - INFO - 从MySQL获取到 555 条测试规范数据
2025-06-28 20:58:06,174 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,174 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,175 - WARNING - 批次 YX2500000883 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,175 - WARNING - ⚠️ 批次 YX2500000883 配置需求获取失败，跳过
2025-06-28 20:58:06,176 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,176 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,177 - WARNING - 批次 YX2500000884 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,177 - WARNING - ⚠️ 批次 YX2500000884 配置需求获取失败，跳过
2025-06-28 20:58:06,177 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,177 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,178 - WARNING - 批次 YX2500000886 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,178 - WARNING - ⚠️ 批次 YX2500000886 配置需求获取失败，跳过
2025-06-28 20:58:06,179 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,179 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,179 - WARNING - 批次 YX2500000887 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,180 - WARNING - ⚠️ 批次 YX2500000887 配置需求获取失败，跳过
2025-06-28 20:58:06,180 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,180 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,181 - WARNING - 批次 YX2500000939 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,181 - WARNING - ⚠️ 批次 YX2500000939 配置需求获取失败，跳过
2025-06-28 20:58:06,181 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,182 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,182 - WARNING - 批次 YX2500000940 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,183 - WARNING - ⚠️ 批次 YX2500000940 配置需求获取失败，跳过
2025-06-28 20:58:06,183 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,183 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,184 - WARNING - 批次 YX2500000941 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,184 - WARNING - ⚠️ 批次 YX2500000941 配置需求获取失败，跳过
2025-06-28 20:58:06,184 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,185 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,186 - WARNING - 批次 YX2500000943 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,186 - WARNING - ⚠️ 批次 YX2500000943 配置需求获取失败，跳过
2025-06-28 20:58:06,186 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,187 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,187 - WARNING - 批次 YX2500000944 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,188 - WARNING - ⚠️ 批次 YX2500000944 配置需求获取失败，跳过
2025-06-28 20:58:06,188 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,188 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,189 - WARNING - 批次 YX2500001354 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,189 - WARNING - ⚠️ 批次 YX2500001354 配置需求获取失败，跳过
2025-06-28 20:58:06,190 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,190 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,191 - WARNING - 批次 YX2500001355 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,191 - WARNING - ⚠️ 批次 YX2500001355 配置需求获取失败，跳过
2025-06-28 20:58:06,191 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,192 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,192 - WARNING - 批次 YX2500001356 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,192 - WARNING - ⚠️ 批次 YX2500001356 配置需求获取失败，跳过
2025-06-28 20:58:06,193 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,193 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,194 - WARNING - 批次 YX2500001357 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,194 - WARNING - ⚠️ 批次 YX2500001357 配置需求获取失败，跳过
2025-06-28 20:58:06,194 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,194 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,195 - WARNING - 批次 YX2300000310 未找到匹配的测试规范 (DEVICE=JWQ5103ASQFNAT_TA0, STAGE=BTT)
2025-06-28 20:58:06,195 - WARNING - ⚠️ 批次 YX2300000310 配置需求获取失败，跳过
2025-06-28 20:58:06,195 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,196 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,196 - WARNING - 批次 YX2500001202 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,196 - WARNING - ⚠️ 批次 YX2500001202 配置需求获取失败，跳过
2025-06-28 20:58:06,196 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,197 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,197 - WARNING - 批次 YX2300000311 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TA0, STAGE=BTT)
2025-06-28 20:58:06,197 - WARNING - ⚠️ 批次 YX2300000311 配置需求获取失败，跳过
2025-06-28 20:58:06,198 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,198 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,198 - WARNING - 批次 YX2300000312 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TA0, STAGE=BTT)
2025-06-28 20:58:06,198 - WARNING - ⚠️ 批次 YX2300000312 配置需求获取失败，跳过
2025-06-28 20:58:06,199 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,199 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,199 - WARNING - 批次 YX2400000583 未找到匹配的测试规范 (DEVICE=JW3655E, STAGE=BTT)
2025-06-28 20:58:06,199 - WARNING - ⚠️ 批次 YX2400000583 配置需求获取失败，跳过
2025-06-28 20:58:06,200 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,200 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,200 - WARNING - 批次 YX2400000585 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,201 - WARNING - ⚠️ 批次 YX2400000585 配置需求获取失败，跳过
2025-06-28 20:58:06,201 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,201 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,202 - WARNING - 批次 YX2400000603 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,202 - WARNING - ⚠️ 批次 YX2400000603 配置需求获取失败，跳过
2025-06-28 20:58:06,203 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,203 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,203 - WARNING - 批次 YX2400000605 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:06,204 - WARNING - ⚠️ 批次 YX2400000605 配置需求获取失败，跳过
2025-06-28 20:58:06,204 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,204 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,204 - WARNING - 批次 YX24XX040012 未找到匹配的测试规范 (DEVICE=JW3655E, STAGE=BTT)
2025-06-28 20:58:06,205 - WARNING - ⚠️ 批次 YX24XX040012 配置需求获取失败，跳过
2025-06-28 20:58:06,205 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:06,205 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:06,205 - INFO - 🔄 缓存更新: uph_data
2025-06-28 20:58:06,246 - INFO - 从MySQL获取到 1759 条UPH数据
2025-06-28 20:58:06,258 - INFO - ⚡ 从MySQL获取到 1759 条UPH数据（缓存）
2025-06-28 20:58:06,258 - INFO - 🔄 缓存更新: recipe_file_data
2025-06-28 20:58:06,285 - ERROR - MySQL获取工艺配方数据失败: (1054, "Unknown column 'RECIPE_FILE' in 'field list'")
2025-06-28 20:58:06,286 - WARNING - ❌ Excel数据源路径不存在: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\Excellist2025.06.05
2025-06-28 20:58:06,286 - WARNING - 无法获取配方文件数据，跳过KIT配置查询
2025-06-28 20:58:06,286 - INFO - Lot YX2500001955: 查找设备, 需求: {'DEVICE': 'JWH7069TLGAA-M001', 'STAGE': 'UIS', 'HB_PN': '20220182_B', 'TB_PN': 'TB_CC4812A_V41', 'HANDLER_CONFIG': 'PnP', 'PKG_PN': 'TLGA5*6-41L', 'TESTER': 'CTA8280F', 'UPH': '2360', 'TEST_SPEC_SOURCE': 'ET_FT_TEST_SPEC'}
2025-06-28 20:58:06,287 - ERROR - ❌ 执行真实排产失败: 'DataSourceManager' object has no attribute 'get_available_equipment'
2025-06-28 20:58:06,290 - INFO - ✅ 算法执行完成，生成 0 条结果。
2025-06-28 20:58:06,290 - ERROR - ❌ 排产算法未返回任何结果，验证终止。

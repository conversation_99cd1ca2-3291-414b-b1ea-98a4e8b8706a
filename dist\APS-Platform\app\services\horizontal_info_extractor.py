#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
横向信息提取器
专门处理Excel文件前13行的横向布局信息
"""

import os
import json
import logging
import pandas as pd
import datetime
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class HorizontalFieldMapping:
    """横向字段映射配置"""
    field_name: str                 # 目标字段名
    search_keywords: List[str]      # 搜索关键词
    value_offset: tuple            # 值的相对位置 (行偏移, 列偏移)
    data_type: str = 'str'         # 数据类型
    required: bool = False         # 是否必需
    
class HorizontalInfoExtractor:
    """横向信息提取器"""
    
    def __init__(self):
        # 定义横向字段映射配置
        self.field_mappings = {
            'document_type': HorizontalFieldMapping(
                field_name='单据类型',
                search_keywords=['封测外包加工单', '外包加工单', '加工单'],
                value_offset=(0, 0),  # 关键词本身就是值
                required=True
            ),
            'document_number': HorizontalFieldMapping(
                field_name='单据编号',
                search_keywords=['编号：', '编号', 'No.', 'NO.'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=True
            ),
            'processing_type': HorizontalFieldMapping(
                field_name='加工属性',
                search_keywords=['加工属性'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            ),
            'order_date': HorizontalFieldMapping(
                field_name='下单日期',
                search_keywords=['下单日期：', '下单日期', '日期：'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                data_type='date',
                required=False
            ),
            'contractor_name': HorizontalFieldMapping(
                field_name='加工承揽商',
                search_keywords=['加工承揽商：', '加工承揽商', '承揽商：'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            ),
            'client_name': HorizontalFieldMapping(
                field_name='加工委托方',
                search_keywords=['加工委托方：', '加工委托方', '委托方：'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            ),
            'contractor_contact': HorizontalFieldMapping(
                field_name='承揽商联系人',
                search_keywords=['联系人：'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            ),
            'contractor_address': HorizontalFieldMapping(
                field_name='承揽商地址',
                search_keywords=['地址：'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            ),
            'contractor_phone': HorizontalFieldMapping(
                field_name='承揽商电话',
                search_keywords=['电话：'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            ),
            'contractor_email': HorizontalFieldMapping(
                field_name='承揽商Email',
                search_keywords=['Email:', 'email:', 'E-mail:'],
                value_offset=(0, 1),  # 值在关键词右侧1列
                required=False
            )
        }
    
    def extract_horizontal_info(self, df: pd.DataFrame, header_analysis_limit: int = 13) -> Dict[str, Any]:
        """提取横向信息"""
        try:
            result = {
                'status': 'success',
                'horizontal_data': {},
                'extraction_summary': {},
                'found_fields': [],
                'missing_fields': []
            }
            
            rows, cols = df.shape
            search_limit = min(header_analysis_limit, rows)
            
            logger.info(f"开始提取横向信息，搜索前{search_limit}行")
            
            # 提取每个字段
            for field_key, mapping in self.field_mappings.items():
                field_info = self._extract_single_field(df, mapping, search_limit)
                
                if field_info['found']:
                    result['horizontal_data'][mapping.field_name] = field_info['value']
                    result['found_fields'].append({
                        'field_name': mapping.field_name,
                        'position': field_info['position'],
                        'value': field_info['value']
                    })
                    logger.debug(f"找到字段 {mapping.field_name}: {field_info['value']} 位置: {field_info['position']}")
                else:
                    if mapping.required:
                        result['missing_fields'].append(mapping.field_name)
                    logger.debug(f"未找到字段 {mapping.field_name}")
            
            # 生成提取摘要
            result['extraction_summary'] = {
                'total_fields': len(self.field_mappings),
                'found_count': len(result['found_fields']),
                'missing_count': len(result['missing_fields']),
                'success_rate': len(result['found_fields']) / len(self.field_mappings) if self.field_mappings else 0
            }
            
            # 添加元数据
            result['horizontal_data']['提取时间'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            result['horizontal_data']['信息来源'] = '横向布局区域'
            result['horizontal_data']['提取方法'] = 'horizontal_extractor'
            
            logger.info(f"横向信息提取完成，找到 {len(result['found_fields'])}/{len(self.field_mappings)} 个字段")
            
            return result
            
        except Exception as e:
            logger.error(f"横向信息提取失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'horizontal_data': {},
                'extraction_summary': {}
            }
    
    def _extract_single_field(self, df: pd.DataFrame, mapping: HorizontalFieldMapping, search_limit: int) -> Dict[str, Any]:
        """提取单个字段的信息"""
        field_info = {
            'found': False,
            'value': None,
            'position': None,
            'keyword_matched': None
        }
        
        # 在指定范围内搜索关键词
        for row_idx in range(search_limit):
            for col_idx in range(df.shape[1]):
                try:
                    cell_value = df.iloc[row_idx, col_idx]
                    if pd.notna(cell_value):
                        cell_text = str(cell_value).strip()
                        
                        # 检查是否匹配关键词
                        for keyword in mapping.search_keywords:
                            if keyword.lower() in cell_text.lower():
                                # 找到关键词，尝试提取值
                                value = self._extract_value_from_position(
                                    df, row_idx, col_idx, mapping, cell_text, keyword
                                )
                                
                                if value is not None:
                                    field_info['found'] = True
                                    field_info['value'] = value
                                    field_info['position'] = f"第{row_idx+1}行第{col_idx+1}列"
                                    field_info['keyword_matched'] = keyword
                                    return field_info
                                
                except Exception as e:
                    logger.debug(f"处理单元格 ({row_idx}, {col_idx}) 时出错: {e}")
                    continue
        
        return field_info
    
    def _extract_value_from_position(self, df: pd.DataFrame, row_idx: int, col_idx: int, 
                                   mapping: HorizontalFieldMapping, cell_text: str, keyword: str) -> Optional[str]:
        """从指定位置提取值"""
        try:
            row_offset, col_offset = mapping.value_offset
            
            # 如果偏移量为(0,0)，表示关键词本身就是值
            if row_offset == 0 and col_offset == 0:
                return self._clean_cell_value(cell_text, mapping.data_type)
            
            # 计算目标位置
            target_row = row_idx + row_offset
            target_col = col_idx + col_offset
            
            # 检查目标位置是否有效
            if (target_row >= 0 and target_row < df.shape[0] and 
                target_col >= 0 and target_col < df.shape[1]):
                
                target_value = df.iloc[target_row, target_col]
                if pd.notna(target_value):
                    return self._clean_cell_value(str(target_value).strip(), mapping.data_type)
            
            # 如果偏移位置没有值，尝试在同一行中搜索
            if row_offset == 0:
                return self._search_value_in_row(df, row_idx, col_idx, keyword, mapping.data_type)
            
            return None
            
        except Exception as e:
            logger.debug(f"从位置提取值时出错: {e}")
            return None
    
    def _search_value_in_row(self, df: pd.DataFrame, row_idx: int, start_col: int, 
                           keyword: str, data_type: str) -> Optional[str]:
        """在同一行中搜索值"""
        try:
            # 从关键词位置开始向右搜索
            for col_idx in range(start_col + 1, df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    # 跳过包含关键词的单元格和空值
                    if cell_text and keyword.lower() not in cell_text.lower():
                        return self._clean_cell_value(cell_text, data_type)
            
            return None
            
        except Exception as e:
            logger.debug(f"在行中搜索值时出错: {e}")
            return None
    
    def _clean_cell_value(self, value: str, data_type: str) -> str:
        """清理单元格值"""
        if not value:
            return ''
        
        # 移除常见的标点符号
        value = value.strip().rstrip('：:')
        
        if data_type == 'date':
            # 尝试标准化日期格式
            date_patterns = [
                r'(\d{4}-\d{1,2}-\d{1,2})',
                r'(\d{4}/\d{1,2}/\d{1,2})',
                r'(\d{4}\.\d{1,2}\.\d{1,2})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, value)
                if match:
                    return match.group(1)
        
        return value
    
    def format_horizontal_summary(self, horizontal_data: Dict[str, Any]) -> str:
        """格式化横向信息摘要"""
        if not horizontal_data:
            return "未提取到横向信息"
        
        summary_lines = []
        summary_lines.append("📋 横向信息摘要:")
        
        # 基本信息
        basic_fields = ['单据类型', '单据编号', '加工属性', '下单日期']
        for field in basic_fields:
            if field in horizontal_data:
                summary_lines.append(f"  • {field}: {horizontal_data[field]}")
        
        # 联系信息
        if '加工承揽商' in horizontal_data or '加工委托方' in horizontal_data:
            summary_lines.append("  📞 相关方信息:")
            contact_fields = ['加工承揽商', '加工委托方', '承揽商联系人', '承揽商电话', '承揽商Email']
            for field in contact_fields:
                if field in horizontal_data:
                    summary_lines.append(f"    - {field}: {horizontal_data[field]}")
        
        return '\n'.join(summary_lines)

def test_horizontal_extractor():
    """测试横向信息提取器"""
    test_file = "downloads/email_attachments/宜欣  生产订单模板(新封装-测试-编带)2025040901  JW7106-M001.xls"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print("🔍 测试横向信息提取器")
    print("=" * 60)
    
    # 读取Excel文件
    df = pd.read_excel(test_file, engine='xlrd', header=None)
    print(f"文件尺寸: {df.shape[0]} 行 x {df.shape[1]} 列")
    
    # 创建提取器并提取信息
    extractor = HorizontalInfoExtractor()
    result = extractor.extract_horizontal_info(df)
    
    if result['status'] == 'success':
        print(f"\n✅ 提取状态: 成功")
        print(f"📊 提取摘要: 找到 {result['extraction_summary']['found_count']}/{result['extraction_summary']['total_fields']} 个字段 (成功率: {result['extraction_summary']['success_rate']:.1%})")
        
        print(f"\n📋 找到的字段:")
        for field_info in result['found_fields']:
            print(f"  • {field_info['field_name']}: '{field_info['value']}' (位置: {field_info['position']})")
        
        if result['missing_fields']:
            print(f"\n❌ 缺失的字段: {', '.join(result['missing_fields'])}")
        
        print(f"\n{extractor.format_horizontal_summary(result['horizontal_data'])}")
        
    else:
        print(f"❌ 提取失败: {result.get('message', '未知错误')}")

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    test_horizontal_extractor() 
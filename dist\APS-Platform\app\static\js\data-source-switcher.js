/**
 * 数据源切换器 - 当MySQL数据库出问题时切换到Excel数据源
 */

class DataSourceSwitcher {
    constructor() {
        this.currentSource = 'mysql';
        this.isChecking = false;
        this.init();
    }

    init() {
        this.createSwitcherUI();
        this.checkDataSourceStatus();
        this.setupEventListeners();
        
        // 每30秒检查一次数据源状态
        setInterval(() => {
            this.checkDataSourceStatus();
        }, 30000);
    }

    createSwitcherUI() {
        // 创建数据源切换器HTML
        const switcherHTML = `
            <div id="data-source-switcher" class="card border-warning mb-3" style="display: none;">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-database"></i> 数据源状态监控
                    </h6>
                </div>
                <div class="card-body">
                    <div id="source-status" class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="badge badge-secondary me-2" id="mysql-status">MySQL</span>
                                    <span id="mysql-indicator" class="text-muted">检查中...</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="badge badge-secondary me-2" id="excel-status">Excel</span>
                                    <span id="excel-indicator" class="text-muted">检查中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">当前数据源: </span>
                            <span id="current-source-display" class="badge badge-primary">MySQL</span>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="switch-to-mysql">
                                <i class="fas fa-database"></i> 使用MySQL
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-success" id="switch-to-excel">
                                <i class="fas fa-file-excel"></i> 使用Excel
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" id="switch-to-auto">
                                <i class="fas fa-magic"></i> 自动选择
                            </button>
                        </div>
                    </div>
                    
                    <div id="switch-message" class="mt-2" style="display: none;"></div>
                </div>
            </div>
        `;

        // 查找合适的插入位置（通常在页面顶部）
        const targetContainer = document.querySelector('.container-fluid') || 
                               document.querySelector('.container') || 
                               document.body;
        
        if (targetContainer) {
            targetContainer.insertAdjacentHTML('afterbegin', switcherHTML);
        }
    }

    setupEventListeners() {
        // MySQL切换按钮
        document.getElementById('switch-to-mysql')?.addEventListener('click', () => {
            this.switchDataSource('mysql');
        });

        // Excel切换按钮
        document.getElementById('switch-to-excel')?.addEventListener('click', () => {
            this.switchDataSource('excel');
        });

        // 自动选择按钮
        document.getElementById('switch-to-auto')?.addEventListener('click', () => {
            this.switchDataSource('auto');
        });
    }

    async checkDataSourceStatus() {
        if (this.isChecking) return;
        this.isChecking = true;

        try {
            const response = await fetch('/api/v2/production/data-source/status');
            const status = await response.json();

            this.updateStatusDisplay(status);
            this.currentSource = status.current_source || 'mysql';
            
            // 如果MySQL不可用且当前使用MySQL，显示切换建议
            if (!status.mysql_available && this.currentSource === 'mysql') {
                this.showSwitchSuggestion();
            }

        } catch (error) {
            console.error('检查数据源状态失败:', error);
            this.showErrorState();
        } finally {
            this.isChecking = false;
        }
    }

    updateStatusDisplay(status) {
        const switcher = document.getElementById('data-source-switcher');
        if (!switcher) return;

        // 更新MySQL状态
        const mysqlStatus = document.getElementById('mysql-status');
        const mysqlIndicator = document.getElementById('mysql-indicator');
        if (status.mysql_available) {
            mysqlStatus.className = 'badge badge-success me-2';
            mysqlIndicator.innerHTML = '<i class="fas fa-check-circle text-success"></i> 正常';
        } else {
            mysqlStatus.className = 'badge badge-danger me-2';
            mysqlIndicator.innerHTML = '<i class="fas fa-exclamation-circle text-danger"></i> 不可用';
        }

        // 更新Excel状态
        const excelStatus = document.getElementById('excel-status');
        const excelIndicator = document.getElementById('excel-indicator');
        if (status.excel_available) {
            excelStatus.className = 'badge badge-success me-2';
            excelIndicator.innerHTML = '<i class="fas fa-check-circle text-success"></i> 正常';
        } else {
            excelStatus.className = 'badge badge-warning me-2';
            excelIndicator.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> 不可用';
        }

        // 更新当前数据源显示
        const currentSourceDisplay = document.getElementById('current-source-display');
        if (currentSourceDisplay) {
            currentSourceDisplay.textContent = status.current_source === 'excel' ? 'Excel' : 'MySQL';
            currentSourceDisplay.className = status.current_source === 'excel' ? 
                'badge badge-success' : 'badge badge-primary';
        }

        // 根据状态决定是否显示切换器
        if (!status.mysql_available || status.current_source === 'excel') {
            switcher.style.display = 'block';
        } else if (status.mysql_available && status.current_source === 'mysql') {
            // MySQL正常且正在使用MySQL时，可以隐藏切换器
            switcher.style.display = 'none';
        }
    }

    showSwitchSuggestion() {
        const switcher = document.getElementById('data-source-switcher');
        const messageDiv = document.getElementById('switch-message');
        
        if (switcher && messageDiv) {
            switcher.style.display = 'block';
            messageDiv.style.display = 'block';
            messageDiv.innerHTML = `
                <div class="alert alert-warning alert-sm mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>注意:</strong> MySQL数据库连接异常，建议切换到Excel数据源继续排产操作。
                </div>
            `;
        }
    }

    showErrorState() {
        const switcher = document.getElementById('data-source-switcher');
        if (switcher) {
            switcher.style.display = 'block';
            document.getElementById('mysql-indicator').innerHTML = 
                '<i class="fas fa-question-circle text-muted"></i> 未知';
            document.getElementById('excel-indicator').innerHTML = 
                '<i class="fas fa-question-circle text-muted"></i> 未知';
        }
    }

    async switchDataSource(targetSource) {
        const messageDiv = document.getElementById('switch-message');
        
        try {
            // 显示加载状态
            messageDiv.style.display = 'block';
            messageDiv.innerHTML = `
                <div class="alert alert-info alert-sm mb-0">
                    <i class="fas fa-spinner fa-spin"></i> 正在切换到 ${this.getSourceDisplayName(targetSource)} 数据源...
                </div>
            `;

            const response = await fetch('/api/v2/production/data-source/switch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ source: targetSource })
            });

            const result = await response.json();

            if (result.success) {
                messageDiv.innerHTML = `
                    <div class="alert alert-success alert-sm mb-0">
                        <i class="fas fa-check-circle"></i> ${result.message}
                    </div>
                `;
                
                // 更新当前数据源
                this.currentSource = result.current_source || targetSource;
                
                // 延迟刷新状态
                setTimeout(() => {
                    this.checkDataSourceStatus();
                    
                    // 如果页面有排产相关的数据，建议刷新
                    if (typeof window.refreshSchedulingData === 'function') {
                        window.refreshSchedulingData();
                    }
                }, 1000);

                // 3秒后隐藏成功消息
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 3000);

            } else {
                messageDiv.innerHTML = `
                    <div class="alert alert-danger alert-sm mb-0">
                        <i class="fas fa-exclamation-circle"></i> 切换失败: ${result.message}
                    </div>
                `;
            }

        } catch (error) {
            console.error('切换数据源失败:', error);
            messageDiv.innerHTML = `
                <div class="alert alert-danger alert-sm mb-0">
                    <i class="fas fa-exclamation-circle"></i> 切换失败: 网络错误
                </div>
            `;
        }
    }

    getSourceDisplayName(source) {
        const names = {
            'mysql': 'MySQL数据库',
            'excel': 'Excel文件',
            'auto': '自动选择'
        };
        return names[source] || source;
    }

    // 公共方法：强制显示切换器
    show() {
        const switcher = document.getElementById('data-source-switcher');
        if (switcher) {
            switcher.style.display = 'block';
        }
    }

    // 公共方法：隐藏切换器
    hide() {
        const switcher = document.getElementById('data-source-switcher');
        if (switcher) {
            switcher.style.display = 'none';
        }
    }
}

// 全局实例
let dataSourceSwitcher = null;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dataSourceSwitcher = new DataSourceSwitcher();
    
    // 将实例暴露到全局，方便其他脚本调用
    window.dataSourceSwitcher = dataSourceSwitcher;
});

// 为排产页面提供的数据刷新回调函数
window.refreshSchedulingData = function() {
    // 这个函数可以在具体的排产页面中重写
    console.log('数据源已切换，建议刷新排产数据');
    
    // 如果页面有刷新数据的方法，可以在这里调用
    if (typeof refreshData === 'function') {
        refreshData();
    }
    
    // 或者简单地重新加载页面
    // location.reload();
}; 
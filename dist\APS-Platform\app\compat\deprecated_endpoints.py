#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
废弃端点管理系统
管理即将被移除的API v1端点，提供废弃警告和迁移指导
"""

from functools import wraps
from flask import request, jsonify, current_app
import warnings
from datetime import datetime, timedelta
import logging

# 配置日志
deprecated_logger = logging.getLogger('deprecated_api')

class DeprecatedEndpoint:
    """废弃端点配置类"""
    
    def __init__(self, endpoint, version='v1', removal_date=None, replacement=None, reason=None):
        self.endpoint = endpoint
        self.version = version
        self.removal_date = removal_date or datetime.now() + timedelta(days=90)  # 默认90天后移除
        self.replacement = replacement
        self.reason = reason or "API版本升级"
        
    def to_dict(self):
        return {
            'endpoint': self.endpoint,
            'version': self.version,
            'removal_date': self.removal_date.isoformat(),
            'replacement': self.replacement,
            'reason': self.reason
        }

# 废弃端点配置
DEPRECATED_ENDPOINTS = {
    # 低频使用的API v1端点 (使用次数 < 3)
    '/api/ai/config': DeprecatedEndpoint(
        endpoint='/api/ai/config',
        replacement='/api/v2/system/ai-config',
        reason='迁移到系统设置模块'
    ),
    '/api/dashboard/stats': DeprecatedEndpoint(
        endpoint='/api/dashboard/stats',
        replacement='/api/v2/system/monitoring/metrics',
        reason='合并到性能监控系统'
    ),
    '/api/dashboard/charts': DeprecatedEndpoint(
        endpoint='/api/dashboard/charts',
        replacement='/api/v2/system/monitoring/charts',
        reason='合并到性能监控系统'
    ),
    '/api/debug/dashboard': DeprecatedEndpoint(
        endpoint='/api/debug/dashboard',
        replacement='/api/v2/system/monitoring/debug',
        reason='合并到系统监控'
    ),
    
    # 未使用的API v1端点 (已移除)
    # '/compatibility/scheduling' - 已删除，无实际使用
    '/menu/settings/<int:id>': DeprecatedEndpoint(
        endpoint='/menu/settings/<int:id>',
        replacement='/api/v2/auth/menu-settings',
        reason='菜单管理已重构',
        removal_date=datetime.now() + timedelta(days=30)
    ),
    '/check-database': DeprecatedEndpoint(
        endpoint='/check-database',
        replacement='/test-database-connection',
        reason='端点重命名',
        removal_date=datetime.now() + timedelta(days=30)
    ),
    '/auth/users/<username>/permissions': DeprecatedEndpoint(
        endpoint='/auth/users/<username>/permissions',
        replacement='/api/v2/auth/users/<username>/permissions',
        reason='迁移到API v2',
        removal_date=datetime.now() + timedelta(days=60)
    ),

}

def deprecated_endpoint(endpoint_path=None, replacement=None, removal_date=None, reason=None):
    """
    标记废弃端点的装饰器
    
    Args:
        endpoint_path: 端点路径
        replacement: 替代端点
        removal_date: 移除日期
        reason: 废弃原因
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取端点信息
            endpoint = endpoint_path or request.endpoint
            
            # 检查是否在废弃列表中
            deprecated_info = DEPRECATED_ENDPOINTS.get(endpoint)
            if not deprecated_info:
                deprecated_info = DeprecatedEndpoint(
                    endpoint=endpoint,
                    replacement=replacement,
                    removal_date=removal_date,
                    reason=reason
                )
            
            # 记录废弃API使用
            deprecated_logger.warning(
                f"Deprecated API called: {endpoint} from {request.remote_addr}. "
                f"Replacement: {deprecated_info.replacement}. "
                f"Removal date: {deprecated_info.removal_date}"
            )
            
            # 在响应头中添加废弃警告
            response = func(*args, **kwargs)
            
            if hasattr(response, 'headers'):
                response.headers['X-API-Deprecated'] = 'true'
                response.headers['X-API-Replacement'] = deprecated_info.replacement or 'none'
                response.headers['X-API-Removal-Date'] = deprecated_info.removal_date.isoformat()
                response.headers['X-API-Deprecation-Reason'] = deprecated_info.reason
            
            # 如果是JSON响应，添加废弃信息
            if hasattr(response, 'json') and response.is_json:
                try:
                    data = response.get_json()
                    if isinstance(data, dict):
                        data['_deprecated'] = {
                            'warning': f'This endpoint is deprecated and will be removed on {deprecated_info.removal_date.strftime("%Y-%m-%d")}',
                            'replacement': deprecated_info.replacement,
                            'reason': deprecated_info.reason
                        }
                        response = jsonify(data)
                except:
                    pass  # 如果修改响应失败，继续正常流程
            
            return response
        return wrapper
    return decorator

def get_deprecated_endpoints():
    """获取所有废弃端点信息"""
    return {k: v.to_dict() for k, v in DEPRECATED_ENDPOINTS.items()}

def is_endpoint_deprecated(endpoint):
    """检查端点是否已废弃"""
    return endpoint in DEPRECATED_ENDPOINTS

def get_replacement_endpoint(endpoint):
    """获取端点的替代方案"""
    deprecated_info = DEPRECATED_ENDPOINTS.get(endpoint)
    return deprecated_info.replacement if deprecated_info else None

def check_removal_due_endpoints():
    """检查即将到期的废弃端点"""
    now = datetime.now()
    due_endpoints = []
    
    for endpoint, info in DEPRECATED_ENDPOINTS.items():
        if info.removal_date <= now:
            due_endpoints.append({
                'endpoint': endpoint,
                'removal_date': info.removal_date,
                'replacement': info.replacement
            })
    
    return due_endpoints

def generate_deprecation_report():
    """生成废弃端点报告"""
    now = datetime.now()
    
    report = {
        'total_deprecated': len(DEPRECATED_ENDPOINTS),
        'removal_schedule': {
            'within_30_days': [],
            'within_90_days': [],
            'beyond_90_days': []
        },
        'by_category': {
            'low_usage': [],
            'unused': [],
            'compatibility': []
        }
    }
    
    for endpoint, info in DEPRECATED_ENDPOINTS.items():
        days_until_removal = (info.removal_date - now).days
        
        # 按时间分类
        if days_until_removal <= 30:
            report['removal_schedule']['within_30_days'].append(info.to_dict())
        elif days_until_removal <= 90:
            report['removal_schedule']['within_90_days'].append(info.to_dict())
        else:
            report['removal_schedule']['beyond_90_days'].append(info.to_dict())
        
        # 按类型分类
        if 'compatibility' in info.reason.lower():
            report['by_category']['compatibility'].append(info.to_dict())
        elif '未使用' in info.reason or 'unused' in info.reason.lower():
            report['by_category']['unused'].append(info.to_dict())
        else:
            report['by_category']['low_usage'].append(info.to_dict())
    
    return report

# Flask蓝图注册废弃端点管理API
from flask import Blueprint

deprecated_bp = Blueprint('deprecated', __name__, url_prefix='/api/deprecated')

def list_deprecated_endpoints():
    """列出所有废弃端点"""
    return jsonify(get_deprecated_endpoints())

@deprecated_bp.route('/report', methods=['GET'])
def deprecation_report():
    """获取废弃端点报告"""
    return jsonify(generate_deprecation_report())

def check_endpoint_status(endpoint):
    """检查特定端点的废弃状态"""
    endpoint = '/' + endpoint
    
    if is_endpoint_deprecated(endpoint):
        info = DEPRECATED_ENDPOINTS[endpoint]
        return jsonify({
            'deprecated': True,
            'info': info.to_dict()
        })
    else:
        return jsonify({
            'deprecated': False,
            'message': 'Endpoint is not deprecated'
        })

# 中间件：自动检测和警告废弃端点使用
def init_deprecated_endpoints_middleware(app):
    """初始化废弃端点中间件"""
    
    @app.before_request
    def check_deprecated_endpoint():
        """请求前检查是否使用废弃端点"""
        endpoint = request.path
        
        if is_endpoint_deprecated(endpoint):
            deprecated_info = DEPRECATED_ENDPOINTS[endpoint]
            
            # 记录使用情况
            app.logger.warning(
                f"Deprecated endpoint accessed: {endpoint} by {request.remote_addr}"
            )
            
            # 在开发模式下发出Python警告
            if app.debug:
                warnings.warn(
                    f"Deprecated API endpoint '{endpoint}' accessed. "
                    f"Use '{deprecated_info.replacement}' instead. "
                    f"This endpoint will be removed on {deprecated_info.removal_date.strftime('%Y-%m-%d')}",
                    DeprecationWarning,
                    stacklevel=2
                )
    
    # 注册废弃端点管理蓝图
    app.register_blueprint(deprecated_bp) 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式测试页面 - AEC-FT ICP</title>
    <link rel="stylesheet" href="/static/vendor/bootstrap/bootstrap.min.css">
    <link rel="stylesheet" href="/static/vendor/fontawesome/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compatibility.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-primary mb-4">
                    <i class="fas fa-check-circle"></i>
                    AEC-FT ICP 样式测试页面
                </h1>
                
                <div class="alert alert-success" role="alert">
                    <h4 class="alert-heading">样式加载测试</h4>
                    <p>如果您能看到这个带有绿色背景的警告框，说明Bootstrap CSS已正确加载。</p>
                    <hr>
                    <p class="mb-0">如果您能看到上方标题左侧的勾选图标，说明FontAwesome图标库已正确加载。</p>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">组件测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>按钮测试</h6>
                                <button type="button" class="btn btn-primary me-2">
                                    <i class="fas fa-play"></i> 主要按钮
                                </button>
                                <button type="button" class="btn btn-secondary me-2">
                                    <i class="fas fa-pause"></i> 次要按钮
                                </button>
                                <button type="button" class="btn btn-success">
                                    <i class="fas fa-check"></i> 成功按钮
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>表单测试</h6>
                                <input type="text" class="form-control mb-2" placeholder="输入框测试">
                                <select class="form-select">
                                    <option>选择框测试</option>
                                    <option>选项1</option>
                                    <option>选项2</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6>进度条测试</h6>
                                <div class="progress mb-3">
                                    <div class="progress-bar" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6>表格测试</h6>
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>测试项目1</td>
                                            <td><span class="badge bg-success">正常</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>测试项目2</td>
                                            <td><span class="badge bg-warning">警告</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <strong>访问说明:</strong> 
                    将此文件放在Flask应用的静态目录中，然后访问 
                    <code>http://localhost:5000/static/test_styles.html</code> 来查看效果。
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/vendor/bootstrap/bootstrap.bundle.min.js"></script>
</body>
</html>
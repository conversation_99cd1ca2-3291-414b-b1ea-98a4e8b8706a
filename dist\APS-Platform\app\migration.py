"""
菜单系统数据迁移工具
用于在系统升级过程中从数据库菜单表(menu_settings)迁移到基于配置文件的菜单系统
"""

from app import db
from app.models import UserPermission, User, MenuSetting
from app.config.menu_config import MENU_ID_MAP, get_all_menu_ids
from flask import current_app
import logging

logger = logging.getLogger(__name__)

def migrate_menu_system():
    """
    执行菜单系统迁移
    1. 将用户权限从MenuSetting表迁移到直接记录menu_id
    2. 为管理员分配所有权限
    """
    logger.info("开始菜单系统迁移...")
    
    try:
        # 获取所有用户
        users = User.query.all()
        logger.info(f"找到 {len(users)} 个用户需要迁移权限")
        
        # 迁移每个用户的权限
        for user in users:
            # 管理员拥有所有权限
            if user.role == 'admin':
                logger.info(f"用户 {user.username} 是管理员，分配所有菜单权限")
                menu_ids = get_all_menu_ids()
            else:
                # 获取旧权限系统中的权限
                try:
                    # 尝试使用旧的permissions表
                    old_permissions_query = """
                        SELECT menu_id FROM user_permissions 
                        WHERE username = :username
                    """
                    result = db.session.execute(old_permissions_query, {'username': user.username})
                    menu_ids = [row[0] for row in result]
                    
                    logger.info(f"从user_permissions表获取用户 {user.username} 的 {len(menu_ids)} 个权限")
                except Exception as e:
                    logger.error(f"无法获取用户 {user.username} 的旧权限: {str(e)}")
                    menu_ids = []
            
            # 更新权限
            # 清除现有权限
            UserPermission.query.filter_by(username=user.username).delete()
            
            # 添加新权限
            for menu_id in menu_ids:
                permission = UserPermission(username=user.username, menu_id=menu_id)
                db.session.add(permission)
            
            logger.info(f"为用户 {user.username} 设置了 {len(menu_ids)} 个权限")
        
        # 提交更改
        db.session.commit()
        logger.info("菜单系统迁移完成")
        
        return True, "迁移成功"
    
    except Exception as e:
        db.session.rollback()
        error_msg = f"菜单系统迁移失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg 
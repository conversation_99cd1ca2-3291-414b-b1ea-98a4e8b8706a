"""
订单处理API路由 - 统一的订单获取、解析和分类处理
"""

import os
import uuid
import logging
from datetime import datetime
from flask import request, jsonify
from flask_login import current_user
from app.api_v2.production import production_bp
try:
    from app.services.order_processing_service import OrderProcessingService
except ImportError as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"订单处理服务导入失败: {e}")
    OrderProcessingService = None
from app.services.task_manager import TaskManager
from app.services.event_bus import EventBus

logger = logging.getLogger(__name__)

# 获取全局服务实例
from app.services.order_processing_service import get_order_processing_service
from app.services.task_manager import get_task_manager
from app.services.event_bus import get_event_bus

order_service = get_order_processing_service()
task_manager = get_task_manager()
event_bus = get_event_bus()

def get_current_user_id():
    """获取当前用户ID"""
    if current_user.is_authenticated:
        return current_user.username
    return request.headers.get('X-User-Id', 'anonymous')

@production_bp.route('/order-processing/start', methods=['POST'])
def start_order_processing():
    """
    启动订单处理任务
    支持两种模式：
    1. fetch_and_parse: 获取邮箱附件并解析
    2. parse_existing: 仅解析现有文件
    """
    try:
        data = request.get_json() or {}
        
        # 验证必要参数
        task_type = data.get('task_type')
        if not task_type or task_type not in ['fetch_and_parse', 'parse_existing']:
            return jsonify({
                'status': 'error',
                'message': '无效的任务类型，支持：fetch_and_parse, parse_existing'
            }), 400
        
        source_directory = data.get('source_directory', 'downloads')
        email_config_id = data.get('email_config_id')
        classification_rules = data.get('classification_rules')
        
        # 验证邮箱配置（获取并解析模式必需）
        if task_type == 'fetch_and_parse' and not email_config_id:
            return jsonify({
                'status': 'error',
                'message': '获取并解析模式需要指定邮箱配置'
            }), 400
        
        # 获取当前用户ID
        user_id = get_current_user_id()
        
        # 创建任务
        task_id = task_manager.create_task(
            name=f'订单处理任务 - {task_type}',
            description=f'执行{task_type}模式的订单处理',
            user_id=user_id
        )
        
        # 启动后台任务
        if task_type == 'fetch_and_parse':
            success = task_manager.start_task(
                task_id=task_id,
                target_func=order_service._execute_fetch_and_parse_task,
                config_id=email_config_id
            )
        else:  # parse_existing
            # 获取指定目录下的Excel文件
            import glob
            excel_files = glob.glob(os.path.join(source_directory, "*.xlsx")) + \
                         glob.glob(os.path.join(source_directory, "*.xls"))
            
            success = task_manager.start_task(
                task_id=task_id,
                target_func=order_service._execute_parse_existing_files_task,
                file_paths=excel_files,
                classification_rules=classification_rules
            )
        
        if success:
            logger.info(f"订单处理任务已启动，任务ID: {task_id}")
            return jsonify({
                'status': 'success',
                'message': '订单处理任务已启动',
                'task_id': task_id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '启动任务失败'
            }), 500
            
    except Exception as e:
        logger.error(f"启动订单处理任务失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'启动任务失败: {str(e)}'
        }), 500

@production_bp.route('/order-processing/control/<task_id>', methods=['POST'])
def control_order_processing(task_id):
    """
    控制订单处理任务
    支持操作：pause, resume, cancel
    """
    try:
        data = request.get_json() or {}
        action = data.get('action')
        user_id = get_current_user_id()
        
        if not action or action not in ['pause', 'resume', 'cancel']:
            return jsonify({
                'status': 'error',
                'message': '无效的操作，支持：pause, resume, cancel'
            }), 400
        
        # 验证用户权限
        task = task_manager.get_task(task_id)
        if not task:
            return jsonify({
                'status': 'error',
                'message': '任务不存在'
            }), 404
        
        if task.user_id != user_id:
            return jsonify({
                'status': 'error',
                'message': '无权限操作该任务'
            }), 403
        
        # 执行任务控制
        if action == 'pause':
            success = task_manager.pause_task(task_id)
            message = '任务已暂停'
        elif action == 'resume':
            success = task_manager.resume_task(task_id)
            message = '任务已恢复'
        elif action == 'cancel':
            success = task_manager.cancel_task(task_id)
            message = '任务已取消'
        
        if success:
            logger.info(f"任务 {task_id} 执行操作 {action} 成功")
            
            # 发布事件
            event_bus.publish_event(
                'TASK_UPDATED',
                {
                    'task_id': task_id,
                    'action': action,
                    'timestamp': datetime.now().isoformat()
                },
                user_id=user_id
            )
            
            return jsonify({
                'status': 'success',
                'message': message
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'执行操作失败，任务可能不存在或状态不允许此操作'
            }), 400
            
    except Exception as e:
        logger.error(f"控制任务 {task_id} 失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'控制任务失败: {str(e)}'
        }), 500

@production_bp.route('/order-processing/status/<task_id>', methods=['GET'])
def get_order_processing_status(task_id):
    """
    查询订单处理任务状态
    """
    try:
        task = task_manager.get_task(task_id)
        task_status = task.to_dict() if task else None
        
        if task_status:
            return jsonify({
                'status': 'success',
                'task_status': task_status
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '任务不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'查询任务状态失败: {str(e)}'
        }), 500

@production_bp.route('/order-processing/list', methods=['GET'])
def list_order_processing_tasks():
    """
    列出所有订单处理任务
    """
    try:
        # 获取查询参数
        status_filter = request.args.get('status')  # pending, running, completed, failed
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        
        # TaskManager中没有list_tasks方法，使用get_user_tasks代替
        # 这里简化处理，返回所有任务
        with task_manager._task_lock:
            all_tasks = list(task_manager._tasks.values())
            
        # 应用状态过滤
        if status_filter:
            from app.services.task_manager import TaskStatus
            try:
                status_enum = TaskStatus(status_filter)
                all_tasks = [t for t in all_tasks if t.status == status_enum]
            except ValueError:
                pass  # 无效状态过滤器，忽略
        
        # 排序和分页
        all_tasks.sort(key=lambda t: t.created_at, reverse=True)
        tasks = all_tasks[offset:offset + limit]
        tasks = [task.to_dict() for task in tasks]
        
        return jsonify({
            'status': 'success',
            'tasks': tasks,
            'total': len(tasks)
        })
        
    except Exception as e:
        logger.error(f"列出任务失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'列出任务失败: {str(e)}'
        }), 500

@production_bp.route('/order-processing/stats', methods=['GET'])
def get_order_processing_stats():
    """
    获取订单处理统计信息
    """
    try:
        # 简化统计信息获取，避免复杂的数据库查询
        basic_stats = {
            'timestamp': datetime.now().isoformat(),
            'task_summary': {
                'total_tasks': 0,
                'running_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0
            },
            'system_status': {
                'task_manager': 'healthy',
                'event_bus': 'healthy',
                'order_service': 'healthy'
            }
        }
        
        # 尝试获取任务统计（安全方式）
        try:
            if task_manager and hasattr(task_manager, '_tasks'):
                with task_manager._task_lock:
                    tasks = task_manager._tasks
                    basic_stats['task_summary']['total_tasks'] = len(tasks)
                    
                    # 按状态统计
                    from app.services.task_manager import TaskStatus
                    status_counts = {}
                    for task in tasks.values():
                        status = task.status.value
                        status_counts[status] = status_counts.get(status, 0) + 1
                    
                    basic_stats['task_summary']['running_tasks'] = status_counts.get('running', 0)
                    basic_stats['task_summary']['completed_tasks'] = status_counts.get('completed', 0)
                    basic_stats['task_summary']['failed_tasks'] = status_counts.get('failed', 0)
                    basic_stats['task_summary']['status_breakdown'] = status_counts
        except Exception as e:
            logger.warning(f"无法获取详细任务统计: {e}")
            basic_stats['task_summary']['note'] = '详细统计暂不可用'
        
        return jsonify({
            'status': 'success',
            'stats': basic_stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取统计信息失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@production_bp.route('/order-processing/health', methods=['GET'])
def get_order_processing_health():
    """
    获取订单处理服务健康状态
    """
    try:
        # 简化健康检查，避免死锁
        health_info = {
            'timestamp': datetime.now().isoformat(),
            'services': {
                'task_manager': 'available',
                'event_bus': 'available', 
                'order_service': 'available'
            },
            'basic_checks': {
                'task_manager_exists': task_manager is not None,
                'event_bus_exists': event_bus is not None,
                'order_service_exists': order_service is not None
            }
        }
        
        # 基础健康检查
        basic_healthy = all(health_info['basic_checks'].values())
        
        # 尝试获取简单状态（如果可能的话）
        try:
            with task_manager._task_lock:
                task_count = len(task_manager._tasks)
            health_info['task_count'] = task_count
        except:
            health_info['task_count'] = 'unknown'
        
        return jsonify({
            'status': 'success',
            'healthy': basic_healthy,
            'message': 'Health check completed',
            'details': health_info
        })
        
    except Exception as e:
        logger.error(f"获取健康状态失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'健康检查失败: {str(e)}',
            'healthy': False,
            'timestamp': datetime.now().isoformat()
        }), 500 
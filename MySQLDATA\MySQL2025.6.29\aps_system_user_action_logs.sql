-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `user_action_logs`
--

DROP TABLE IF EXISTS `user_action_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_action_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=235 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_action_logs`
--

LOCK TABLES `user_action_logs` WRITE;
/*!40000 ALTER TABLE `user_action_logs` DISABLE KEYS */;
INSERT INTO `user_action_logs` VALUES (1,'system','test','database_fix','fix_script','数据库修复脚本测试','127.0.0.1',NULL,'2025-06-20 05:59:05'),(2,'system','test','database_fix','quick_fix','快速修复脚本测试','127.0.0.1',NULL,NULL),(3,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-06-20 06:00:41'),(4,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 你是谁？..., 模式: database','127.0.0.1',NULL,'2025-06-20 06:00:59'),(5,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-20T07:29:17.156762\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-20 07:29:17'),(6,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-06-20 07:29:21'),(7,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们的封装项目如何？限定50个字之内回答我..., 模式: database','127.0.0.1',NULL,'2025-06-20 07:30:09'),(8,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T02:55:54.844998\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 02:55:55'),(9,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:02:12'),(10,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:15:50'),(11,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:15:54'),(12,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:16:04'),(13,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:22:30'),(14,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T03:57:52.971470\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:57:53'),(15,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:12:17'),(16,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T04:44:25.671807\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:44:26'),(17,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T04:45:02.110122\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:45:02'),(18,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T04:57:10.554617\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:57:11'),(19,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T05:11:39.917309\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 05:11:40'),(20,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T05:46:45.607300\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 05:46:46'),(21,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T08:47:11.168374\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 08:47:11'),(22,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T08:53:38.629626\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 08:53:39'),(23,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T09:04:20.068508\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 09:04:20'),(24,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T09:13:59.798082\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 09:14:00'),(25,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T10:23:15.256864\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 10:23:15'),(26,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T11:20:15.829421\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 11:20:16'),(27,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T11:32:35.398743\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 11:32:35'),(28,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 11:40:58'),(29,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:00:41.320718\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:00:41'),(30,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:07:24.667569\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:07:25'),(31,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','python-requests/2.31.0','2025-06-22 12:07:25'),(32,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','python-requests/2.31.0','2025-06-22 12:07:25'),(33,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:10:08'),(34,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:10:35'),(35,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:11:02'),(36,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:28:00.673566\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:28:01'),(37,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:29:33.531636\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:29:34'),(38,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:29:55.526528\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:29:56'),(39,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:30:11.433972\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:30:11'),(40,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:02:37.463708\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:02:37'),(41,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:27:49.194527\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:27:49'),(42,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:45:30.756087\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:45:31'),(43,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:54:58.664880\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:54:59'),(44,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 14:03:40'),(45,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T14:11:50.645286\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 14:11:51'),(46,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 14:39:33'),(47,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:07:07'),(48,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T15:25:30.927911\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:25:31'),(49,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:27:03'),(50,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:30:37'),(51,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:31:09'),(52,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T15:42:27.890611\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 15:42:28'),(53,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T15:43:31.023408\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 15:43:31'),(54,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:06:20.772339\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 16:06:21'),(55,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:11:53.960768\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:11:54'),(56,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:17:31.799489\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:17:32'),(57,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:19:38.424903\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 16:19:38'),(58,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:19:45.341038\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 16:19:45'),(59,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:20:33.751276\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:20:34'),(60,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:24:58.742760\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:24:59'),(61,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:25:30.325344\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:25:30'),(62,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:25:52.333122\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:25:52'),(63,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:28:31.587932\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:28:32'),(64,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:50:45.128748\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:50:45'),(65,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T17:00:28.920183\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 17:00:29'),(66,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T02:32:52.027529\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 02:32:52'),(67,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T02:32:57.391815\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 02:32:57'),(68,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T02:33:47.806436\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 02:33:48'),(69,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T03:04:39.602611\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 03:04:40'),(70,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T05:08:56.202184\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 05:08:56'),(71,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T08:20:26.447056\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 08:20:26'),(72,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:11:33.025122\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:11:33'),(73,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:12:02.147448\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:12:02'),(74,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:15:16.471532\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:15:16'),(75,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:16:04.065219\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:16:04'),(76,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:45:46.633356\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 09:45:47'),(77,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T12:01:51.115960\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-23 12:01:51'),(78,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T12:57:09.946598\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-23 12:57:10'),(79,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T00:45:08.987149\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 00:45:09'),(80,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T00:53:17.884940\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 00:53:18'),(81,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T00:54:00.230674\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 00:54:00'),(82,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T00:57:00.259493\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 00:57:00'),(83,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T01:20:12.523596\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 01:20:13'),(84,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T01:23:59.908636\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 01:24:00'),(85,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T01:26:58.789479\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 01:26:59'),(86,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T01:41:23.363578\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 01:41:23'),(87,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T01:58:01.785301\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 01:58:02'),(88,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T02:21:07.668228\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 02:21:08'),(89,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T07:42:56.906695\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 07:42:57'),(90,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T07:44:38.214261\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 07:44:38'),(91,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T07:49:12.931980\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 07:49:13'),(92,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T08:02:51.171289\"}','127.0.0.1','python-requests/2.32.3','2025-06-24 08:02:51'),(93,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T08:03:01.192008\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 08:03:01'),(94,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T08:12:04.502349\"}','127.0.0.1','python-requests/2.32.3','2025-06-24 08:12:05'),(95,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T08:22:57.164452\"}','127.0.0.1','python-requests/2.32.3','2025-06-24 08:22:57'),(96,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T08:31:06.269345\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 08:31:06'),(97,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T09:13:38.514773\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 09:13:39'),(98,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T09:22:06.360410\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 09:22:06'),(99,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-24T10:44:17.708719\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-24 10:44:18'),(100,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T04:02:23.691731\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 04:02:24'),(101,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T06:16:57.107724\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 06:16:57'),(102,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T07:56:47.678473\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 07:56:48'),(103,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:00:05.752854\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:00:06'),(104,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:00:12.154266\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:00:12'),(105,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:02:14.690754\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 08:02:15'),(106,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:03:03.238005\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:03:03'),(107,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:03:03.995006\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:03:04'),(108,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:03:37.021078\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:03:37'),(109,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:04:08.437674\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:04:08'),(110,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:05:03.480532\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:05:03'),(111,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:09:42.387869\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:09:42'),(112,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:17:22.957843\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:17:23'),(113,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:19:25.253445\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 08:19:25'),(114,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:23:58.863586\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:23:59'),(115,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:25:49.484890\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:25:49'),(116,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:27:48.237487\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:27:48'),(117,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:30:59.902232\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:31:00'),(118,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:31:34.526821\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:31:35'),(119,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:32:17.947636\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:32:18'),(120,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:33:00.534525\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:33:01'),(121,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:33:41.996016\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:33:42'),(122,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:34:18.396371\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:34:18'),(123,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:36:54.128361\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 08:36:54'),(124,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:46:35.368621\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 08:46:35'),(125,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:47:12.111777\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 08:47:12'),(126,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:48:12.528817\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 08:48:13'),(127,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T08:53:57.972408\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 08:53:58'),(128,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T09:29:18.778256\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 09:29:19'),(129,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T09:37:56.396173\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 09:37:56'),(130,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T09:38:16.159607\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 09:38:16'),(131,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T09:38:40.662196\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 09:38:41'),(132,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T09:38:41.996352\"}','127.0.0.1','python-requests/2.32.3','2025-06-26 09:38:42'),(133,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T09:43:49.598048\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 09:43:50'),(134,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T10:21:15.613121\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-26 10:21:16'),(135,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T12:24:55.382862\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 12:24:55'),(136,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T16:20:42.078049\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 16:20:42'),(137,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-26T17:02:02.922099\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 17:02:03'),(138,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T00:26:07.931590\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 00:26:08'),(139,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T03:17:49.785276\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 03:17:50'),(140,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T03:37:42.373771\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 03:37:42'),(141,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T03:46:15.685513\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 03:46:16'),(142,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T03:57:25.056315\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 03:57:25'),(143,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T08:00:51.747580\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 08:00:52'),(144,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T08:53:01.857939\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 08:53:02'),(145,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-27T10:12:05.362684\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-27 10:12:05'),(146,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T02:35:47.018110\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 02:35:47'),(147,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T02:45:52.687422\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 02:45:53'),(148,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T08:22:53.515329\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 08:22:54'),(149,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T08:41:25.468704\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 08:41:25'),(150,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T10:30:05.356940\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 10:30:05'),(151,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T10:32:53.851225\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 10:32:54'),(152,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T10:35:43.676019\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 10:35:44'),(153,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T10:57:41.619498\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 10:57:42'),(154,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T11:06:36.422441\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 11:06:36'),(155,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T11:11:20.661125\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 11:11:21'),(156,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T11:13:46.999137\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 11:13:47'),(157,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T11:16:42.482890\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 11:16:42'),(158,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:29:59.098714\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 15:29:59'),(159,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:48:26.857935\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 15:48:27'),(160,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:48:43.476936\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:48:43'),(161,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:49:11.684729\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:49:12'),(162,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:49:42.170093\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:49:42'),(163,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:55:37.722908\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:55:38'),(164,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:57:11.132066\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:57:11'),(165,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:58:23.010741\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:58:23'),(166,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:59:07.037568\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 15:59:07'),(167,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T15:59:50.768163\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 15:59:51'),(168,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:04:14.951804\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:04:15'),(169,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:05:29.018103\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:05:29'),(170,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:06:10.820804\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:06:11'),(171,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:06:51.581697\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 16:06:52'),(172,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:07:06.692904\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:07:07'),(173,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:07:44.292687\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:07:44'),(174,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:08:43.512804\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:08:44'),(175,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:10:56.964866\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:10:57'),(176,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:13:48.578714\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:13:49'),(177,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:15:13.203124\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:15:13'),(178,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:15:42.395138\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:15:42'),(179,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:17:38.414700\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:17:38'),(180,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:22:09.479558\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:22:09'),(181,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:24:44.347738\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:24:44'),(182,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:24:57.991033\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:24:58'),(183,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:25:11.317272\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:25:11'),(184,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:25:29.097254\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:25:29'),(185,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:28:07.692046\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:28:08'),(186,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:28:39.528902\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:28:40'),(187,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:29:16.570820\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:29:17'),(188,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:29:45.129789\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 16:29:45'),(189,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T16:37:47.205165\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 16:37:47'),(190,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T17:10:48.598992\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 17:10:49'),(191,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T17:14:25.965719\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 17:14:26'),(192,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T17:17:20.935892\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 17:17:21'),(193,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T17:17:38.676029\"}','127.0.0.1','python-requests/2.31.0','2025-06-28 17:17:39'),(194,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T17:49:44.323117\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 17:49:44'),(195,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:15:46.368961\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:15:46'),(196,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:16:42.992255\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:16:43'),(197,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:27:16.047481\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:27:16'),(198,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:32:33.618689\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:32:34'),(199,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:36:17.735296\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:36:18'),(200,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:38:39.012832\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:38:39'),(201,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:45:19.280898\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:45:19'),(202,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T18:47:33.166330\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 18:47:33'),(203,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-28T19:04:45.279613\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 19:04:45'),(204,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T00:43:33.224127\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 00:43:33'),(205,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:00:06.152012\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 01:00:06'),(206,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:00:56.611896\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 01:00:57'),(207,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:10:57.060815\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 01:10:57'),(208,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:42:23.641848\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 01:42:24'),(209,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:44:25.696393\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 01:44:26'),(210,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:48:42.347501\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 01:48:42'),(211,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T01:55:42.054744\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 01:55:42'),(212,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T02:04:59.734891\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 02:05:00'),(213,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T02:41:38.060507\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 02:41:38'),(214,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T02:49:27.092411\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 02:49:27'),(215,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T02:57:40.657457\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 02:57:41'),(216,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T03:11:23.687117\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 03:11:24'),(217,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T03:18:59.372635\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 03:18:59'),(218,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T04:47:14.431537\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 04:47:14'),(219,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:14:27.374360\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 05:14:27'),(220,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:15:54.094396\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 05:15:54'),(221,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:22:01.617446\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 05:22:02'),(222,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:24:58.577291\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 05:24:59'),(223,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:28:35.338201\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 05:28:35'),(224,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:30:09.641578\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 05:30:10'),(225,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T05:30:13.626398\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 05:30:14'),(226,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T11:08:08.874328\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 11:08:09'),(227,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T11:40:02.044157\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 11:40:02'),(228,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T11:52:52.454082\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 11:52:52'),(229,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T11:54:16.186941\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 11:54:16'),(230,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T11:57:14.816008\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 11:57:15'),(231,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T12:30:30.299720\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 12:30:30'),(232,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T12:51:29.466912\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 12:51:29'),(233,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T12:53:35.500265\"}','127.0.0.1','python-requests/2.31.0','2025-06-29 12:53:36'),(234,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-29T12:58:46.298683\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 12:58:46');
/*!40000 ALTER TABLE `user_action_logs` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-29 21:13:40

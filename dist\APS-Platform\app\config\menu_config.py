"""
菜单配置文件 - 定义系统中所有菜单项
这个文件用于替代数据库中的menu_settings表，使菜单更容易配置和修改
"""

# 菜单配置版本 - 每次修改菜单后请增加此版本号
MENU_CONFIG_VERSION = "3.0.1750673616"

# 菜单ID-权限码映射关系
# 这些ID用于权限管理，必须保持唯一，与之前数据库中的ID保持一致
MENU_ID_MAP = {
    # 主菜单项
    'production': 1,
    'orders': 2,
    # 'forecast': 3,  # 已移除FCST分析菜单
    'resources': 4,
    'wip': 5,
    'system': 6,
    
    # 生产管理子菜单
    'production_semi_auto': 7,
    'production_auto': 8,
    'production_algorithm': 9,

    'production_priority': 25,  # 优先级设定父菜单
    'production_device_priority': 31,  # 产品优先级配置
    'production_lot_priority': 32,  # 批次优先级配置
    
    'production_preview': 33,  # 排产预览父菜单
    'production_wait_lots': 34,  # 待排产批次
    'production_done_lots': 35,  # 已排产批次
    
    'orders_optimized_parser': 36,  # 优化解析器（移到订单管理下）
    'orders_horizontal_info': 37,  # 横向信息
    'orders_summary_preview': 38,  # 汇总预览
    
    # 订单管理子菜单
    'orders_semi_auto': 10,
    'orders_auto': 11,
    
    # 预测分析子菜单 - 已移除
    # 'forecast_capacity': 12,
    # 'forecast_tracking': 13,
    
    # 资源管理子菜单
    'resources_hardware': 14,  # 设备资源 → eqp_status表
    'resources_test_specs': 15,  # 测试规范 → et_ft_test_spec表
    'resources_test_hardware': 16,  # 套件资源 → tcc_inv表
    'resources_uph': 17,  # UPH → et_uph_eqp表
    'resources_product_cycle': 18,  # 产品周期 → ct表
    'resources_recipe_file': 39,  # 设备配方 → et_recipe_file表
    
    # WIP跟踪子菜单
    'wip_by_product': 19,
    'wip_by_batch': 20,
    'wip_lot_management': 40,  # WIP批次管理 → wip_lot表
    
    # 系统管理子菜单
    'system_users': 21,
    'system_logs': 22,
    'system_ai_assistant': 24,
    'system_settings': 26,  # 系统设置菜单
    'system_performance': 28,  # 性能监控菜单
    'system_data_source': 29,
    'system_ai_config': 30
}

# 反向映射，用于从ID获取菜单码
ID_TO_MENU_CODE = {v: k for k, v in MENU_ID_MAP.items()}

# 菜单结构配置
MENU_CONFIG = [
    {
        'code': 'production',
        'name': '排产管理',
        'icon': 'fas fa-industry',
        'route': None,
        'order': 1,
        'children': [
            {
                'code': 'production_semi_auto',
                'name': '手动排产',
                'icon': 'fas fa-clock',
                'route': '/production/semi-auto',
                'order': 1
            },
            {
                'code': 'production_auto',
                'name': '自动排产',
                'icon': 'fas fa-robot',
                'route': '/production/auto',
                'order': 2
            },
            {
                'code': 'production_preview',
                'name': '排产预览',
                'icon': 'fas fa-cog',
                'route': None,
                'order': 3,
                'children': [
                    {
                        'code': 'production_wait_lots',
                        'name': '待排产批次管理',
                        'icon': 'fas fa-clock',
                        'route': '/api/v3/universal/et_wait_lot',
                        'order': 1
                    },
                    {
                        'code': 'production_done_lots',
                        'name': '已排产批次管理',
                        'icon': 'fas fa-check',
                        'route': '/api/v3/universal/lotprioritydone',
                        'order': 2
                    }
                ]
            },
            {
                'code': 'production_algorithm',
                'name': '算法设置',
                'icon': 'fas fa-calculator',
                'route': '/production/algorithm',
                'order': 4
            },
            {
                'code': 'production_priority',
                'name': '优先级设定',
                'icon': 'fas fa-sort-amount-up',
                'route': None,
                'order': 5,
                'children': [
                    {
                        'code': 'production_device_priority',
                        'name': '产品优先级配置',
                        'icon': 'fas fa-microchip',
                        'route': '/api/v3/universal/devicepriorityconfig',
                        'order': 1
                    },
                    {
                        'code': 'production_lot_priority',
                        'name': '批次优先级配置',
                        'icon': 'fas fa-boxes',
                        'route': '/api/v3/universal/lotpriorityconfig',
                        'order': 2
                    }
                ]
            }
        ]
    },
    {
        'code': 'orders',
        'name': '订单管理',
        'icon': 'fas fa-shopping-cart',
        'route': None,
        'order': 2,
        'children': [
            {
                'code': 'orders_semi_auto',
                'name': '手动导入订单',
                'icon': 'fas fa-upload',
                'route': '/orders/semi-auto',
                'order': 1
            },
            {
                'code': 'orders_auto',
                'name': '订单处理中心',
                'icon': 'fas fa-rocket',
                'route': '/orders/auto',
                'order': 2
            },
            {
                'code': 'orders_summary_preview',
                'name': '汇总预览',
                'icon': 'fas fa-eye',
                'route': '/orders/summary-preview',
                'order': 3
            },
        ]
    },
    # FCST分析菜单已移除
    {
        'code': 'resources',
        'name': '资源管理',
        'icon': 'fas fa-cogs',
        'route': None,
        'order': 3,  # 顺序调整为3（原来是4）
        'children': [
            {
                'code': 'resources_hardware',
                'name': '设备状态管理',
                'icon': 'fas fa-microchip',
                'route': '/api/v3/universal/eqp_status',
                'order': 1
            },
            {
                'code': 'resources_test_specs',
                'name': '测试规格管理',
                'icon': 'fas fa-file-alt',
                'route': '/api/v3/universal/et_ft_test_spec',
                'order': 2
            },
            {
                'code': 'resources_test_hardware',
                'name': '套件资源管理',
                'icon': 'fas fa-microscope',
                'route': '/api/v3/universal/tcc_inv',
                'order': 3
            },
            {
                'code': 'resources_uph',
                'name': 'UPH设备管理',
                'icon': 'fas fa-tachometer-alt',
                'route': '/api/v3/universal/et_uph_eqp',
                'order': 4
            },
            {
                'code': 'resources_product_cycle',
                'name': '产品周期管理',
                'icon': 'fas fa-clock',
                'route': '/api/v3/universal/ct',
                'order': 5
            },
            {
                'code': 'resources_recipe_file',
                'name': '设备配方管理',
                'icon': 'fas fa-cogs',
                'route': '/api/v3/universal/et_recipe_file',
                'order': 6
            }
        ]
    },
    {
        'code': 'wip',
        'name': 'WIP跟踪',
        'icon': 'fas fa-tasks',
        'route': None,
        'order': 4,  # 顺序调整为4（原来是5）
        'children': [
            {
                'code': 'wip_by_product',
                'name': '按产品',
                'icon': 'fas fa-box',
                'route': '/wip/by-product',
                'order': 1
            },
            {
                'code': 'wip_by_batch',
                'name': '按批次',
                'icon': 'fas fa-boxes',
                'route': '/wip/by-batch',
                'order': 2
            },
            {
                'code': 'wip_lot_management',
                'name': 'WIP批次管理',
                'icon': 'fas fa-list-alt',
                'route': '/api/v3/universal/wip_lot',
                'order': 3
            }
        ]
    },
    {
        'code': 'system',
        'name': '系统管理',
        'icon': 'fas fa-tools',
        'route': None,
        'order': 5,  # 顺序调整为5（原来是6）
        'children': [
            {
                'code': 'system_users',
                'name': '用户管理',
                'icon': 'fas fa-users',
                'route': '/users',
                'order': 1
            },
            {
                'code': 'system_logs',
                'name': '系统日志',
                'icon': 'fas fa-history',
                'route': '/system/logs',
                'order': 2
            },
            {
                'code': 'system_ai_assistant',
                'name': 'AI助手',
                'icon': 'fas fa-robot',
                'route': '/ai-assistant',
                'order': 3
            },
            {
                'code': 'system_settings',
                'name': '系统设置',
                'icon': 'fas fa-cog',
                'route': None,
                'order': 4,
                'children': [
                    {
                        'code': 'system_performance',
                        'name': '性能监控',
                        'icon': 'fas fa-chart-line',
                        'route': '/system/performance',
                        'order': 1
                    },
                    {
                        'code': 'system_data_source',
                        'name': '数据库配置',
                        'icon': 'fas fa-database',
                        'route': '/system/database-config',
                        'order': 2
                    },
                    {
                        'code': 'system_ai_config',
                        'name': 'AI助手设置',
                        'icon': 'fas fa-robot',
                        'route': '/system/settings',
                        'order': 3
                    }
                ]
            }
        ]
    }
]

# 扁平化菜单列表，方便通过code或id查找
def _flatten_menu(menu_list):
    result = []
    for menu in menu_list:
        menu_copy = menu.copy()
        children = menu_copy.pop('children', [])
        result.append(menu_copy)
        if children:
            result.extend(_flatten_menu(children))
    return result

FLAT_MENU_LIST = _flatten_menu(MENU_CONFIG)

# 通过code获取菜单项
def get_menu_by_code(code):
    for menu in FLAT_MENU_LIST:
        if menu['code'] == code:
            return menu
    return None

# 通过id获取菜单项
def get_menu_by_id(menu_id):
    code = ID_TO_MENU_CODE.get(menu_id)
    if code:
        return get_menu_by_code(code)
    return None

# 获取所有菜单ID
def get_all_menu_ids():
    return list(MENU_ID_MAP.values()) 
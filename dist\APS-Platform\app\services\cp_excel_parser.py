#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CP订单Excel解析器
专门用于解析CP模板格式的Excel文件，确保100%解析成功
"""

import os
import json
import logging
import pandas as pd
import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class CpFieldMapping:
    """CP字段映射配置"""
    field_name: str
    search_keywords: List[str]
    required: bool = False
    data_type: str = 'str'

class CpExcelParser:
    """CP订单Excel解析器"""
    
    def __init__(self):
        """初始化CP解析器"""
        self.field_mappings = {
            # 核心必需字段
            'order_number': CpFieldMapping(
                field_name='订单号',
                search_keywords=['订单号', 'Order', 'order'],
                required=True
            ),
            'product_name': CpFieldMapping(
                field_name='产品名称',
                search_keywords=['产品名称', '产品', 'Product', 'product'],
                required=False
            ),
            'chip_name': CpFieldMapping(
                field_name='芯片名称',
                search_keywords=['芯片名称', '芯片', 'Chip', 'chip'],
                required=True
            ),
            'chip_batch': CpFieldMapping(
                field_name='芯片批号',
                search_keywords=['芯片批号', '批号', 'Batch', 'batch'],
                required=False
            ),
            'processing_pieces': CpFieldMapping(
                field_name='加工片数',
                search_keywords=['加工', '片数', 'pieces', 'Pieces'],
                required=False,
                data_type='int'
            ),
            'finished_model': CpFieldMapping(
                field_name='成品型号',
                search_keywords=['成品型号', '成品', '型号', 'Model', 'model'],
                required=False
            ),
            'wafer_numbers': CpFieldMapping(
                field_name='片号',
                search_keywords=['片号', 'wafer', 'Wafer'],
                required=False
            ),
            'cp_mapping': CpFieldMapping(
                field_name='CP Mapping',
                search_keywords=['CP Mapping', 'cp mapping', 'mapping', 'Mapping'],
                required=False
            ),
            'package_method': CpFieldMapping(
                field_name='包装方式',
                search_keywords=['包装方式', '包装', 'Package', 'package'],
                required=False
            ),
            'process_step': CpFieldMapping(
                field_name='工序',
                search_keywords=['工序', 'Step', 'step', 'Process', 'process'],
                required=False
            ),
            'shipping_address': CpFieldMapping(
                field_name='发货地址',
                search_keywords=['发货地址', '发货', '地址', 'Address', 'address'],
                required=False
            )
        }
        
        logger.info("CP订单解析器初始化完成")
    
    def is_cp_template(self, file_path: str) -> bool:
        """判断是否为CP模板文件"""
        try:
            # 从文件名判断
            file_name = os.path.basename(file_path).lower()
            if 'cp' in file_name and ('excel' in file_name or 'template' in file_name):
                return True
            
            # 从文件内容判断
            engine = 'openpyxl' if file_path.endswith('.xlsx') else 'xlrd'
            df = pd.read_excel(file_path, engine=engine, header=None)
            
            # 检查文件尺寸 (CP模板通常是20行x11列)
            rows, cols = df.shape
            if rows == 20 and cols == 11:
                return True
            
            # 检查是否包含CP特有的字段
            content_text = ''
            for i in range(min(15, rows)):
                row_text = ' '.join(str(cell) for cell in df.iloc[i] if pd.notna(cell))
                content_text += row_text.lower() + ' '
            
            cp_indicators = ['cp mapping', '芯片批号', '成品型号', '加工属性']
            cp_count = sum(1 for indicator in cp_indicators if indicator in content_text)
            
            return cp_count >= 2
            
        except Exception as e:
            logger.warning(f"判断CP模板失败: {e}")
            return False
    
    def parse_cp_file(self, file_path: str) -> Dict[str, Any]:
        """解析CP模板文件"""
        try:
            logger.info(f"开始解析CP文件: {file_path}")
            
            # 读取Excel文件
            engine = 'openpyxl' if file_path.endswith('.xlsx') else 'xlrd'
            df = pd.read_excel(file_path, engine=engine, header=None)
            logger.info(f"CP文件尺寸: {df.shape[0]} 行 x {df.shape[1]} 列")
            
            # 1. 提取横向信息 (前10行)
            horizontal_info = self._extract_cp_horizontal_info(df)
            
            # 2. 定位表头和数据行
            header_row_idx = self._find_cp_header_row(df)
            if header_row_idx is None:
                return {
                    'status': 'error',
                    'message': 'CP模板：无法找到有效的表头行',
                    'data': [],
                    'horizontal_info': horizontal_info
                }
            
            # 3. 解析纵向数据
            vertical_data = self._extract_cp_vertical_data(df, header_row_idx, horizontal_info)
            
            if not vertical_data:
                return {
                    'status': 'error',
                    'message': 'CP模板：未能提取到有效的订单数据',
                    'data': [],
                    'horizontal_info': horizontal_info
                }
            
            return {
                'status': 'success',
                'message': f'CP模板解析成功，提取 {len(vertical_data)} 条订单数据',
                'data': vertical_data,
                'file_name': os.path.basename(file_path),
                'horizontal_info': horizontal_info,
                'template_type': 'CP',
                'extraction_method': 'cp_parser'
            }
            
        except Exception as e:
            logger.error(f"解析CP文件失败 {file_path}: {e}")
            return {
                'status': 'error',
                'message': f'CP模板解析失败: {str(e)}',
                'data': [],
                'file_name': os.path.basename(file_path) if file_path else ''
            }
    
    def _extract_cp_horizontal_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """提取CP模板的横向信息"""
        horizontal_info = {}
        
        try:
            rows, cols = df.shape
            search_limit = min(10, rows)  # CP模板的横向信息在前10行
            
            for row_idx in range(search_limit):
                row_data = df.iloc[row_idx]
                
                for col_idx, cell_value in enumerate(row_data):
                    if pd.notna(cell_value):
                        cell_text = str(cell_value).strip()
                        
                        # 编号
                        if cell_text == '编号：' and col_idx + 1 < len(row_data):
                            next_cell = row_data.iloc[col_idx + 1]
                            if pd.notna(next_cell):
                                horizontal_info['编号'] = str(next_cell).strip()
                        
                        # 加工属性
                        elif cell_text == '加工属性' and col_idx + 1 < len(row_data):
                            next_cell = row_data.iloc[col_idx + 1]
                            if pd.notna(next_cell):
                                horizontal_info['加工属性'] = str(next_cell).strip()
                        
                        # 日期
                        elif cell_text == '日期：' and col_idx + 1 < len(row_data):
                            next_cell = row_data.iloc[col_idx + 1]
                            if pd.notna(next_cell):
                                horizontal_info['日期'] = str(next_cell).strip()
                        
                        # 加工承揽商
                        elif cell_text == '加工承揽商：' and col_idx + 1 < len(row_data):
                            next_cell = row_data.iloc[col_idx + 1]
                            if pd.notna(next_cell):
                                horizontal_info['加工承揽商'] = str(next_cell).strip()
                        
                        # 加工委托方
                        elif cell_text == '加工委托方：' and col_idx + 1 < len(row_data):
                            next_cell = row_data.iloc[col_idx + 1]
                            if pd.notna(next_cell):
                                horizontal_info['加工委托方'] = str(next_cell).strip()
                        
                        # 联系人
                        elif cell_text == '联系人：' and col_idx + 1 < len(row_data):
                            next_cell = row_data.iloc[col_idx + 1]
                            if pd.notna(next_cell):
                                if col_idx <= 3:  # 左侧的联系人是承揽商的
                                    horizontal_info['承揽商联系人'] = str(next_cell).strip()
                                else:  # 右侧的联系人是委托方的
                                    horizontal_info['委托方联系人'] = str(next_cell).strip()
            
            logger.info(f"CP横向信息提取完成，找到 {len(horizontal_info)} 个字段")
            return horizontal_info
            
        except Exception as e:
            logger.warning(f"CP横向信息提取失败: {e}")
            return {}
    
    def _find_cp_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """查找CP模板的表头行"""
        rows, cols = df.shape
        
        # CP模板的表头通常在第11行 (索引10)
        for row_idx in range(8, min(15, rows)):
            row_data = df.iloc[row_idx]
            row_text = ' '.join(str(cell).lower() for cell in row_data if pd.notna(cell))
            
            # 检查是否包含订单号关键字
            if '订单号' in row_text:
                logger.info(f"CP模板表头行发现在第 {row_idx + 1} 行")
                return row_idx
        
        logger.warning("CP模板：未找到包含'订单号'的表头行")
        return None
    
    def _extract_cp_vertical_data(self, df: pd.DataFrame, header_row_idx: int, horizontal_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取CP模板的纵向数据"""
        vertical_data = []
        
        try:
            # 获取表头信息
            header_row = df.iloc[header_row_idx]
            
            # 建立列映射
            column_mapping = {}
            for col_idx, header_cell in enumerate(header_row):
                if pd.notna(header_cell):
                    header_text = str(header_cell).strip()
                    
                    # 直接映射已知的字段
                    field_map = {
                        '订单号': '订单号',
                        '产品名称': '产品名称',
                        '芯片名称': '芯片名称',
                        '芯片批号': '芯片批号',
                        '加工': '加工片数',  # 第11行是"加工"，第12行是"片数"
                        '成品型号': '成品型号',
                        '片号': '片号',
                        'CP Mapping': 'CP Mapping',
                        '包装方式': '包装方式',
                        '工序': '工序',
                        '发货地址': '发货地址'
                    }
                    
                    if header_text in field_map:
                        column_mapping[col_idx] = field_map[header_text]
            
            # 特殊处理：检查第12行是否有"片数"
            if header_row_idx + 1 < df.shape[0]:
                next_row = df.iloc[header_row_idx + 1]
                for col_idx, cell in enumerate(next_row):
                    if pd.notna(cell) and str(cell).strip() == '片数':
                        if col_idx in column_mapping and column_mapping[col_idx] == '加工片数':
                            # 确认这一列确实是加工片数
                            pass
            
            logger.info(f"CP列映射: {column_mapping}")
            
            # 数据行从表头下面开始
            data_start_row = header_row_idx + 2  # 跳过表头和可能的"片数"行
            rows, cols = df.shape
            
            for row_idx in range(data_start_row, rows):
                row_data = df.iloc[row_idx]
                
                # 检查是否为有效的数据行
                non_empty_count = sum(1 for cell in row_data if pd.notna(cell) and str(cell).strip())
                if non_empty_count < 3:  # 至少要有3个非空字段
                    continue
                
                # 提取数据
                record = {}
                
                for col_idx, field_name in column_mapping.items():
                    if col_idx < len(row_data):
                        cell_value = row_data.iloc[col_idx]
                        if pd.notna(cell_value):
                            record[field_name] = str(cell_value).strip()
                        else:
                            record[field_name] = ''
                
                # 必须有订单号才认为是有效记录
                if record.get('订单号', '').strip():
                    # 添加横向信息
                    record.update(horizontal_info)
                    
                    # 添加元数据
                    record['数据行号'] = row_idx + 1
                    record['导入时间'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    record['模板类型'] = 'CP'
                    
                    # 自动分类
                    process_step = record.get('工序', '')
                    if 'CP1' in process_step.upper():
                        record['分类结果'] = 'CP1'
                    elif 'CP2' in process_step.upper():
                        record['分类结果'] = 'CP2'
                    elif 'CP3' in process_step.upper():
                        record['分类结果'] = 'CP3'
                    else:
                        record['分类结果'] = '未知'
                    
                    vertical_data.append(record)
                    logger.debug(f"CP数据提取: 第{row_idx + 1}行 - {record.get('订单号', 'N/A')}")
            
            logger.info(f"CP纵向数据提取完成，共 {len(vertical_data)} 条记录")
            return vertical_data
            
        except Exception as e:
            logger.error(f"CP纵向数据提取失败: {e}")
            return []
    
    def batch_parse_cp_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """批量解析CP文件"""
        results = {
            'status': 'success',
            'processed_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'cp1_records': 0,
            'cp2_records': 0,
            'cp3_records': 0,
            'unknown_records': 0,
            'file_results': [],
            'cp_data': []
        }
        
        for file_path in file_paths:
            # 只处理CP模板文件
            if not self.is_cp_template(file_path):
                continue
            
            logger.info(f"批量处理CP文件: {os.path.basename(file_path)}")
            parse_result = self.parse_cp_file(file_path)
            
            file_result = {
                'file_name': os.path.basename(file_path),
                'status': parse_result['status'],
                'message': parse_result['message'],
                'records': len(parse_result['data']) if parse_result['status'] == 'success' else 0
            }
            
            if parse_result['status'] == 'success':
                results['processed_files'] += 1
                results['total_records'] += len(parse_result['data'])
                
                # 按分类统计
                for record in parse_result['data']:
                    classification = record.get('分类结果', '未知')
                    if classification == 'CP1':
                        results['cp1_records'] += 1
                    elif classification == 'CP2':
                        results['cp2_records'] += 1
                    elif classification == 'CP3':
                        results['cp3_records'] += 1
                    else:
                        results['unknown_records'] += 1
                
                results['cp_data'].extend(parse_result['data'])
            else:
                results['failed_files'] += 1
            
            results['file_results'].append(file_result)
        
        return results 
"""
待排产批次数据模型
对应ET_WAIT_LOT.xlsx文件
"""
from app import db
from datetime import datetime

class WaitLot(db.Model):
    """待排产批次模型 - 对应ET_WAIT_LOT表"""
    __tablename__ = 'et_wait_lot'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lot_id = db.Column(db.String(50), nullable=False, comment='批次号')
    device = db.Column(db.String(100), nullable=True, comment='产品名称')
    stage = db.Column(db.String(20), nullable=True, comment='工序')
    quantity = db.Column(db.Integer, nullable=True, comment='数量')
    pkg_pn = db.Column(db.String(100), nullable=True, comment='封装料号')
    chip_id = db.Column(db.String(100), nullable=True, comment='芯片ID')
    priority = db.Column(db.Integer, nullable=True, comment='优先级')
    wait_time = db.Column(db.DateTime, nullable=True, comment='等待时间')
    expected_start_time = db.Column(db.DateTime, nullable=True, comment='预计开始时间')
    expected_end_time = db.Column(db.DateTime, nullable=True, comment='预计结束时间')
    equipment_id = db.Column(db.String(50), nullable=True, comment='设备ID')
    status = db.Column(db.String(20), nullable=True, default='WAITING', comment='状态')
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'lot_id': self.lot_id,
            'device': self.device,
            'stage': self.stage,
            'quantity': self.quantity,
            'pkg_pn': self.pkg_pn,
            'chip_id': self.chip_id,
            'priority': self.priority,
            'wait_time': self.wait_time.isoformat() if self.wait_time else None,
            'expected_start_time': self.expected_start_time.isoformat() if self.expected_start_time else None,
            'expected_end_time': self.expected_end_time.isoformat() if self.expected_end_time else None,
            'equipment_id': self.equipment_id,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<WaitLot {self.lot_id}>' 
#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n调试设备状态数据和字段映射问题\n检查EQP_STATUS表中的STATUS字段实际值\n\"\"\"\n\nimport sys\nimport os\nimport logging\nfrom datetime import datetime\n\n# 添加项目根目录到Python路径\nsys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))\n\n# 配置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s'\n)\n\nlogger = logging.getLogger(__name__)\n\ndef debug_equipment_status():\n    \"\"\"调试设备状态数据\"\"\"\n    try:\n        logger.info(\"🔍 开始调试设备状态数据...\")\n        \n        # 导入数据管理器\n        from app.services.data_source_manager import DataSourceManager\n        \n        # 创建数据管理器实例\n        data_manager = DataSourceManager()\n        \n        # 获取设备状态数据\n        equipment_result = data_manager.get_eqp_status_data()\n        all_equipment = equipment_result.get('data', [])\n        \n        logger.info(f\"📊 总设备数: {len(all_equipment)}\")\n        \n        # 分析STATUS字段的所有可能值\n        status_values = {}\n        status_types = {}\n        \n        for i, eqp in enumerate(all_equipment[:10]):  # 只显示前10条记录\n            status = eqp.get('STATUS')\n            status_str = str(status)\n            \n            # 统计状态值\n            if status_str not in status_values:\n                status_values[status_str] = 0\n            status_values[status_str] += 1\n            \n            # 统计状态类型\n            status_type = type(status).__name__\n            if status_type not in status_types:\n                status_types[status_type] = 0\n            status_types[status_type] += 1\n            \n            logger.info(f\"设备 {i+1}: HANDLER_ID={eqp.get('HANDLER_ID', 'N/A')}, STATUS={status} (类型: {status_type})\")\n            logger.info(f\"       DEVICE={eqp.get('DEVICE', 'N/A')}, STAGE={eqp.get('STAGE', 'N/A')}\")\n            logger.info(f\"       HANDLER_TYPE={eqp.get('HANDLER_TYPE', 'N/A')}\")\n            \n        # 显示状态值统计\n        logger.info(\"\\n📈 STATUS字段值统计:\")\n        for status_val, count in sorted(status_values.items()):\n            logger.info(f\"   '{status_val}': {count} 台设备\")\n            \n        # 显示状态类型统计\n        logger.info(\"\\n📊 STATUS字段类型统计:\")\n        for status_type, count in sorted(status_types.items()):\n            logger.info(f\"   {status_type}: {count} 台设备\")\n            \n        # 测试状态处理逻辑\n        logger.info(\"\\n🧪 测试状态处理逻辑:\")\n        available_count = 0\n        unavailable_count = 0\n        \n        for eqp in all_equipment:\n            status = eqp.get('STATUS')\n            \n            # 使用与real_scheduling_service.py相同的逻辑\n            if isinstance(status, (int, float)):\n                status_code = int(status)\n            elif isinstance(status, str):\n                try:\n                    status_code = int(status) if status.isdigit() else None\n                except ValueError:\n                    # 兼容旧的字符串状态值\n                    if status.upper() in ['IDLE', 'READY', 'ONLINE']:\n                        status_code = 0  # 待机\n                    elif status.upper() in ['RUN', 'RUNNING', 'SETUPRUN']:\n                        status_code = 1  # 运行\n                    elif status.upper() in ['DOWN', 'FAULT', 'ERROR']:\n                        status_code = 2  # 故障\n                    else:\n                        status_code = 0  # 默认为待机\n            else:\n                status_code = 0  # 默认为待机\n            \n            # 只选择待机(0)和运行中(1)的设备，排除故障(2)设备\n            if status_code in [0, 1]:\n                available_count += 1\n            else:\n                unavailable_count += 1\n                \n        logger.info(f\"✅ 可用设备数: {available_count}\")\n        logger.info(f\"❌ 不可用设备数: {unavailable_count}\")\n        \n        # 如果没有可用设备，显示详细原因\n        if available_count == 0:\n            logger.warning(\"⚠️ 没有可用设备，详细分析:\")\n            for eqp in all_equipment[:5]:  # 显示前5台设备的详细信息\n                status = eqp.get('STATUS')\n                handler_id = eqp.get('HANDLER_ID', 'N/A')\n                device = eqp.get('DEVICE', 'N/A')\n                \n                logger.warning(f\"   设备 {handler_id}: STATUS={status}, DEVICE={device}\")\n                logger.warning(f\"        原始状态值: '{status}' (类型: {type(status).__name__})\")\n                \n        logger.info(\"✅ 设备状态调试完成\")\n        \n    except Exception as e:\n        logger.error(f\"❌ 调试设备状态失败: {e}\")\n        import traceback\n        logger.error(traceback.format_exc())\n\nif __name__ == \"__main__\":\n    debug_equipment_status() 
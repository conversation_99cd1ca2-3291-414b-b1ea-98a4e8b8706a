from flask import Blueprint

# 创建系统管理API蓝图
system_bp = Blueprint('system_v2', __name__, url_prefix='/api/v2/system')

# 导入子模块API
from .database_config import db_config_bp
system_bp.register_blueprint(db_config_bp, url_prefix='/database-config')

# 导入路由
from . import routes

# 注册dashboard子蓝图
from .dashboard import dashboard_bp
system_bp.register_blueprint(dashboard_bp, url_prefix='/dashboard')

# 注册监控子蓝图
from .monitoring import monitoring_bp
system_bp.register_blueprint(monitoring_bp) 
# 注册缺失的监控API
from .missing_monitoring import missing_monitoring_bp
system_bp.register_blueprint(missing_monitoring_bp, url_prefix='/monitoring')

# 注册定时任务API
from .scheduled_tasks_api import scheduled_tasks_api
system_bp.register_blueprint(scheduled_tasks_api)

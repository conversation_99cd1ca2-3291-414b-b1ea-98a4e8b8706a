#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库验证脚本 - 验证定时任务数据存储
"""

import mysql.connector
from datetime import datetime

def verify_database():
    """验证数据库中的定时任务数据"""
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps_system'
        )
        cursor = conn.cursor()

        print("Database Verification Results:")
        print("=" * 50)

        # 查看定时任务表
        cursor.execute('SELECT * FROM scheduled_tasks ORDER BY created_at DESC LIMIT 5')
        tasks = cursor.fetchall()
        print(f"Scheduled Tasks Count: {len(tasks)}")
        
        for i, task in enumerate(tasks, 1):
            print(f"Task {i}:")
            print(f"  ID: {task[0][:40]}...")
            print(f"  Name: {task[1]}")
            print(f"  Type: {task[2]}")
            print(f"  Status: {task[4]}")
            print(f"  Created: {task[6]}")
            print("  ---")

        # 查看执行日志表
        cursor.execute('SELECT * FROM task_execution_logs ORDER BY started_at DESC LIMIT 3')
        logs = cursor.fetchall()
        print(f"Execution Logs Count: {len(logs)}")
        
        for i, log in enumerate(logs, 1):
            print(f"Log {i}:")
            print(f"  ID: {log[0]}")
            print(f"  Task Name: {log[1]}")
            print(f"  Status: {log[2]}")
            print(f"  Started: {log[3]}")
            print(f"  Finished: {log[4] if log[4] else 'N/A'}")
            print("  ---")

        # 统计信息
        cursor.execute('SELECT COUNT(*) FROM scheduled_tasks')
        total_tasks = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM scheduled_tasks WHERE status = "active"')
        active_tasks = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM task_execution_logs')
        total_logs = cursor.fetchone()[0]
        
        print("Summary:")
        print(f"  Total Tasks: {total_tasks}")
        print(f"  Active Tasks: {active_tasks}")
        print(f"  Total Logs: {total_logs}")

        cursor.close()
        conn.close()
        print("\nDatabase verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"Database verification failed: {e}")
        return False

if __name__ == '__main__':
    verify_database() 
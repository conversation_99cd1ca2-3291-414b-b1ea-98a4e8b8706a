#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试算法权重配置API
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{BASE_URL}/auth/login"
WEIGHTS_API_URL = f"{BASE_URL}/api/production/algorithm-weights"

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    # 首先获取登录页面
    try:
        login_page = session.get(f"{BASE_URL}/auth/login")
        print(f"登录页面状态: {login_page.status_code}")
    except Exception as e:
        print(f"无法访问登录页面: {e}")
        return None
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    try:
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        print(f"登录响应状态: {response.status_code}")
        
        if response.status_code in [200, 302]:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_endpoint(session, strategy):
    """测试API端点"""
    print(f"\n🔍 测试策略: {strategy}")
    
    try:
        url = f"{WEIGHTS_API_URL}?strategy={strategy}"
        print(f"请求URL: {url}")
        
        response = session.get(url)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ API响应成功")
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return data
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text[:500]}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_direct_database():
    """直接测试数据库"""
    print("\n🗄️ 直接测试数据库...")
    
    try:
        import pymysql
        conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', charset='utf8mb4')
        cursor = conn.cursor()
        
        cursor.execute('USE aps_system')
        cursor.execute("""
            SELECT strategy_name, tech_match_weight, load_balance_weight, 
                   deadline_weight, value_efficiency_weight, business_priority_weight
            FROM scheduling_config 
            WHERE user_id IS NULL AND is_active = 1
            ORDER BY strategy_name
        """)
        
        results = cursor.fetchall()
        print(f"数据库中的配置数量: {len(results)}")
        
        for row in results:
            strategy, tech, load, deadline, value, business = row
            print(f"策略: {strategy}")
            print(f"  技术匹配度: {tech}%")
            print(f"  负载均衡: {load}%") 
            print(f"  交期紧迫度: {deadline}%")
            print(f"  产值效率: {value}%")
            print(f"  业务优先级: {business}%")
            print()
        
        conn.close()
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始调试算法权重配置API...")
    
    # 测试数据库
    db_ok = test_direct_database()
    
    if not db_ok:
        print("❌ 数据库中没有配置数据，请先运行迁移脚本")
        return
    
    # 测试API
    session = login_and_get_session()
    if not session:
        print("❌ 无法登录，跳过API测试")
        return
    
    # 测试各个策略
    strategies = ['intelligent', 'deadline', 'product', 'value']
    
    for strategy in strategies:
        test_api_endpoint(session, strategy)
    
    print("\n🔧 调试完成")

if __name__ == "__main__":
    main()

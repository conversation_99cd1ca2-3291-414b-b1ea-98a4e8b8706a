/**
 * 会话状态检查器
 * 用于诊断登录状态和会话问题
 */
class SessionChecker {
    constructor() {
        this.checkInterval = null;
        this.lastCheckTime = null;
        this.sessionWarningShown = false;
    }

    /**
     * 开始会话监控
     */
    startMonitoring(intervalMinutes = 5) {
        console.log('🔍 开始会话状态监控...');
        
        // 立即检查一次
        this.checkSession();
        
        // 设置定期检查
        this.checkInterval = setInterval(() => {
            this.checkSession();
        }, intervalMinutes * 60 * 1000);
    }

    /**
     * 停止会话监控
     */
    stopMonitoring() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
            console.log('⏹️ 会话状态监控已停止');
        }
    }

    /**
     * 检查会话状态
     */
    async checkSession() {
        try {
            const response = await fetch('/api/v2/auth/session/status', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();
            this.lastCheckTime = new Date();

            if (data.success) {
                const session = data.session;
                console.log('📊 会话状态:', session);

                if (!session.is_authenticated) {
                    this.handleSessionExpired();
                } else {
                    this.sessionWarningShown = false;
                    console.log('✅ 用户已认证:', session.username);
                }
            } else {
                console.error('❌ 会话状态检查失败:', data.error);
            }
        } catch (error) {
            console.error('❌ 会话状态检查出错:', error);
        }
    }

    /**
     * 处理会话过期
     */
    handleSessionExpired() {
        if (!this.sessionWarningShown) {
            this.sessionWarningShown = true;
            console.warn('⚠️ 用户会话已过期');
            
            // 显示提示信息
            this.showSessionExpiredAlert();
        }
    }

    /**
     * 显示会话过期提醒
     */
    showSessionExpiredAlert() {
        const alertHtml = `
            <div class="alert alert-warning alert-dismissible fade show session-expired-alert" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
                <h6><i class="fas fa-exclamation-triangle"></i> 会话已过期</h6>
                <p class="mb-2">您的登录会话已过期，请重新登录以继续使用系统。</p>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-primary" onclick="sessionChecker.refreshSession()">
                        <i class="fas fa-sync"></i> 刷新会话
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sessionChecker.redirectToLogin()">
                        <i class="fas fa-sign-in-alt"></i> 重新登录
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 移除旧的提醒
        const oldAlert = document.querySelector('.session-expired-alert');
        if (oldAlert) {
            oldAlert.remove();
        }
        
        // 添加新的提醒
        document.body.insertAdjacentHTML('beforeend', alertHtml);
    }

    /**
     * 尝试刷新会话
     */
    async refreshSession() {
        try {
            const response = await fetch('/api/v2/auth/session/refresh', {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.success) {
                console.log('✅ 会话刷新成功');
                this.sessionWarningShown = false;
                
                // 移除提醒
                const alert = document.querySelector('.session-expired-alert');
                if (alert) {
                    alert.remove();
                }
                
                // 显示成功提示
                this.showSuccessMessage('会话已刷新，您可以继续使用系统');
                
                // 重新检查会话
                setTimeout(() => this.checkSession(), 1000);
            } else {
                console.error('❌ 会话刷新失败:', data.error);
                this.showErrorMessage('会话刷新失败，请重新登录');
            }
        } catch (error) {
            console.error('❌ 会话刷新出错:', error);
            this.showErrorMessage('会话刷新出错，请重新登录');
        }
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        window.location.href = '/auth/login';
    }

    /**
     * 获取详细的调试信息
     */
    async getDebugInfo() {
        try {
            const response = await fetch('/api/v2/auth/debug/login-status', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();
            
            if (data.success) {
                console.group('🐛 登录状态调试信息');
                console.log('请求信息:', data.debug.request_info);
                console.log('会话信息:', data.debug.session_info);
                console.log('当前用户信息:', data.debug.current_user_info);
                console.log('应用配置:', data.debug.app_config);
                console.groupEnd();
                
                return data.debug;
            } else {
                console.error('❌ 获取调试信息失败:', data.error);
                return null;
            }
        } catch (error) {
            console.error('❌ 获取调试信息出错:', error);
            return null;
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        if (window.ToastManager) {
            window.ToastManager.success(message);
        } else {
            // 回退方案
            console.log('会话管理器成功:', message);
            alert('成功: ' + message);
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        if (window.ToastManager) {
            window.ToastManager.error(message);
        } else {
            // 回退方案
            console.error('会话管理器错误:', message);
            alert('错误: ' + message);
        }
    }

    /**
     * 手动诊断当前状态
     */
    async diagnose() {
        console.group('🔍 会话状态诊断');
        
        // 1. 检查会话状态
        console.log('1. 检查会话状态...');
        await this.checkSession();
        
        // 2. 获取详细调试信息
        console.log('2. 获取详细调试信息...');
        const debugInfo = await this.getDebugInfo();
        
        // 3. 检查本地存储
        console.log('3. 检查本地存储...');
        console.log('localStorage keys:', Object.keys(localStorage));
        console.log('sessionStorage keys:', Object.keys(sessionStorage));
        
        // 4. 检查Cookie
        console.log('4. 检查Cookie...');
        console.log('document.cookie:', document.cookie);
        
        console.groupEnd();
        
        return {
            sessionStatus: this.lastCheckTime,
            debugInfo: debugInfo,
            localStorage: Object.keys(localStorage),
            sessionStorage: Object.keys(sessionStorage),
            cookies: document.cookie
        };
    }
}

// 创建全局实例
const sessionChecker = new SessionChecker();

// 页面加载完成后自动开始监控
document.addEventListener('DOMContentLoaded', function() {
    // 只在非登录页面启动监控
    if (!window.location.pathname.includes('/auth/login')) {
        sessionChecker.startMonitoring(5); // 每5分钟检查一次
    }
});

// 添加全局快捷键用于诊断 (Ctrl+Alt+D)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.altKey && e.key === 'd') {
        e.preventDefault();
        sessionChecker.diagnose();
    }
});

// 导出供其他脚本使用
window.SessionChecker = SessionChecker;
window.sessionChecker = sessionChecker; 
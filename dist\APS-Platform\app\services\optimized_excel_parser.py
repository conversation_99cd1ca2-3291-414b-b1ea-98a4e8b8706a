#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版Excel解析器 v3.0
解决进度跟踪、重复检测和性能问题
"""

import os
import json
import logging
import hashlib
import threading
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
import pandas as pd

logger = logging.getLogger(__name__)

@dataclass
class ParseProgress:
    """解析进度"""
    task_id: str
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    current_file: str = ''
    start_time: datetime = None
    last_update: datetime = None
    status: str = 'pending'  # pending, processing, completed, failed
    total_records: int = 0
    duplicates_found: int = 0
    duplicates_list: List[Dict] = None
    
    def __post_init__(self):
        if self.duplicates_list is None:
            self.duplicates_list = []
        if self.start_time is None:
            self.start_time = datetime.now()
        self.last_update = datetime.now()
    
    @property
    def progress_percentage(self) -> float:
        if self.total_files == 0:
            return 0.0
        return (self.processed_files / self.total_files) * 100
    
    @property
    def elapsed_time(self) -> float:
        if self.start_time:
            return (datetime.now() - self.start_time).total_seconds()
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['start_time'] = self.start_time.isoformat() if self.start_time else None
        result['last_update'] = self.last_update.isoformat() if self.last_update else None
        result['progress_percentage'] = self.progress_percentage
        result['elapsed_time'] = self.elapsed_time
        return result

@dataclass
class DuplicateRecord:
    """重复记录信息"""
    field_name: str  # 重复字段名称
    field_value: str  # 重复值
    files: List[str]  # 包含该重复值的文件列表
    records: List[Dict]  # 重复记录详情
    count: int  # 重复次数
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        self._progress_data: Dict[str, ParseProgress] = {}
        self._lock = threading.Lock()
    
    def create_task(self, task_id: str, total_files: int) -> ParseProgress:
        """创建新任务"""
        with self._lock:
            progress = ParseProgress(task_id=task_id, total_files=total_files)
            self._progress_data[task_id] = progress
            return progress
    
    def update_progress(self, task_id: str, **kwargs) -> Optional[ParseProgress]:
        """更新进度"""
        with self._lock:
            if task_id in self._progress_data:
                progress = self._progress_data[task_id]
                for key, value in kwargs.items():
                    if hasattr(progress, key):
                        setattr(progress, key, value)
                progress.last_update = datetime.now()
                return progress
        return None
    
    def get_progress(self, task_id: str) -> Optional[ParseProgress]:
        """获取进度"""
        with self._lock:
            return self._progress_data.get(task_id)
    
    def complete_task(self, task_id: str, status: str = 'completed') -> Optional[ParseProgress]:
        """完成任务"""
        return self.update_progress(task_id, status=status)

class DuplicateDetector:
    """重复检测器"""
    
    def __init__(self, check_fields: List[str] = None):
        self.check_fields = check_fields or ['单据编号', '订单号', '源文件']
        self.duplicate_index: Dict[str, Dict[str, List[Dict]]] = {}
    
    def add_records(self, records: List[Dict], source_file: str):
        """添加记录到重复检测索引"""
        for record in records:
            for field in self.check_fields:
                if field in record and record[field]:
                    field_value = str(record[field]).strip()
                    if field_value:
                        if field not in self.duplicate_index:
                            self.duplicate_index[field] = {}
                        
                        if field_value not in self.duplicate_index[field]:
                            self.duplicate_index[field][field_value] = []
                        
                        # 添加记录信息
                        record_info = record.copy()
                        record_info['_source_file'] = source_file
                        record_info['_detected_time'] = datetime.now().isoformat()
                        self.duplicate_index[field][field_value].append(record_info)
    
    def find_duplicates(self) -> List[DuplicateRecord]:
        """查找重复记录"""
        duplicates = []
        
        for field_name, field_data in self.duplicate_index.items():
            for field_value, records in field_data.items():
                if len(records) > 1:
                    # 找到重复
                    files = list(set(record['_source_file'] for record in records))
                    duplicate = DuplicateRecord(
                        field_name=field_name,
                        field_value=field_value,
                        files=files,
                        records=records,
                        count=len(records)
                    )
                    duplicates.append(duplicate)
        
        return duplicates

class OptimizedExcelParser:
    """优化版Excel解析器 v3.0"""
    
    def __init__(self, downloads_dir: str = "downloads", search_subdirs: bool = True, 
                 max_workers: int = 4):
        self.downloads_dir = downloads_dir
        self.search_subdirs = search_subdirs
        self.max_workers = max_workers
        self.progress_tracker = ProgressTracker()
        self.duplicate_detector = DuplicateDetector()
        
        # 导入增强解析器
        try:
            from .enhanced_excel_parser import EnhancedExcelParser
            self.enhanced_parser = EnhancedExcelParser(downloads_dir, search_subdirs)
            logger.info("优化解析器：增强解析器初始化成功")
        except ImportError as e:
            logger.warning(f"优化解析器：增强解析器导入失败: {e}")
            self.enhanced_parser = None
    
    def generate_task_id(self) -> str:
        """生成任务ID"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = hashlib.md5(str(time.time()).encode()).hexdigest()[:8]
        return f"parse_{timestamp}_{random_suffix}"
    
    def _parse_single_file(self, file_path: str, task_id: str) -> Dict[str, Any]:
        """解析单个文件（线程安全）"""
        try:
            file_name = os.path.basename(file_path)
            
            # 更新当前处理文件 - 修复：立即更新进度显示
            logger.info(f"🔄 开始解析文件: {file_name}")
            self.progress_tracker.update_progress(task_id, current_file=f"⏳ {file_name}")
            
            start_time = time.time()
            
            if self.enhanced_parser:
                result = self.enhanced_parser.parse_file_with_structure_analysis(file_path)
            else:
                # 回退到基础解析
                result = self._basic_parse_file(file_path)
            
            parse_time = time.time() - start_time
            
            # 添加性能信息
            result['parse_time'] = parse_time
            result['file_size'] = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            
            # 如果解析成功，添加到重复检测
            if result['status'] == 'success' and result.get('data'):
                self.duplicate_detector.add_records(result['data'], file_name)
            
            return result
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {e}")
            return {
                'status': 'error',
                'message': f'解析失败: {str(e)}',
                'data': [],
                'file_name': os.path.basename(file_path)
            }
    
    def _basic_parse_file(self, file_path: str) -> Dict[str, Any]:
        """基础解析方法（回退方案）"""
        try:
            df = pd.read_excel(file_path, engine='xlrd', header=None)
            
            # 简单的数据提取逻辑
            data = []
            for i, row in df.iterrows():
                if i > 20:  # 限制解析行数
                    break
                
                # 简单数据提取
                if pd.notna(row.iloc[0]):
                    record = {
                        '订单号': str(row.iloc[0]) if pd.notna(row.iloc[0]) else '',
                        'Lot Type': str(row.iloc[1]) if len(row) > 1 and pd.notna(row.iloc[1]) else '',
                        '产品名称': str(row.iloc[2]) if len(row) > 2 and pd.notna(row.iloc[2]) else '',
                        '源文件': os.path.basename(file_path),
                        '导入时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    if record['订单号']:  # 至少有订单号才认为是有效记录
                        data.append(record)
            
            return {
                'status': 'success',
                'message': f'基础解析成功，提取 {len(data)} 条记录',
                'data': data,
                'confidence_score': 0.5,  # 基础解析置信度较低
                'parse_method': 'basic'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'基础解析失败: {str(e)}',
                'data': []
            }
    
    def batch_parse_files_optimized(self, file_paths: List[str] = None, 
                                   progress_callback: Callable = None) -> Dict[str, Any]:
        """优化版批量解析文件"""
        task_id = self.generate_task_id()
        
        try:
            # 如果没有指定文件，扫描所有文件
            if file_paths is None:
                from app.services.order_excel_parser import OrderExcelParser
                scanner = OrderExcelParser(self.downloads_dir, self.search_subdirs)
                scan_result = scanner.scan_order_files()
                
                if scan_result['status'] != 'success':
                    return scan_result
                
                file_paths = [file_info['path'] for file_info in scan_result['yixin_order_files']]
            
            # 创建进度跟踪
            progress = self.progress_tracker.create_task(task_id, len(file_paths))
            progress.status = 'processing'
            
            # 重置重复检测器
            self.duplicate_detector = DuplicateDetector()
            
            # 结果汇总
            results = {
                'task_id': task_id,
                'status': 'success',
                'processed_files': 0,
                'failed_files': 0,
                'total_records': 0,
                'engineering_records': 0,
                'production_records': 0,
                'file_results': [],
                'engineering_data': [],
                'production_data': [],
                'performance_stats': {
                    'total_parse_time': 0.0,
                    'concurrent_processing': True,
                    'max_workers': self.max_workers
                }
            }
            
            start_time = time.time()
            
            # 并发处理文件
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self._parse_single_file, file_path, task_id): file_path
                    for file_path in file_paths
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    
                    try:
                        result = future.result()
                        file_name = os.path.basename(file_path)
                        
                        # 更新文件结果
                        file_result = {
                            'file_name': file_name,
                            'file_path': file_path,
                            'status': result['status'],
                            'message': result['message'],
                            'records': len(result['data']) if result['status'] == 'success' else 0,
                            'parse_method': result.get('parse_method', 'unknown'),
                            'confidence_score': result.get('confidence_score', 0.0),
                            'parse_time': result.get('parse_time', 0.0),
                            'file_size': result.get('file_size', 0)
                        }
                        
                        results['file_results'].append(file_result)
                        
                        if result['status'] == 'success':
                            results['processed_files'] += 1
                            results['total_records'] += len(result['data'])
                            
                            # 按分类整理数据
                            for record in result['data']:
                                classification = record.get('分类结果', '未知')
                                if classification == '工程':
                                    results['engineering_data'].append(record)
                                    results['engineering_records'] += 1
                                elif classification == '量产':
                                    results['production_data'].append(record)
                                    results['production_records'] += 1
                        else:
                            results['failed_files'] += 1
                        
                        # 更新进度 - 修复：同步当前文件信息
                        self.progress_tracker.update_progress(
                            task_id,
                            processed_files=results['processed_files'],
                            failed_files=results['failed_files'],
                            total_records=results['total_records'],
                            current_file=file_name  # 修复：同步当前处理的文件名
                        )
                        
                        # 性能统计
                        if result.get('parse_time'):
                            results['performance_stats']['total_parse_time'] += result['parse_time']
                        
                        # 调用进度回调 - 修复：确保传递字典格式
                        if progress_callback:
                            progress_obj = self.progress_tracker.get_progress(task_id)
                            progress_callback(progress_obj.to_dict() if progress_obj else {})
                        
                    except Exception as e:
                        logger.error(f"处理文件失败 {file_path}: {e}")
                        results['failed_files'] += 1
                        
                        # 更新进度 - 修复：同步失败文件信息
                        self.progress_tracker.update_progress(
                            task_id,
                            failed_files=results['failed_files'],
                            current_file=f"❌ {os.path.basename(file_path)}"  # 修复：显示失败文件名
                        )
            
            total_time = time.time() - start_time
            
            # 完善性能统计
            if results['processed_files'] > 0:
                results['performance_stats']['average_parse_time'] = (
                    results['performance_stats']['total_parse_time'] / results['processed_files']
                )
                results['performance_stats']['files_per_second'] = results['processed_files'] / total_time if total_time > 0 else 0
            
            results['performance_stats']['total_processing_time'] = total_time
            
            # 重复检测
            duplicates = self.duplicate_detector.find_duplicates()
            
            results['duplicates'] = {
                'found': len(duplicates) > 0,
                'count': len(duplicates),
                'details': [dup.to_dict() for dup in duplicates],
                'requires_confirmation': len(duplicates) > 0
            }
            
            # 更新最终进度
            self.progress_tracker.update_progress(
                task_id,
                duplicates_found=len(duplicates),
                duplicates_list=[dup.to_dict() for dup in duplicates]
            )
            
            # 完成任务
            self.progress_tracker.complete_task(task_id, 'completed')
            
            results['message'] = (
                f"✅ 优化解析完成：成功 {results['processed_files']} 个文件，"
                f"失败 {results['failed_files']} 个文件，"
                f"共 {results['total_records']} 条记录，"
                f"发现 {len(duplicates)} 个重复项 "
                f"(耗时: {total_time:.2f}秒, 速度: {results['performance_stats']['files_per_second']:.2f}文件/秒)"
            )
            
            return results
            
        except Exception as e:
            self.progress_tracker.complete_task(task_id, 'failed')
            logger.error(f"批量解析失败: {str(e)}")
            return {
                'task_id': task_id,
                'status': 'error',
                'message': f"❌ 批量解析失败: {str(e)}",
                'processed_files': 0,
                'failed_files': 0,
                'total_records': 0
            }
    
    def get_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取解析进度"""
        progress = self.progress_tracker.get_progress(task_id)
        return progress.to_dict() if progress else None
    
    def check_duplicates_interactive(self, duplicates: List[DuplicateRecord]) -> Dict[str, Any]:
        """交互式重复检查"""
        if not duplicates:
            return {
                'action': 'proceed',
                'message': '✅ 未发现重复记录，可以继续处理'
            }
        
        # 生成重复报告
        report = {
            'action': 'confirm_required',
            'message': f'⚠️ 发现 {len(duplicates)} 个重复项，需要人工确认',
            'duplicates': [],
            'suggestions': []
        }
        
        for dup in duplicates:
            dup_info = {
                'field': dup.field_name,
                'value': dup.field_value,
                'count': dup.count,
                'files': dup.files,
                'suggestion': self._get_duplicate_suggestion(dup)
            }
            report['duplicates'].append(dup_info)
            report['suggestions'].append(dup_info['suggestion'])
        
        return report
    
    def _get_duplicate_suggestion(self, duplicate: DuplicateRecord) -> str:
        """获取重复处理建议"""
        if duplicate.field_name == '单据编号':
            if len(duplicate.files) > 1:
                return "💡 建议：相同单据编号出现在多个文件中，可能是同一订单的不同批次，建议合并处理"
            else:
                return "⚠️ 建议：同一文件中存在重复单据编号，可能是数据录入错误，建议检查源文件"
        elif duplicate.field_name == '订单号':
            return "🔄 建议：重复订单号可能表示订单修订或补充，建议确认是否需要覆盖或追加"
        else:
            return "🧹 建议：发现重复数据，请检查是否需要去重处理"

# 全局进度跟踪器实例
global_progress_tracker = ProgressTracker()

def get_global_progress_tracker() -> ProgressTracker:
    """获取全局进度跟踪器"""
    return global_progress_tracker

from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from . import system_bp
from app import db
from app.models import SystemSetting, DatabaseInfo, AISettings
from datetime import datetime
import logging
import pymysql
import json

logger = logging.getLogger(__name__)

@system_bp.route('/health')
def health_check():
    """系统管理模块健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'system_v2',
        'timestamp': datetime.now().isoformat()
    })

@system_bp.route('/database/status')
@login_required
def get_database_status():
    """获取数据库状态"""
    try:
        # 测试数据库连接
        db.session.execute('SELECT 1')
        
        # 获取数据库信息
        database_info = DatabaseInfo.query.first()
        
        return jsonify({
            'status': 'connected',
            'database_type': 'MySQL',
            'connection_info': {
                'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': current_app.config.get('MYSQL_PORT', 3306),
                'database': current_app.config.get('MYSQL_DATABASE', 'aps')
            },
            'database_info': {
                'version': database_info.version if database_info else 'Unknown',
                'last_migration': database_info.last_migration.isoformat() if database_info and database_info.last_migration else None
            }
        })
    except Exception as e:
        logger.error(f"获取数据库状态失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@system_bp.route('/database/test-connection', methods=['POST'])
@login_required
def test_database_connection():
    """测试数据库连接"""
    try:
        data = request.get_json()
        
        # 获取连接参数
        mysql_config = {
            'host': data.get('host', current_app.config.get('MYSQL_HOST', '127.0.0.1')),
            'port': int(data.get('port', current_app.config.get('MYSQL_PORT', 3306))),
            'user': data.get('user', current_app.config.get('MYSQL_USER', 'root')),
            'password': data.get('password', current_app.config.get('MYSQL_PASSWORD', '')),
            'database': data.get('database', current_app.config.get('MYSQL_DATABASE', 'aps')),
            'charset': data.get('charset', 'utf8mb4')
        }
        
        # 测试连接
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database'],
            charset=mysql_config['charset'],
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION() as version")
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': '数据库连接成功',
            'version': result['version']
        })
        
    except Exception as e:
        logger.error(f"数据库连接测试失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'数据库连接失败: {str(e)}'
        }), 500

@system_bp.route('/database/config', methods=['GET', 'POST'])
@login_required
def database_config():
    """获取或更新数据库配置"""
    if request.method == 'GET':
        try:
            # 获取AI设置中的数据库配置
            ai_setting = AISettings.query.filter_by(id=1).first()
            
            if ai_setting and ai_setting.settings:
                settings = json.loads(ai_setting.settings)
                db_config = settings.get('database', {})
                
                return jsonify({
                    'mysql': db_config.get('mysql', {}),
                    'current_database': current_app.config.get('MYSQL_DATABASE', 'aps')
                })
            else:
                return jsonify({
                    'mysql': {
                        'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                        'port': current_app.config.get('MYSQL_PORT', 3306),
                        'user': current_app.config.get('MYSQL_USER', 'root'),
                        'database': current_app.config.get('MYSQL_DATABASE', 'aps')
                    },
                    'current_database': current_app.config.get('MYSQL_DATABASE', 'aps')
                })
                
        except Exception as e:
            logger.error(f"获取数据库配置失败: {str(e)}")
            return jsonify({'error': '获取数据库配置失败'}), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # 获取或创建AI设置
            ai_setting = AISettings.query.filter_by(id=1).first()
            if not ai_setting:
                ai_setting = AISettings(id=1, settings='{}')
                db.session.add(ai_setting)
            
            # 更新数据库配置
            settings = json.loads(ai_setting.settings) if ai_setting.settings else {}
            if 'database' not in settings:
                settings['database'] = {}
            
            settings['database']['mysql'] = {
                'host': data.get('host', '127.0.0.1'),
                'port': int(data.get('port', 3306)),
                'user': data.get('user', 'root'),
                'password': data.get('password', ''),
                'database': data.get('database', 'aps'),
                'charset': data.get('charset', 'utf8mb4')
            }
            
            ai_setting.settings = json.dumps(settings)
            db.session.commit()
            
            return jsonify({
                'status': 'success',
                'message': '数据库配置已更新'
            })
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新数据库配置失败: {str(e)}")
            return jsonify({'error': '更新数据库配置失败'}), 500

@system_bp.route('/settings')
@login_required
def get_system_settings():
    """获取系统设置"""
    try:
        settings = SystemSetting.query.all()
        return jsonify([{
            'id': setting.id,
            'key': setting.key,
            'value': setting.value,
            'description': setting.description,
            'setting_type': setting.setting_type,
            'updated_at': setting.updated_at.isoformat() if setting.updated_at else None
        } for setting in settings])
    except Exception as e:
        logger.error(f"获取系统设置失败: {str(e)}")
        return jsonify({'error': '获取系统设置失败'}), 500

@system_bp.route('/settings', methods=['POST'])
@login_required
def update_system_settings():
    """更新系统设置"""
    try:
        data = request.get_json()
        
        for setting_data in data.get('settings', []):
            setting = SystemSetting.query.filter_by(key=setting_data['key']).first()
            if setting:
                setting.value = setting_data['value']
                setting.updated_at = datetime.now()
            else:
                setting = SystemSetting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    description=setting_data.get('description', ''),
                    setting_type=setting_data.get('category', 'general'),
                    updated_at=datetime.now()
                )
                db.session.add(setting)
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '系统设置已更新'
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新系统设置失败: {str(e)}")
        return jsonify({'error': '更新系统设置失败'}), 500

@system_bp.route('/check-mysql-databases')
@login_required
def check_mysql_databases():
    """检查MySQL数据库列表"""
    try:
        # 获取MySQL连接配置
        mysql_config = {
            'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
            'port': current_app.config.get('MYSQL_PORT', 3306),
            'user': current_app.config.get('MYSQL_USER', 'root'),
            'password': current_app.config.get('MYSQL_PASSWORD', ''),
            'charset': 'utf8mb4'
        }
        
        # 连接MySQL服务器（不指定数据库）
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            charset=mysql_config['charset'],
            cursorclass=pymysql.cursors.DictCursor
        )
        
        cursor = conn.cursor()
        
        # 查询所有数据库
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        
        # 过滤系统数据库
        user_databases = []
        system_databases = ['information_schema', 'performance_schema', 'mysql', 'sys']
        
        for db_info in databases:
            db_name = db_info['Database']
            if db_name not in system_databases:
                user_databases.append(db_name)
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'databases': user_databases,
            'current_database': current_app.config.get('MYSQL_DATABASE', 'aps')
        })
        
    except Exception as e:
        logger.error(f"检查MySQL数据库失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@system_bp.route('/email-scheduler', methods=['GET', 'POST'])
def email_scheduler_control():
    """邮件调度器控制接口"""
    try:
        if request.method == 'GET':
            # 获取调度器状态（使用新的APScheduler）
            from app import scheduler
            from app.models import EmailConfig, SchedulerConfig
            
            if scheduler:
                # 获取调度器状态
                enabled = SchedulerConfig.get_config('scheduler_enabled', 'true').lower() == 'true'
                running = scheduler.is_running
                
                # 获取邮箱配置数量
                configs_count = EmailConfig.query.filter_by(enabled=True).count()
                
                return jsonify({
                    'success': True,
                    'status': {
                        'enabled': enabled,
                        'running': running,
                        'configs_count': configs_count,
                        'global_scheduler_enabled': enabled
                    },
                    'message': '邮件调度器状态获取成功'
                })
            else:
                return jsonify({
                    'success': True,
                    'status': {
                        'enabled': False,
                        'running': False,
                        'configs_count': 0,
                        'global_scheduler_enabled': False
                    },
                    'message': '邮件调度器未初始化'
                })
        
        # POST请求 - 控制操作
        data = request.get_json() or {}
        operation = data.get('operation', '').lower()
        
        from app import scheduler, db
        from app.models import SchedulerConfig, EmailConfig
        
        if operation == 'start':
            if scheduler:
                # 启动调度器
                if not scheduler.is_running:
                    scheduler.start()
                
                # 更新配置状态
                SchedulerConfig.set_config('scheduler_enabled', 'true')
                SchedulerConfig.set_config('global_scheduler_enabled', 'true')
                
                # 同步更新所有邮箱配置状态为启用
                EmailConfig.query.update({'enabled': True})
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': '邮件调度器启动成功，已同步启用所有邮箱配置'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '邮件调度器未初始化'
                }), 400
                
        elif operation == 'stop':
            if scheduler:
                # 停止调度器
                if scheduler.is_running:
                    scheduler.stop(wait=False)
                
                # 更新配置状态
                SchedulerConfig.set_config('scheduler_enabled', 'false')
                SchedulerConfig.set_config('global_scheduler_enabled', 'false')
                
                # 同步更新所有邮箱配置状态为禁用
                EmailConfig.query.update({'enabled': False})
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': '邮件调度器停止成功，已同步禁用所有邮箱配置'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '邮件调度器未初始化'
                }), 400
                    
        elif operation == 'reload':
            if scheduler:
                # APScheduler从数据库自动加载任务，不需要手动重载
                return jsonify({
                    'success': True,
                    'message': 'APScheduler任务自动从数据库加载，无需手动重载'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '邮件调度器未初始化'
                }), 400
                    
        elif operation == 'status':
            # 同GET请求（使用新的APScheduler）
            if scheduler:
                enabled = SchedulerConfig.get_config('scheduler_enabled', 'true').lower() == 'true'
                running = scheduler.is_running
                configs_count = EmailConfig.query.filter_by(enabled=True).count()
                
                return jsonify({
                    'success': True,
                    'status': {
                        'enabled': enabled,
                        'running': running,
                        'configs_count': configs_count,
                        'global_scheduler_enabled': enabled
                    },
                    'message': '邮件调度器状态获取成功'
                })
            else:
                return jsonify({
                    'success': True,
                    'status': {
                        'enabled': False,
                        'running': False,
                        'configs_count': 0,
                        'global_scheduler_enabled': False
                    },
                    'message': '邮件调度器未初始化'
                })
        else:
            return jsonify({
                'success': False,
                'message': f'不支持的操作: {operation}'
            }), 400
            
    except Exception as e:
        logger.error(f"邮件调度器控制失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'邮件调度器控制失败: {str(e)}'
        }), 500

@system_bp.route('/ai-config')
@login_required
def ai_config():
    """获取AI助手配置信息"""
    try:
        # 模拟AI配置信息
        config = {
            "api_configured": True,
            "search_configured": True,
            "model": "DeepSeek AI",
            "models": {
                "standard": {
                    "name": "DeepSeek-R1-Distill-Qwen-32B",
                    "id": "deepseek-r1-distill-qwen-32b"
                },
                "r1": {
                    "name": "DeepSeek-R1",
                    "id": "deepseek-r1"
                }
            },
            "modes": [
                {
                    "id": "standard",
                    "name": "标准对话",
                    "description": "使用DeepSeek-R1-Distill-Qwen-32B进行基本的问答",
                    "available": True
                },
                {
                    "id": "r1",
                    "name": "深度思考",
                    "description": "使用DeepSeek-R1处理更复杂的问题，提供更详细的分析",
                    "available": True
                },
                {
                    "id": "web_search",
                    "name": "联网搜索",
                    "description": "可以搜索网络获取最新信息",
                    "available": True
                },
                {
                    "id": "database",
                    "name": "数据库查询",
                    "description": "从系统数据库中获取实时数据进行回答",
                    "available": True
                }
            ],
            "prioritize_database": True
        }
        
        return jsonify(config)
        
    except Exception as e:
        logger.error(f"获取AI配置出错: {str(e)}")
        return jsonify({
            "error": f"获取配置信息时出现了错误: {str(e)}"
        }), 500

@system_bp.route('/ai-settings', methods=['GET', 'POST'])
@login_required
def ai_settings():
    """AI设置API - 包含Dify配置"""
    if request.method == 'GET':
        try:
            # 获取Dify相关设置
            chatbot_token = SystemSetting.query.filter_by(key='chatbot_token').first()
            chatbot_server = SystemSetting.query.filter_by(key='chatbot_server').first()
            enable_chatbot = SystemSetting.query.filter_by(key='enable_chatbot').first()
            
            # 获取AI配置
            ai_setting = AISettings.query.filter_by(id=1).first()
            ai_config = {}
            if ai_setting and ai_setting.settings:
                ai_config = json.loads(ai_setting.settings)
            
            config = {
                'chatbot': {
                    'enabled': enable_chatbot.value.lower() == 'true' if enable_chatbot else False,
                    'token': chatbot_token.value if chatbot_token else 'uV72gGRdNz0eP7ac',
                    'server': chatbot_server.value if chatbot_server else 'http://localhost:3000'
                },
                'database': ai_config.get('database', {
                    'enabled': True,
                    'prioritize_database': True,
                    'type': 'mysql'
                })
            }
            
            return jsonify(config)
            
        except Exception as e:
            logger.error(f"获取AI设置失败: {e}")
            return jsonify({'error': '获取AI设置失败'}), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # 更新Dify聊天机器人设置
            if 'chatbot' in data:
                chatbot_config = data['chatbot']
                
                # 更新或创建设置
                settings_to_update = [
                    ('enable_chatbot', str(chatbot_config.get('enabled', False)).lower()),
                    ('chatbot_token', chatbot_config.get('token', 'uV72gGRdNz0eP7ac')),
                    ('chatbot_server', chatbot_config.get('server', 'http://localhost:3000'))
                ]
                
                for key, value in settings_to_update:
                    setting = SystemSetting.query.filter_by(key=key).first()
                    if setting:
                        setting.value = value
                        setting.updated_at = datetime.now()
                    else:
                        setting = SystemSetting(
                            key=key,
                            value=value,
                            description=f'Dify {key}',
                            category='chatbot',
                            updated_at=datetime.now()
                        )
                        db.session.add(setting)
            
            # 更新AI数据库设置
            if 'database' in data:
                ai_setting = AISettings.query.filter_by(id=1).first()
                if not ai_setting:
                    ai_setting = AISettings(id=1, settings='{}')
                    db.session.add(ai_setting)
                
                settings = json.loads(ai_setting.settings) if ai_setting.settings else {}
                settings['database'] = data['database']
                ai_setting.settings = json.dumps(settings)
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'AI设置已更新'
            })
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新AI设置失败: {e}")
            return jsonify({'error': '更新AI设置失败'}), 500 
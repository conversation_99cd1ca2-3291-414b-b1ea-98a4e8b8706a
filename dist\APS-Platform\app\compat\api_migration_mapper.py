#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API v1到v2迁移映射系统
提供自动的端点迁移和兼容性支持
"""

from flask import request, redirect, url_for, jsonify, current_app
from functools import wraps
import logging

# 配置日志
migration_logger = logging.getLogger('api_migration')

# API v1到v2的映射配置
API_MIGRATION_MAP = {
    # 系统监控相关
    '/api/dashboard/stats': {
        'v2_endpoint': '/api/v2/system/monitoring/metrics',
        'method': 'GET',
        'parameters_mapping': {},
        'response_transform': None,
        'migration_type': 'direct'  # direct, transform, custom
    },
    '/api/dashboard/charts': {
        'v2_endpoint': '/api/v2/system/monitoring/charts',
        'method': 'GET',
        'parameters_mapping': {},
        'response_transform': None,
        'migration_type': 'direct'
    },
    '/api/debug/dashboard': {
        'v2_endpoint': '/api/v2/system/monitoring/debug',
        'method': 'GET',
        'parameters_mapping': {},
        'response_transform': None,
        'migration_type': 'direct'
    },
    
    # AI助手相关
    '/api/ai/config': {
        'v2_endpoint': '/api/v2/system/ai-config',
        'method': ['GET', 'POST'],
        'parameters_mapping': {
            'model_name': 'ai_model',
            'api_key': 'openai_key',
            'base_url': 'api_base_url'
        },
        'response_transform': 'transform_ai_config_response',
        'migration_type': 'transform'
    },
    
    # 用户认证相关
    '/auth/users/<username>/permissions': {
        'v2_endpoint': '/api/v2/auth/users/<username>/permissions',
        'method': ['GET', 'PUT'],
        'parameters_mapping': {},
        'response_transform': None,
        'migration_type': 'direct'
    },
    

    
    # 菜单设置
    '/menu/settings/<int:id>': {
        'v2_endpoint': '/api/v2/auth/menu-settings/<int:id>',
        'method': ['GET', 'PUT', 'DELETE'],
        'parameters_mapping': {},
        'response_transform': None,
        'migration_type': 'direct'
    },
    
    # 数据库检查
    '/check-database': {
        'v2_endpoint': '/test-database-connection',
        'method': 'GET',
        'parameters_mapping': {},
        'response_transform': None,
        'migration_type': 'direct'
    },
    
    # 生产调度兼容性 - 已移除，无实际使用
    # '/compatibility/scheduling' - 已删除
}

# 响应转换函数
def transform_ai_config_response(v2_response):
    """转换AI配置响应格式"""
    if isinstance(v2_response, dict):
        # v1格式转换
        v1_format = {
            'model_name': v2_response.get('ai_model', ''),
            'api_key': v2_response.get('openai_key', ''),
            'base_url': v2_response.get('api_base_url', ''),
            'status': v2_response.get('status', 'unknown')
        }
        return v1_format
    return v2_response



def transform_scheduling_response(v2_response):
    """转换生产调度响应格式"""
    if isinstance(v2_response, dict):
        v1_format = {
            'lot_ids': v2_response.get('batch_ids', []),
            'priority_level': v2_response.get('priority', 0),
            'schedule_type': v2_response.get('algorithm_type', ''),
            'result': v2_response.get('scheduling_result', {}),
            'success': v2_response.get('success', False)
        }
        return v1_format
    return v2_response

# 获取转换函数
def get_transform_function(transform_name):
    """获取响应转换函数"""
    transform_functions = {
        'transform_ai_config_response': transform_ai_config_response,
        'transform_scheduling_response': transform_scheduling_response
    }
    return transform_functions.get(transform_name)

def migrate_parameters(v1_params, mapping):
    """迁移请求参数"""
    if not mapping:
        return v1_params
    
    v2_params = {}
    for v1_key, v2_key in mapping.items():
        if v1_key in v1_params:
            v2_params[v2_key] = v1_params[v1_key]
    
    # 保留未映射的参数
    for key, value in v1_params.items():
        if key not in mapping:
            v2_params[key] = value
    
    return v2_params

def auto_migrate_endpoint(v1_endpoint):
    """自动迁移API端点"""
    migration_config = API_MIGRATION_MAP.get(v1_endpoint)
    
    if not migration_config:
        return None
    
    try:
        v2_endpoint = migration_config['v2_endpoint']
        method = request.method
        
        # 检查方法是否支持
        supported_methods = migration_config.get('method', ['GET'])
        if isinstance(supported_methods, str):
            supported_methods = [supported_methods]
        
        if method not in supported_methods:
            current_app.logger.warning(f"Method {method} not supported for migration from {v1_endpoint} to {v2_endpoint}")
            return None
        
        # 迁移参数
        if method == 'GET':
            v1_params = request.args.to_dict()
        else:
            v1_params = request.get_json() or {}
        
        v2_params = migrate_parameters(v1_params, migration_config.get('parameters_mapping', {}))
        
        # 根据迁移类型处理
        migration_type = migration_config.get('migration_type', 'direct')
        
        if migration_type == 'direct':
            # 直接重定向
            if method == 'GET':
                return redirect(f"{v2_endpoint}?{urlencode(v2_params)}" if v2_params else v2_endpoint)
            else:
                # 对于POST/PUT等，需要转发请求
                return forward_request_to_v2(v2_endpoint, method, v2_params)
        
        elif migration_type == 'transform':
            # 需要响应转换
            response = forward_request_to_v2(v2_endpoint, method, v2_params)
            
            # 应用响应转换
            transform_func_name = migration_config.get('response_transform')
            if transform_func_name:
                transform_func = get_transform_function(transform_func_name)
                if transform_func:
                    if hasattr(response, 'get_json'):
                        data = response.get_json()
                        transformed_data = transform_func(data)
                        return jsonify(transformed_data)
            
            return response
        
        else:
            current_app.logger.warning(f"Unknown migration type: {migration_type}")
            return None
            
    except Exception as e:
        current_app.logger.error(f"Error migrating endpoint {v1_endpoint}: {e}")
        return None

def forward_request_to_v2(v2_endpoint, method, params):
    """转发请求到API v2端点"""
    try:
        # 这里需要实现内部请求转发
        # 由于Flask的限制，我们使用test_client来模拟内部请求
        with current_app.test_client() as client:
            if method == 'GET':
                response = client.get(v2_endpoint, query_string=params)
            elif method == 'POST':
                response = client.post(v2_endpoint, json=params)
            elif method == 'PUT':
                response = client.put(v2_endpoint, json=params)
            elif method == 'DELETE':
                response = client.delete(v2_endpoint)
            else:
                return jsonify({'error': f'Unsupported method: {method}'}), 400
            
            # 返回响应
            return jsonify(response.get_json()), response.status_code
            
    except Exception as e:
        current_app.logger.error(f"Error forwarding request to {v2_endpoint}: {e}")
        return jsonify({'error': 'Internal migration error'}), 500

def is_endpoint_migratable(endpoint):
    """检查端点是否可迁移"""
    return endpoint in API_MIGRATION_MAP

def get_migration_info(endpoint):
    """获取端点迁移信息"""
    return API_MIGRATION_MAP.get(endpoint)

def list_all_migrations():
    """列出所有可用的迁移映射"""
    migrations = []
    for v1_endpoint, config in API_MIGRATION_MAP.items():
        migrations.append({
            'v1_endpoint': v1_endpoint,
            'v2_endpoint': config['v2_endpoint'],
            'methods': config.get('method', ['GET']),
            'migration_type': config.get('migration_type', 'direct'),
            'has_parameter_mapping': bool(config.get('parameters_mapping')),
            'has_response_transform': bool(config.get('response_transform'))
        })
    return migrations

# 中间件：自动迁移API v1请求
def init_api_migration_middleware(app):
    """初始化API迁移中间件"""
    
    @app.before_request
    def auto_migrate_api_v1():
        """自动迁移API v1请求到v2"""
        endpoint = request.path
        
        # 只处理API v1端点
        if is_endpoint_migratable(endpoint):
            migration_logger.info(f"Attempting to migrate {endpoint} to API v2")
            
            migrated_response = auto_migrate_endpoint(endpoint)
            if migrated_response:
                migration_logger.info(f"Successfully migrated {endpoint}")
                return migrated_response
            else:
                migration_logger.warning(f"Failed to migrate {endpoint}")
    
    # 注册迁移管理API
    from flask import Blueprint
    
    migration_bp = Blueprint('migration', __name__, url_prefix='/api/migration')
    
    def list_migrations():
        """列出所有迁移映射"""
        return jsonify(list_all_migrations())
    
    @migration_bp.route('/info/<path:endpoint>', methods=['GET'])
    def migration_info(endpoint):
        """获取特定端点的迁移信息"""
        endpoint = '/' + endpoint
        info = get_migration_info(endpoint)
        
        if info:
            return jsonify({
                'migratable': True,
                'info': info
            })
        else:
            return jsonify({
                'migratable': False,
                'message': 'No migration available for this endpoint'
            })
    
    @migration_bp.route('/test/<path:endpoint>', methods=['GET'])
    def test_migration(endpoint):
        """测试端点迁移"""
        endpoint = '/' + endpoint
        
        if is_endpoint_migratable(endpoint):
            try:
                # 模拟迁移测试
                config = get_migration_info(endpoint)
                return jsonify({
                    'success': True,
                    'v1_endpoint': endpoint,
                    'v2_endpoint': config['v2_endpoint'],
                    'migration_type': config.get('migration_type', 'direct'),
                    'message': 'Migration test successful'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        else:
            return jsonify({
                'success': False,
                'message': 'Endpoint is not migratable'
            }), 404
    
    app.register_blueprint(migration_bp)
    app.logger.info('✅ API迁移中间件已启用')

# 辅助函数
try:
    from urllib.parse import urlencode
except ImportError:
    from urllib import urlencode 
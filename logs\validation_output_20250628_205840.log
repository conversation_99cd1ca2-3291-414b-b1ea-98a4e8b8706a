2025-06-28 20:58:40,527 - INFO - 🚀 开始运行算法验证...
2025-06-28 20:58:40,527 - INFO - 🔄 加载验证数据...
2025-06-28 20:58:40,528 - INFO - 加载待排产批次从: Excellist2025.06.05/排产验证/ET_WAIT_LOT.xlsx
2025-06-28 20:58:40,857 - INFO - ✅ 成功加载 409 条待排产批次
2025-06-28 20:58:40,863 - INFO - 加载期望结果从: Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx
2025-06-28 20:58:40,982 - INFO - ✅ 成功加载 409 条期望结果
2025-06-28 20:58:40,987 - INFO - ✅ 验证数据加载成功。
2025-06-28 20:58:40,987 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:58:40,988 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:58:41,892 - INFO - 系统模型包已禁用，请直接从app.models导入
2025-06-28 20:58:41,935 - INFO - 🚀 开始执行真实排产，算法: intelligent
2025-06-28 20:58:41,935 - INFO - 📦 使用传入的 409 条批次数据进行排产
2025-06-28 20:58:41,963 - INFO - ✅ MySQL数据源可用
2025-06-28 20:58:41,963 - INFO - 🔄 缓存更新: test_spec_data
2025-06-28 20:58:42,103 - INFO - 从MySQL获取到 555 条测试规范数据
2025-06-28 20:58:42,122 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,123 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,123 - WARNING - 批次 YX2500000883 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,124 - WARNING - ⚠️ 批次 YX2500000883 配置需求获取失败，跳过
2025-06-28 20:58:42,124 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,124 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,125 - WARNING - 批次 YX2500000884 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,125 - WARNING - ⚠️ 批次 YX2500000884 配置需求获取失败，跳过
2025-06-28 20:58:42,126 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,126 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,126 - WARNING - 批次 YX2500000886 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,127 - WARNING - ⚠️ 批次 YX2500000886 配置需求获取失败，跳过
2025-06-28 20:58:42,127 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,127 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,128 - WARNING - 批次 YX2500000887 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,128 - WARNING - ⚠️ 批次 YX2500000887 配置需求获取失败，跳过
2025-06-28 20:58:42,128 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,129 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,129 - WARNING - 批次 YX2500000939 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,129 - WARNING - ⚠️ 批次 YX2500000939 配置需求获取失败，跳过
2025-06-28 20:58:42,130 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,130 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,130 - WARNING - 批次 YX2500000940 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,131 - WARNING - ⚠️ 批次 YX2500000940 配置需求获取失败，跳过
2025-06-28 20:58:42,131 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,132 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,132 - WARNING - 批次 YX2500000941 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,132 - WARNING - ⚠️ 批次 YX2500000941 配置需求获取失败，跳过
2025-06-28 20:58:42,133 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,133 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,134 - WARNING - 批次 YX2500000943 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,134 - WARNING - ⚠️ 批次 YX2500000943 配置需求获取失败，跳过
2025-06-28 20:58:42,134 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,134 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,135 - WARNING - 批次 YX2500000944 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,135 - WARNING - ⚠️ 批次 YX2500000944 配置需求获取失败，跳过
2025-06-28 20:58:42,135 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,136 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,136 - WARNING - 批次 YX2500001354 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,136 - WARNING - ⚠️ 批次 YX2500001354 配置需求获取失败，跳过
2025-06-28 20:58:42,136 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,136 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,137 - WARNING - 批次 YX2500001355 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,137 - WARNING - ⚠️ 批次 YX2500001355 配置需求获取失败，跳过
2025-06-28 20:58:42,137 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,138 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,138 - WARNING - 批次 YX2500001356 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,138 - WARNING - ⚠️ 批次 YX2500001356 配置需求获取失败，跳过
2025-06-28 20:58:42,139 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,139 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,139 - WARNING - 批次 YX2500001357 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,140 - WARNING - ⚠️ 批次 YX2500001357 配置需求获取失败，跳过
2025-06-28 20:58:42,140 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,140 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,141 - WARNING - 批次 YX2300000310 未找到匹配的测试规范 (DEVICE=JWQ5103ASQFNAT_TA0, STAGE=BTT)
2025-06-28 20:58:42,141 - WARNING - ⚠️ 批次 YX2300000310 配置需求获取失败，跳过
2025-06-28 20:58:42,141 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,142 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,142 - WARNING - 批次 YX2500001202 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,142 - WARNING - ⚠️ 批次 YX2500001202 配置需求获取失败，跳过
2025-06-28 20:58:42,143 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,143 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,143 - WARNING - 批次 YX2300000311 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TA0, STAGE=BTT)
2025-06-28 20:58:42,144 - WARNING - ⚠️ 批次 YX2300000311 配置需求获取失败，跳过
2025-06-28 20:58:42,144 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,144 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,144 - WARNING - 批次 YX2300000312 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TA0, STAGE=BTT)
2025-06-28 20:58:42,145 - WARNING - ⚠️ 批次 YX2300000312 配置需求获取失败，跳过
2025-06-28 20:58:42,145 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,145 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,146 - WARNING - 批次 YX2400000583 未找到匹配的测试规范 (DEVICE=JW3655E, STAGE=BTT)
2025-06-28 20:58:42,146 - WARNING - ⚠️ 批次 YX2400000583 配置需求获取失败，跳过
2025-06-28 20:58:42,146 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,147 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,147 - WARNING - 批次 YX2400000585 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,147 - WARNING - ⚠️ 批次 YX2400000585 配置需求获取失败，跳过
2025-06-28 20:58:42,148 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,148 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,149 - WARNING - 批次 YX2400000603 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,149 - WARNING - ⚠️ 批次 YX2400000603 配置需求获取失败，跳过
2025-06-28 20:58:42,149 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,149 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,150 - WARNING - 批次 YX2400000605 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:58:42,150 - WARNING - ⚠️ 批次 YX2400000605 配置需求获取失败，跳过
2025-06-28 20:58:42,150 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,150 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,151 - WARNING - 批次 YX24XX040012 未找到匹配的测试规范 (DEVICE=JW3655E, STAGE=BTT)
2025-06-28 20:58:42,151 - WARNING - ⚠️ 批次 YX24XX040012 配置需求获取失败，跳过
2025-06-28 20:58:42,151 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:58:42,151 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:58:42,152 - INFO - 🔄 缓存更新: uph_data
2025-06-28 20:58:42,199 - INFO - 从MySQL获取到 1759 条UPH数据
2025-06-28 20:58:42,212 - INFO - ⚡ 从MySQL获取到 1759 条UPH数据（缓存）
2025-06-28 20:58:42,212 - ERROR - 获取KIT配置失败: name 'pymysql' is not defined
2025-06-28 20:58:42,213 - INFO - Lot YX2500001955: 查找设备, 需求: {'DEVICE': 'JWH7069TLGAA-M001', 'STAGE': 'UIS', 'HB_PN': '20220182_B', 'TB_PN': 'TB_CC4812A_V41', 'HANDLER_CONFIG': 'PnP', 'PKG_PN': 'TLGA5*6-41L', 'TESTER': 'CTA8280F', 'UPH': '2360', 'TEST_SPEC_SOURCE': 'ET_FT_TEST_SPEC'}
2025-06-28 20:58:42,213 - INFO - 🔄 从eqp_status获取可用设备...
2025-06-28 20:58:42,213 - INFO - 🔄 缓存更新: equipment_status_data
2025-06-28 20:58:42,242 - INFO - 从MySQL获取到 67 条设备状态数据
2025-06-28 20:58:42,243 - INFO - 🏭 从MySQL获取到 67 条设备状态数据（缓存）
2025-06-28 20:58:42,244 - INFO - 表格 eqp_status: 总计 67 条记录, 显示 67 条
2025-06-28 20:58:42,244 - ERROR - ❌ 执行真实排产失败: 'str' object has no attribute 'get'
2025-06-28 20:58:42,249 - INFO - ✅ 算法执行完成，生成 0 条结果。
2025-06-28 20:58:42,249 - ERROR - ❌ 排产算法未返回任何结果，验证终止。

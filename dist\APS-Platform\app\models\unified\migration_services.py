"""
数据迁移服务

提供批次数据和测试规范数据的迁移功能，支持从传统模型迁移到统一模型
"""

import json
import logging
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from app import db

from .unified_lot_model import UnifiedLotModel
from .unified_test_spec import UnifiedTestSpec

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_traditional_models():
    """延迟导入传统模型，避免循环导入"""
    try:
        # 直接从app.models导入
        import importlib
        models_module = importlib.import_module('app.models')
        
        # 如果app.models没有这些类，从app.models导入
        if not hasattr(models_module, 'LOT_WIP'):
            # 从根目录的models.py导入
            models_module = importlib.import_module('models')
        
        return {
            'LOT_WIP': getattr(models_module, 'LOT_WIP', None),
            'v_wip_lot_unified': getattr(models_module, 'v_wip_lot_unified', None),
            'v_et_wait_lot_unified': getattr(models_module, 'v_et_wait_lot_unified', None),
            'TestSpec': getattr(models_module, 'TestSpec', None),
            'Test_Spec': getattr(models_module, 'Test_Spec', None),
        }
    except Exception as e:
        logger.error(f"导入传统模型失败: {e}")
        return {}


class MigrationError(Exception):
    """迁移异常"""
    pass


class LotDataMigrationService:
    """批次数据迁移服务"""
    
    def __init__(self):
        self.migration_log = []
        self.error_log = []
        self.models = get_traditional_models()
    
    def migrate_all_lot_data(self, batch_size=100):
        """迁移所有批次数据"""
        logger.info("开始批次数据迁移...")
        
        try:
            # 按优先级顺序迁移
            et_wait_count = self.migrate_from_et_wait_lot(batch_size)
            lot_wip_count = self.migrate_from_lot_wip(batch_size)
            wip_lot_count = self.migrate_from_wip_lot(batch_size)
            
            total_migrated = et_wait_count + lot_wip_count + wip_lot_count
            
            logger.info(f"批次数据迁移完成，共迁移 {total_migrated} 条记录")
            logger.info(f"ET_WAIT_LOT: {et_wait_count}, LOT_WIP: {lot_wip_count}, WIP_LOT: {wip_lot_count}")
            
            return {
                'success': True,
                'total_migrated': total_migrated,
                'v_et_wait_lot_unified': et_wait_count,
                'lot_wip': lot_wip_count,
                'v_wip_lot_unified': wip_lot_count,
                'migration_log': self.migration_log,
                'error_log': self.error_log
            }
            
        except Exception as e:
            logger.error(f"批次数据迁移失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'migration_log': self.migration_log,
                'error_log': self.error_log
            }
    
    def migrate_from_et_wait_lot(self, batch_size=100):
        """从ET_WAIT_LOT迁移数据（优先级最高）"""
        logger.info("开始从ET_WAIT_LOT迁移数据...")
        migrated_count = 0
        
        ET_WAIT_LOT = self.models.get('v_et_wait_lot_unified')
        if not ET_WAIT_LOT:
            logger.warning("ET_WAIT_LOT模型不可用，跳过迁移")
            return 0
        
        try:
            # 分批处理
            offset = 0
            while True:
                records = ET_WAIT_LOT.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 检查是否已存在
                        existing = UnifiedLotModel.find_by_lot_id(record.LOT_ID)
                        if existing:
                            # 更新现有记录
                            existing.update_from_et_wait_lot(record)
                            existing.migration_status = 'updated'
                            existing.updated_at = datetime.utcnow()
                        else:
                            # 创建新记录
                            unified_lot = UnifiedLotModel()
                            unified_lot.update_from_et_wait_lot(record)
                            unified_lot.migration_status = 'migrated'
                            db.session.add(unified_lot)
                        
                        migrated_count += 1
                        self.migration_log.append(f"ET_WAIT_LOT: {record.LOT_ID} 迁移成功")
                        
                    except Exception as e:
                        error_msg = f"ET_WAIT_LOT记录 {record.LOT_ID} 迁移失败: {str(e)}"
                        logger.error(error_msg)
                        self.error_log.append(error_msg)
                
                # 提交当前批次
                db.session.commit()
                offset += batch_size
                logger.info(f"ET_WAIT_LOT已迁移 {migrated_count} 条记录")
            
            logger.info(f"ET_WAIT_LOT迁移完成，共 {migrated_count} 条记录")
            return migrated_count
            
        except Exception as e:
            db.session.rollback()
            raise MigrationError(f"ET_WAIT_LOT迁移失败: {str(e)}")
    
    def migrate_from_lot_wip(self, batch_size=100):
        """从LOT_WIP迁移数据（设备分配信息）"""
        logger.info("开始从LOT_WIP迁移数据...")
        migrated_count = 0
        
        LOT_WIP = self.models.get('LOT_WIP')
        if not LOT_WIP:
            logger.warning("LOT_WIP模型不可用，跳过迁移")
            return 0
        
        try:
            offset = 0
            while True:
                records = LOT_WIP.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 查找或创建统一记录
                        unified_lot = UnifiedLotModel.find_by_lot_id(record.LOT_ID)
                        if unified_lot:
                            # 更新设备分配信息
                            if not unified_lot.HANDLER_ID:
                                unified_lot.HANDLER_ID = record.HANDLER_ID
                            if not unified_lot.TESTER_ID:
                                unified_lot.TESTER_ID = record.TESTER_ID
                            if hasattr(record, 'ORDER_INDEX') and not unified_lot.PRIORITY_ORDER:
                                unified_lot.PRIORITY_ORDER = record.ORDER_INDEX
                            
                            # 更新其他缺失字段
                            if not unified_lot.DEVICE:
                                unified_lot.DEVICE = record.DEVICE
                            if not unified_lot.CHIP_ID:
                                unified_lot.CHIP_ID = record.CHIP_ID
                            if not unified_lot.PKG_PN:
                                unified_lot.PKG_PN = record.PKG_PN
                            if not unified_lot.STAGE:
                                unified_lot.STAGE = record.STAGE
                            if not unified_lot.PROD_ID:
                                unified_lot.PROD_ID = record.PROD_ID
                            
                            unified_lot.updated_at = datetime.utcnow()
                        else:
                            # 创建新记录
                            unified_lot = UnifiedLotModel()
                            unified_lot.update_from_lot_wip(record)
                            unified_lot.migration_status = 'migrated'
                            db.session.add(unified_lot)
                        
                        migrated_count += 1
                        self.migration_log.append(f"LOT_WIP: {record.LOT_ID} 迁移成功")
                        
                    except Exception as e:
                        error_msg = f"LOT_WIP记录 {record.LOT_ID} 迁移失败: {str(e)}"
                        logger.error(error_msg)
                        self.error_log.append(error_msg)
                
                # 提交当前批次
                db.session.commit()
                offset += batch_size
                logger.info(f"LOT_WIP已迁移 {migrated_count} 条记录")
            
            logger.info(f"LOT_WIP迁移完成，共 {migrated_count} 条记录")
            return migrated_count
            
        except Exception as e:
            db.session.rollback()
            raise MigrationError(f"LOT_WIP迁移失败: {str(e)}")
    
    def migrate_from_wip_lot(self, batch_size=100):
        """从WIP_LOT迁移数据（扩展信息）"""
        logger.info("开始从WIP_LOT迁移数据...")
        migrated_count = 0
        
        WIP_LOT = self.models.get('v_wip_lot_unified')
        if not WIP_LOT:
            logger.warning("WIP_LOT模型不可用，跳过迁移")
            return 0
        
        try:
            offset = 0
            while True:
                records = WIP_LOT.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 查找或创建统一记录
                        unified_lot = UnifiedLotModel.find_by_lot_id(record.LOT_ID)
                        if unified_lot:
                            # 更新扩展信息
                            unified_lot.update_from_wip_lot(record)
                            unified_lot.updated_at = datetime.utcnow()
                        else:
                            # 创建新记录
                            unified_lot = UnifiedLotModel()
                            unified_lot.update_from_wip_lot(record)
                            unified_lot.migration_status = 'migrated'
                            db.session.add(unified_lot)
                        
                        migrated_count += 1
                        self.migration_log.append(f"WIP_LOT: {record.LOT_ID} 迁移成功")
                        
                    except Exception as e:
                        error_msg = f"WIP_LOT记录 {record.LOT_ID} 迁移失败: {str(e)}"
                        logger.error(error_msg)
                        self.error_log.append(error_msg)
                
                # 提交当前批次
                db.session.commit()
                offset += batch_size
                logger.info(f"WIP_LOT已迁移 {migrated_count} 条记录")
            
            logger.info(f"WIP_LOT迁移完成，共 {migrated_count} 条记录")
            return migrated_count
            
        except Exception as e:
            db.session.rollback()
            raise MigrationError(f"WIP_LOT迁移失败: {str(e)}")
    
    def verify_lot_data_consistency(self):
        """验证批次数据一致性"""
        logger.info("开始验证批次数据一致性...")
        
        try:
            # 统计原始表记录数
            et_wait_count = self.models.get('v_et_wait_lot_unified').query.count()
            lot_wip_count = self.models.get('LOT_WIP').query.count()
            wip_lot_count = self.models.get('v_wip_lot_unified').query.count()
            
            # 统计统一表记录数
            unified_count = UnifiedLotModel.query.count()
            
            # 检查关键字段完整性
            missing_lot_ids = []
            
            # 检查ET_WAIT_LOT的LOT_ID是否都在统一表中
            for record in self.models.get('v_et_wait_lot_unified').query.all():
                if not UnifiedLotModel.find_by_lot_id(record.LOT_ID):
                    missing_lot_ids.append(f"ET_WAIT_LOT: {record.LOT_ID}")
            
            # 检查LOT_WIP的LOT_ID是否都在统一表中
            for record in self.models.get('LOT_WIP').query.all():
                if not UnifiedLotModel.find_by_lot_id(record.LOT_ID):
                    missing_lot_ids.append(f"LOT_WIP: {record.LOT_ID}")
            
            verification_result = {
                'success': len(missing_lot_ids) == 0,
                'original_counts': {
                    'v_et_wait_lot_unified': et_wait_count,
                    'lot_wip': lot_wip_count,
                    'v_wip_lot_unified': wip_lot_count
                },
                'unified_count': unified_count,
                'missing_lot_ids': missing_lot_ids,
                'consistency_rate': (unified_count / max(et_wait_count, 1)) * 100
            }
            
            logger.info(f"数据一致性验证完成: {verification_result}")
            return verification_result
            
        except Exception as e:
            logger.error(f"数据一致性验证失败: {str(e)}")
            return {'success': False, 'error': str(e)}


class TestSpecMigrationService:
    """测试规范迁移服务"""
    
    def __init__(self):
        self.migration_log = []
        self.error_log = []
        self.models = get_traditional_models()
    
    def migrate_all_test_spec_data(self, batch_size=100):
        """迁移所有测试规范数据"""
        logger.info("开始测试规范数据迁移...")
        
        try:
            # 按优先级顺序迁移
            test_spec_count = self.migrate_from_test_spec(batch_size)
            testspec_count = self.migrate_from_testspec(batch_size)
            
            total_migrated = test_spec_count + testspec_count
            
            logger.info(f"测试规范数据迁移完成，共迁移 {total_migrated} 条记录")
            logger.info(f"Test_Spec: {test_spec_count}, TestSpec: {testspec_count}")
            
            return {
                'success': True,
                'total_migrated': total_migrated,
                'test_spec': test_spec_count,
                'testspec': testspec_count,
                'migration_log': self.migration_log,
                'error_log': self.error_log
            }
            
        except Exception as e:
            logger.error(f"测试规范数据迁移失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'migration_log': self.migration_log,
                'error_log': self.error_log
            }
    
    def migrate_from_test_spec(self, batch_size=100):
        """从Test_Spec迁移数据（实际业务使用）"""
        logger.info("开始从Test_Spec迁移数据...")
        migrated_count = 0
        
        Test_Spec = self.models.get('Test_Spec')
        if not Test_Spec:
            logger.warning("Test_Spec模型不可用，跳过迁移")
            return 0
        
        try:
            offset = 0
            while True:
                records = Test_Spec.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 检查是否已存在
                        existing = UnifiedTestSpec.find_by_spec_id(record.TEST_SPEC_ID)
                        if existing:
                            # 更新现有记录
                            existing.update_from_test_spec(record)
                            existing.migration_status = 'updated'
                            existing.updated_at = datetime.utcnow()
                        else:
                            # 创建新记录
                            unified_spec = UnifiedTestSpec()
                            unified_spec.update_from_test_spec(record)
                            unified_spec.migration_status = 'migrated'
                            db.session.add(unified_spec)
                        
                        migrated_count += 1
                        self.migration_log.append(f"Test_Spec: {record.TEST_SPEC_ID} 迁移成功")
                        
                    except Exception as e:
                        error_msg = f"Test_Spec记录 {record.TEST_SPEC_ID} 迁移失败: {str(e)}"
                        logger.error(error_msg)
                        self.error_log.append(error_msg)
                
                # 提交当前批次
                db.session.commit()
                offset += batch_size
                logger.info(f"Test_Spec已迁移 {migrated_count} 条记录")
            
            logger.info(f"Test_Spec迁移完成，共 {migrated_count} 条记录")
            return migrated_count
            
        except Exception as e:
            db.session.rollback()
            raise MigrationError(f"Test_Spec迁移失败: {str(e)}")
    
    def migrate_from_testspec(self, batch_size=100):
        """从TestSpec迁移数据（现代化设计）"""
        logger.info("开始从TestSpec迁移数据...")
        migrated_count = 0
        
        TestSpec = self.models.get('TestSpec')
        if not TestSpec:
            logger.warning("TestSpec模型不可用，跳过迁移")
            return 0
        
        try:
            offset = 0
            while True:
                records = TestSpec.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 检查是否已存在（使用name作为ID）
                        existing = UnifiedTestSpec.find_by_spec_id(record.name)
                        if existing and existing.source_table != 'Test_Spec':
                            # 如果已存在且不是来自Test_Spec，则更新
                            existing.update_from_testspec(record)
                            existing.migration_status = 'updated'
                            existing.updated_at = datetime.utcnow()
                        elif not existing:
                            # 创建新记录
                            unified_spec = UnifiedTestSpec()
                            unified_spec.update_from_testspec(record)
                            unified_spec.migration_status = 'migrated'
                            db.session.add(unified_spec)
                        
                        migrated_count += 1
                        self.migration_log.append(f"TestSpec: {record.name} 迁移成功")
                        
                    except Exception as e:
                        error_msg = f"TestSpec记录 {record.name} 迁移失败: {str(e)}"
                        logger.error(error_msg)
                        self.error_log.append(error_msg)
                
                # 提交当前批次
                db.session.commit()
                offset += batch_size
                logger.info(f"TestSpec已迁移 {migrated_count} 条记录")
            
            logger.info(f"TestSpec迁移完成，共 {migrated_count} 条记录")
            return migrated_count
            
        except Exception as e:
            db.session.rollback()
            raise MigrationError(f"TestSpec迁移失败: {str(e)}")
    
    def verify_test_spec_consistency(self):
        """验证测试规范数据一致性"""
        logger.info("开始验证测试规范数据一致性...")
        
        try:
            # 统计原始表记录数
            Test_Spec = self.models.get('Test_Spec')
            TestSpec = self.models.get('TestSpec')
            
            test_spec_count = Test_Spec.query.count() if Test_Spec else 0
            testspec_count = TestSpec.query.count() if TestSpec else 0
            
            # 统计统一表记录数
            unified_count = UnifiedTestSpec.query.count()
            
            # 检查关键字段完整性
            missing_spec_ids = []
            
            # 检查Test_Spec的TEST_SPEC_ID是否都在统一表中
            if Test_Spec:
                for record in Test_Spec.query.all():
                    if not UnifiedTestSpec.find_by_spec_id(record.TEST_SPEC_ID):
                        missing_spec_ids.append(f"Test_Spec: {record.TEST_SPEC_ID}")
            
            verification_result = {
                'success': len(missing_spec_ids) == 0,
                'original_counts': {
                    'test_spec': test_spec_count,
                    'testspec': testspec_count
                },
                'unified_count': unified_count,
                'missing_spec_ids': missing_spec_ids,
                'consistency_rate': (unified_count / max(test_spec_count, 1)) * 100
            }
            
            logger.info(f"测试规范数据一致性验证完成: {verification_result}")
            return verification_result
            
        except Exception as e:
            logger.error(f"测试规范数据一致性验证失败: {str(e)}")
            return {'success': False, 'error': str(e)}


class DataMigrationManager:
    """数据迁移管理器"""
    
    def __init__(self):
        self.lot_service = LotDataMigrationService()
        self.spec_service = TestSpecMigrationService()
    
    def migrate_all_data(self, batch_size=100):
        """迁移所有数据"""
        logger.info("开始完整数据迁移...")
        
        results = {
            'start_time': datetime.utcnow().isoformat(),
            'lot_migration': None,
            'spec_migration': None,
            'verification': None,
            'success': False
        }
        
        try:
            # 1. 迁移批次数据
            logger.info("第1步：迁移批次数据")
            results['lot_migration'] = self.lot_service.migrate_all_lot_data(batch_size)
            
            # 2. 迁移测试规范数据
            logger.info("第2步：迁移测试规范数据")
            results['spec_migration'] = self.spec_service.migrate_all_test_spec_data(batch_size)
            
            # 3. 验证数据一致性
            logger.info("第3步：验证数据一致性")
            lot_verification = self.lot_service.verify_lot_data_consistency()
            spec_verification = self.spec_service.verify_test_spec_consistency()
            
            results['verification'] = {
                'lot_verification': lot_verification,
                'spec_verification': spec_verification
            }
            
            # 判断整体成功状态
            results['success'] = (
                results['lot_migration']['success'] and
                results['spec_migration']['success'] and
                lot_verification['success'] and
                spec_verification['success']
            )
            
            results['end_time'] = datetime.utcnow().isoformat()
            
            if results['success']:
                logger.info("完整数据迁移成功完成！")
            else:
                logger.warning("数据迁移完成，但存在部分问题，请检查详细日志")
            
            return results
            
        except Exception as e:
            logger.error(f"完整数据迁移失败: {str(e)}")
            results['error'] = str(e)
            results['end_time'] = datetime.utcnow().isoformat()
            return results
    
    def create_migration_report(self, results):
        """创建迁移报告"""
        report = []
        report.append("=" * 60)
        report.append("数据模型优化 - 迁移报告")
        report.append("=" * 60)
        report.append(f"开始时间: {results.get('start_time', 'N/A')}")
        report.append(f"结束时间: {results.get('end_time', 'N/A')}")
        report.append(f"整体状态: {'成功' if results.get('success') else '失败'}")
        report.append("")
        
        # 批次数据迁移结果
        if results.get('lot_migration'):
            lot_result = results['lot_migration']
            report.append("批次数据迁移结果:")
            report.append(f"  状态: {'成功' if lot_result['success'] else '失败'}")
            if lot_result['success']:
                report.append(f"  总计迁移: {lot_result['total_migrated']} 条记录")
                report.append(f"  ET_WAIT_LOT: {lot_result['v_et_wait_lot_unified']} 条")
                report.append(f"  LOT_WIP: {lot_result['lot_wip']} 条")
                report.append(f"  WIP_LOT: {lot_result['v_wip_lot_unified']} 条")
            else:
                report.append(f"  错误: {lot_result.get('error', 'N/A')}")
            report.append("")
        
        # 测试规范迁移结果
        if results.get('spec_migration'):
            spec_result = results['spec_migration']
            report.append("测试规范迁移结果:")
            report.append(f"  状态: {'成功' if spec_result['success'] else '失败'}")
            if spec_result['success']:
                report.append(f"  总计迁移: {spec_result['total_migrated']} 条记录")
                report.append(f"  Test_Spec: {spec_result['test_spec']} 条")
                report.append(f"  TestSpec: {spec_result['testspec']} 条")
            else:
                report.append(f"  错误: {spec_result.get('error', 'N/A')}")
            report.append("")
        
        # 数据一致性验证结果
        if results.get('verification'):
            verification = results['verification']
            report.append("数据一致性验证:")
            
            if verification.get('lot_verification'):
                lot_verify = verification['lot_verification']
                report.append(f"  批次数据一致性: {'通过' if lot_verify['success'] else '失败'}")
                if lot_verify['success']:
                    report.append(f"    一致性率: {lot_verify['consistency_rate']:.2f}%")
                else:
                    report.append(f"    缺失记录: {len(lot_verify.get('missing_lot_ids', []))}")
            
            if verification.get('spec_verification'):
                spec_verify = verification['spec_verification']
                report.append(f"  测试规范一致性: {'通过' if spec_verify['success'] else '失败'}")
                if spec_verify['success']:
                    report.append(f"    一致性率: {spec_verify['consistency_rate']:.2f}%")
                else:
                    report.append(f"    缺失记录: {len(spec_verify.get('missing_spec_ids', []))}")
        
        report.append("=" * 60)
        
        return "\n".join(report)
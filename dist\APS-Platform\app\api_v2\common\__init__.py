"""
通用API模块 - 提供基础的API功能
"""

from flask import Blueprint, jsonify, request
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 创建通用API蓝图
common_v2_bp = Blueprint('common_v2', __name__, url_prefix='/common')

@common_v2_bp.route('/status')
def system_status():
    """系统状态检查"""
    try:
        # 检查数据库连接
        from app import db
        db.session.execute('SELECT 1')
        db_status = 'connected'
    except Exception as e:
        db_status = f'error: {str(e)}'
        logger.error(f"数据库连接检查失败: {e}")
    
    return jsonify({
        'timestamp': datetime.now().isoformat(),
        'status': 'running',
        'database': db_status,
        'api_version': '2.0'
    })

def get_config():
    """获取系统配置信息"""
    from app.config.feature_flags import feature_manager
    
    return jsonify({
        'feature_flags': feature_manager.get_all_flags(),
        'migration_phase': feature_manager.get_migration_phase(),
        'migration_complete': feature_manager.is_migration_complete(),
        'emergency_rollback': feature_manager.is_emergency_rollback()
    })

@common_v2_bp.route('/time')
def get_server_time():
    """获取服务器时间"""
    return jsonify({
        'server_time': datetime.now().isoformat(),
        'timezone': 'Asia/Shanghai'
    })

@common_v2_bp.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'error': 'Not Found',
        'message': 'The requested resource was not found',
        'status_code': 404
    }), 404

@common_v2_bp.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'error': 'Internal Server Error',
        'message': 'An internal server error occurred',
        'status_code': 500
    }), 500 
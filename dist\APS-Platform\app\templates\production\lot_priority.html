{% extends "resources/base_resource.html" %}

{% set page_title = "批次优先级配置" %}
{% set table_title = "批次优先级配置" %}
{% set page_description = "管理批次优先级配置，支持Excel批量导入和实时编辑。包含产品名称、工序、优先级等信息。" %}
{% set table_name = "lotpriorityconfig" %}

{% block extra_css %}
{{ super() }}
<style>
/* Excel上传按钮 */
.excel-upload-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s;
}

.excel-upload-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
}

/* 批次优先级配置专用样式 */
.stage-ft { background-color: #fff3cd; }
.stage-cp { background-color: #d1ecf1; }
.stage-other { background-color: #d4edda; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">{{ page_title }}</h5>
                        <div>
                            <button type="button" class="btn excel-upload-btn me-2" onclick="showExcelUploadModal()">
                                <i class="fas fa-file-excel me-1"></i>Excel导入
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-info me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                        </div>
                    </div>
                    


                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">{{ table_title }}数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Excel上传模态框 -->
<div class="modal fade" id="excelUploadModal" tabindex="-1" aria-labelledby="excelUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="excelUploadModalLabel">Excel文件上传</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="excelFiles" class="form-label">选择Excel文件</label>
                    <input class="form-control" type="file" id="excelFiles" accept=".xlsx,.xls">
                    <div class="form-text">
                        只允许上传单个Excel文件，且文件名必须包含"lot"关键字。
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>批次优先级配置文件格式说明</h6>
                    <p><strong>必需字段：</strong>DEVICE(产品名称), PRIORITY(优先级)</p>
                    <p><strong>可选字段：</strong>STAGE(工序), REFRESH_TIME(刷新时间), USER(用户)</p>
                    <p><strong>文件命名：</strong>文件名包含"lot"关键字即可识别为批次优先级配置</p>
                </div>

                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="uploadStatus">准备上传...</small>
                </div>

                <div class="upload-result" id="uploadResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadExcelFiles()">开始上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑记录模态框 -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">编辑记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="formFields">
                        <!-- 表单字段将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRecord()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条记录吗？此操作不可撤销。</p>
                <div id="deleteRecordInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="batchDeleteCount">0</span> 条记录吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// Excel上传相关函数
function showExcelUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('excelUploadModal'));
    modal.show();
    
    // 重置表单
    document.getElementById('excelFiles').value = '';
    document.getElementById('uploadProgress').style.display = 'none';
    document.getElementById('uploadResult').style.display = 'none';
}

function uploadExcelFiles() {
    const fileInput = document.getElementById('excelFiles');
    const files = fileInput.files;
    
    if (files.length === 0) {
        alert('请选择要上传的Excel文件');
        return;
    }
    
    // 验证文件名是否包含"lot"关键字
    const file = files[0];
    const fileName = file.name.toLowerCase();
    if (!fileName.includes('lot')) {
        alert('文件名必须包含"lot"关键字，请重新选择正确的批次优先级配置文件');
        return;
    }
    
    const progressBar = document.querySelector('.progress-bar');
    const progressContainer = document.getElementById('uploadProgress');
    const statusText = document.getElementById('uploadStatus');
    const resultContainer = document.getElementById('uploadResult');
    
    // 显示进度条
    progressContainer.style.display = 'block';
    resultContainer.style.display = 'none';
    progressBar.style.width = '0%';
    statusText.textContent = '准备上传...';
    
    // 禁用上传按钮
    const uploadBtn = document.querySelector('#excelUploadModal .btn-primary');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';
    
    // 创建FormData
    const formData = new FormData();
    formData.append('files', files[0]); // 只上传单个文件
    
    // 上传文件
    fetch('/api/v2/production/priority-settings/upload', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        progressBar.style.width = '100%';
        statusText.textContent = '上传完成';
        
        // 显示结果
        resultContainer.style.display = 'block';
        if (result.success) {
            resultContainer.className = 'alert alert-success';
            resultContainer.innerHTML = `
                <h6><i class="fas fa-check-circle me-1"></i>上传成功</h6>
                <p>共处理 ${result.total_processed || 0} 条记录</p>
                ${result.results ? result.results.map(r => `<p><strong>${r.filename}:</strong> ${r.success ? '成功' : '失败'} ${r.success ? `(${r.imported_count}条)` : `- ${r.error}`}</p>`).join('') : ''}
            `;
            
            // 刷新数据
            setTimeout(() => {
                loadData();
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('excelUploadModal'));
                modal.hide();
            }, 2000);
        } else {
            resultContainer.className = 'alert alert-danger';
            resultContainer.innerHTML = `
                <h6><i class="fas fa-exclamation-triangle me-1"></i>上传失败</h6>
                <p>${result.error || '未知错误'}</p>
            `;
        }
    })
    .catch(error => {
        console.error('上传失败:', error);
        
        progressBar.style.width = '100%';
        progressBar.className = 'progress-bar bg-danger';
        statusText.textContent = '上传失败';
        
        resultContainer.style.display = 'block';
        resultContainer.className = 'alert alert-danger';
        resultContainer.innerHTML = `
            <h6><i class="fas fa-exclamation-triangle me-1"></i>上传失败</h6>
            <p>${error.message}</p>
        `;
    })
    .finally(() => {
        // 恢复上传按钮
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '开始上传';
    });
}
</script>
{% endblock %}

2025-06-28 19:58:20,907 - INFO - 🔄 加载验证数据...
2025-06-28 19:58:21,255 - INFO - ✅ 加载待排产数据: 409 条记录
2025-06-28 19:58:21,357 - INFO - ✅ 加载期望结果: 409 条记录
2025-06-28 19:58:21,357 - INFO - 🔄 预处理验证数据...
2025-06-28 19:58:21,368 - INFO - ✅ 预处理完成，创建期望结果索引: 409 个键值对
2025-06-28 19:58:21,368 - INFO - 🚀 开始运行全面验证实验...
2025-06-28 19:58:21,369 - INFO - 🚀 开始运行算法测试...
2025-06-28 19:58:22,238 - INFO - 系统模型包已禁用，请直接从app.models导入
2025-06-28 19:58:22,283 - ERROR - ❌ 算法执行失败: expected an indented block after 'if' statement on line 1436 (data_source_manager.py, line 1437)
2025-06-28 19:58:22,286 - ERROR - ❌ 算法执行失败，无法继续验证
2025-06-28 19:59:05,506 - INFO - 🔄 加载验证数据...
2025-06-28 19:59:05,825 - INFO - ✅ 加载待排产数据: 409 条记录
2025-06-28 19:59:05,929 - INFO - ✅ 加载期望结果: 409 条记录
2025-06-28 19:59:05,929 - INFO - 🔄 预处理验证数据...
2025-06-28 19:59:05,940 - INFO - ✅ 预处理完成，创建期望结果索引: 409 个键值对
2025-06-28 19:59:05,940 - INFO - 🚀 开始运行全面验证实验...
2025-06-28 19:59:05,941 - INFO - 🚀 开始运行算法测试...
2025-06-28 19:59:06,731 - INFO - 系统模型包已禁用，请直接从app.models导入
2025-06-28 19:59:06,749 - ERROR - ❌ 算法执行失败: 'utf-8' codec can't decode bytes in position 30-31: unexpected end of data
2025-06-28 19:59:06,753 - ERROR - ❌ 算法执行失败，无法继续验证
2025-06-28 20:55:49,058 - INFO - 🔄 加载验证数据...
2025-06-28 20:55:49,065 - ERROR - ❌ 加载验证数据失败: [Errno 2] No such file or directory: 'Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx'

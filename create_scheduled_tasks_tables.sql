-- 后端定时任务系统数据库表结构
-- 用于替代前端localStorage存储

-- 1. 定时任务配置表
CREATE TABLE IF NOT EXISTS `scheduled_tasks` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `task_id` VARCHAR(255) NOT NULL UNIQUE COMMENT '任务唯一标识',
    `name` VARCHAR(255) NOT NULL COMMENT '任务名称',
    `type` ENUM('once', 'daily', 'weekly', 'interval') NOT NULL COMMENT '任务类型',
    `config_data` JSON COMMENT '任务配置数据(JSON格式)',
    `status` ENUM('active', 'paused', 'completed', 'error') DEFAULT 'active' COMMENT '任务状态',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务配置表';

-- 2. 任务执行日志表
CREATE TABLE IF NOT EXISTS `task_execution_logs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `execution_id` VARCHAR(255) NOT NULL COMMENT '执行ID',
    `task_name` VARCHAR(255) NOT NULL COMMENT '任务名称',
    `status` ENUM('started', 'completed', 'failed', 'error') NOT NULL COMMENT '执行状态',
    `started_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `finished_at` DATETIME NULL COMMENT '结束时间',
    `config_data` JSON COMMENT '任务配置数据',
    `result_data` JSON COMMENT '执行结果数据',
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_task_name` (`task_name`),
    INDEX `idx_status` (`status`),
    INDEX `idx_started_at` (`started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行日志表';

-- 3. APScheduler作业存储表 (APScheduler自动创建，这里仅作参考)
-- CREATE TABLE IF NOT EXISTS `apscheduler_jobs` (
--     `id` VARCHAR(191) PRIMARY KEY,
--     `next_run_time` DOUBLE NULL,
--     `job_state` BLOB NOT NULL
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入示例数据 (可选)
-- INSERT INTO `scheduled_tasks` (`task_id`, `name`, `type`, `config_data`) VALUES
-- (
--     'example_daily_task',
--     '每日自动排产',
--     'daily',
--     JSON_OBJECT(
--         'hour', 9,
--         'minute', 0,
--         'strategy', 'intelligent',
--         'target', 'balanced',
--         'autoImport', true,
--         'emailNotification', false
--     )
-- );

-- 查询示例
-- 查看所有活跃任务
-- SELECT task_id, name, type, status, created_at FROM scheduled_tasks WHERE status = 'active';

-- 查看最近的执行日志
-- SELECT task_name, status, started_at, finished_at FROM task_execution_logs ORDER BY started_at DESC LIMIT 10;

2025-06-28 20:59:25,100 - INFO - 🚀 开始运行算法验证...
2025-06-28 20:59:25,100 - INFO - 🔄 加载验证数据...
2025-06-28 20:59:25,100 - INFO - 加载待排产批次从: Excellist2025.06.05/排产验证/ET_WAIT_LOT.xlsx
2025-06-28 20:59:25,417 - INFO - ✅ 成功加载 409 条待排产批次
2025-06-28 20:59:25,423 - INFO - 加载期望结果从: Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx
2025-06-28 20:59:25,529 - INFO - ✅ 成功加载 409 条期望结果
2025-06-28 20:59:25,534 - INFO - ✅ 验证数据加载成功。
2025-06-28 20:59:25,534 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:59:25,535 - INFO - 🏃‍♂️ 运行核心排产算法...
2025-06-28 20:59:26,332 - INFO - 系统模型包已禁用，请直接从app.models导入
2025-06-28 20:59:26,369 - INFO - 🚀 开始执行真实排产，算法: intelligent
2025-06-28 20:59:26,370 - INFO - 📦 使用传入的 409 条批次数据进行排产
2025-06-28 20:59:26,376 - INFO - ✅ MySQL数据源可用
2025-06-28 20:59:26,376 - INFO - 🔄 缓存更新: test_spec_data
2025-06-28 20:59:26,504 - INFO - 从MySQL获取到 555 条测试规范数据
2025-06-28 20:59:26,523 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,523 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,524 - WARNING - 批次 YX2500000883 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,524 - WARNING - ⚠️ 批次 YX2500000883 配置需求获取失败，跳过
2025-06-28 20:59:26,526 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,526 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,527 - WARNING - 批次 YX2500000884 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,527 - WARNING - ⚠️ 批次 YX2500000884 配置需求获取失败，跳过
2025-06-28 20:59:26,527 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,527 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,528 - WARNING - 批次 YX2500000886 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,528 - WARNING - ⚠️ 批次 YX2500000886 配置需求获取失败，跳过
2025-06-28 20:59:26,528 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,529 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,529 - WARNING - 批次 YX2500000887 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,530 - WARNING - ⚠️ 批次 YX2500000887 配置需求获取失败，跳过
2025-06-28 20:59:26,530 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,530 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,531 - WARNING - 批次 YX2500000939 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,531 - WARNING - ⚠️ 批次 YX2500000939 配置需求获取失败，跳过
2025-06-28 20:59:26,531 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,531 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,532 - WARNING - 批次 YX2500000940 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,533 - WARNING - ⚠️ 批次 YX2500000940 配置需求获取失败，跳过
2025-06-28 20:59:26,533 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,533 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,534 - WARNING - 批次 YX2500000941 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,534 - WARNING - ⚠️ 批次 YX2500000941 配置需求获取失败，跳过
2025-06-28 20:59:26,534 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,535 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,535 - WARNING - 批次 YX2500000943 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,535 - WARNING - ⚠️ 批次 YX2500000943 配置需求获取失败，跳过
2025-06-28 20:59:26,536 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,536 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,536 - WARNING - 批次 YX2500000944 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,537 - WARNING - ⚠️ 批次 YX2500000944 配置需求获取失败，跳过
2025-06-28 20:59:26,537 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,537 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,538 - WARNING - 批次 YX2500001354 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,538 - WARNING - ⚠️ 批次 YX2500001354 配置需求获取失败，跳过
2025-06-28 20:59:26,538 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,538 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,539 - WARNING - 批次 YX2500001355 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,539 - WARNING - ⚠️ 批次 YX2500001355 配置需求获取失败，跳过
2025-06-28 20:59:26,539 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,540 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,540 - WARNING - 批次 YX2500001356 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,541 - WARNING - ⚠️ 批次 YX2500001356 配置需求获取失败，跳过
2025-06-28 20:59:26,541 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,541 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,542 - WARNING - 批次 YX2500001357 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,542 - WARNING - ⚠️ 批次 YX2500001357 配置需求获取失败，跳过
2025-06-28 20:59:26,542 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,542 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,543 - WARNING - 批次 YX2300000310 未找到匹配的测试规范 (DEVICE=JWQ5103ASQFNAT_TA0, STAGE=BTT)
2025-06-28 20:59:26,543 - WARNING - ⚠️ 批次 YX2300000310 配置需求获取失败，跳过
2025-06-28 20:59:26,543 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,544 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,544 - WARNING - 批次 YX2500001202 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,544 - WARNING - ⚠️ 批次 YX2500001202 配置需求获取失败，跳过
2025-06-28 20:59:26,545 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,545 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,546 - WARNING - 批次 YX2300000311 未找到匹配的测试规范 (DEVICE=JWQ5103CSFQFNAT_TA0, STAGE=BTT)
2025-06-28 20:59:26,546 - WARNING - ⚠️ 批次 YX2300000311 配置需求获取失败，跳过
2025-06-28 20:59:26,546 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,546 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,547 - WARNING - 批次 YX2300000312 未找到匹配的测试规范 (DEVICE=JWQ5103CSQFNAT_TA0, STAGE=BTT)
2025-06-28 20:59:26,547 - WARNING - ⚠️ 批次 YX2300000312 配置需求获取失败，跳过
2025-06-28 20:59:26,548 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,548 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,549 - WARNING - 批次 YX2400000583 未找到匹配的测试规范 (DEVICE=JW3655E, STAGE=BTT)
2025-06-28 20:59:26,549 - WARNING - ⚠️ 批次 YX2400000583 配置需求获取失败，跳过
2025-06-28 20:59:26,549 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,549 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,550 - WARNING - 批次 YX2400000585 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,550 - WARNING - ⚠️ 批次 YX2400000585 配置需求获取失败，跳过
2025-06-28 20:59:26,551 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,551 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,551 - WARNING - 批次 YX2400000603 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,552 - WARNING - ⚠️ 批次 YX2400000603 配置需求获取失败，跳过
2025-06-28 20:59:26,552 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,552 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,552 - WARNING - 批次 YX2400000605 未找到匹配的测试规范 (DEVICE=JWQ5102ASQFNAT_TR0, STAGE=BTT)
2025-06-28 20:59:26,553 - WARNING - ⚠️ 批次 YX2400000605 配置需求获取失败，跳过
2025-06-28 20:59:26,553 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,553 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,554 - WARNING - 批次 YX24XX040012 未找到匹配的测试规范 (DEVICE=JW3655E, STAGE=BTT)
2025-06-28 20:59:26,554 - WARNING - ⚠️ 批次 YX24XX040012 配置需求获取失败，跳过
2025-06-28 20:59:26,554 - INFO - 🔬 从MySQL获取到 555 条测试规范数据（缓存）
2025-06-28 20:59:26,555 - INFO - 表格 ET_FT_TEST_SPEC: 总计 555 条记录, 显示 555 条
2025-06-28 20:59:26,555 - INFO - 🔄 缓存更新: uph_data
2025-06-28 20:59:26,601 - INFO - 从MySQL获取到 1759 条UPH数据
2025-06-28 20:59:26,614 - INFO - ⚡ 从MySQL获取到 1759 条UPH数据（缓存）
2025-06-28 20:59:26,614 - ERROR - 获取工艺配方数据时发生未知错误: 'DataSourceManager' object has no attribute '_fetch_from_mysql'
2025-06-28 20:59:26,615 - ERROR - 获取KIT配置失败: not enough values to unpack (expected 2, got 0)
2025-06-28 20:59:26,615 - INFO - Lot YX2500001955: 查找设备, 需求: {'DEVICE': 'JWH7069TLGAA-M001', 'STAGE': 'UIS', 'HB_PN': '20220182_B', 'TB_PN': 'TB_CC4812A_V41', 'HANDLER_CONFIG': 'PnP', 'PKG_PN': 'TLGA5*6-41L', 'TESTER': 'CTA8280F', 'UPH': '2360', 'TEST_SPEC_SOURCE': 'ET_FT_TEST_SPEC'}
2025-06-28 20:59:26,616 - INFO - 🔄 从eqp_status获取可用设备...
2025-06-28 20:59:26,616 - INFO - 🔄 缓存更新: equipment_status_data
2025-06-28 20:59:26,642 - INFO - 从MySQL获取到 67 条设备状态数据
2025-06-28 20:59:26,643 - INFO - 🏭 从MySQL获取到 67 条设备状态数据（缓存）
2025-06-28 20:59:26,644 - INFO - 表格 eqp_status: 总计 67 条记录, 显示 67 条
2025-06-28 20:59:26,644 - ERROR - ❌ 执行真实排产失败: 'str' object has no attribute 'get'
2025-06-28 20:59:26,648 - INFO - ✅ 算法执行完成，生成 0 条结果。
2025-06-28 20:59:26,649 - ERROR - ❌ 排产算法未返回任何结果，验证终止。

"""
数据源管理器 - 支持MySQL和Excel数据源的智能切换
当MySQL数据有问题时，自动切换到Excel数据源
"""

import logging
import os
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import text
from app import db
from app.utils.db_helper import get_mysql_connection

logger = logging.getLogger(__name__)

# 在类的开头添加唯一字段映射配置
UNIQUE_FIELD_MAPPING = {
    'eqp_status': {
        'primary_key': 'id',  # 主键
        'business_key': 'HANDLER_ID',  # 业务主键
        'display_key': 'EQP_ID',  # 显示主键
        'datetime_fields': ['created_at', 'updated_at', 'UPDATE_TIME']  # 日期时间字段
    },
    'et_ft_test_spec': {
        'primary_key': 'id',
        'business_key': 'TEST_SPEC_ID',
        'display_key': 'TEST_SPEC_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'et_uph_eqp': {
        'primary_key': 'id',
        'business_key': 'DEVICE',  # 组合业务键需要额外处理
        'display_key': 'DEVICE',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'ET_UPH_EQP': {  # 大写版本映射
        'primary_key': 'id',
        'business_key': 'DEVICE',
        'display_key': 'DEVICE',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'ct': {
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'CT': {  # 大写版本映射
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'TCC_INV': {
        'primary_key': 'id',
        'business_key': 'id',  # 没有明显业务键，使用ID
        'display_key': 'id',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'et_wait_lot': {
        'primary_key': 'id',  # 实际主键是id，不是LOT_ID
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at', 'CREATE_TIME']
    },
    'lotprioritydone': {
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at', 'timestamp']
    },
    'devicepriorityconfig': {
        'primary_key': 'id',
        'business_key': 'device',
        'display_key': 'device',
        'datetime_fields': ['created_at', 'updated_at', 'from_time', 'end_time', 'refresh_time']
    },
    'lotpriorityconfig': {
        'primary_key': 'id',
        'business_key': 'device',
        'display_key': 'device',
        'datetime_fields': ['created_at', 'updated_at', 'refresh_time']
    }
}

class DataSourceManager:
    """数据源管理器 - 智能切换MySQL和Excel数据源"""
    

    # 缓存机制优化
    _cache = {}
    _cache_timeout = 300  # 5分钟缓存（默认）
    
    # 不同类型数据的缓存超时配置（修复版 - 正确映射数据库表名）
    _cache_timeouts = {
        # aps_system 数据库中的表（优先级配置）
        'devicepriorityconfig': 300,         # 5分钟 - 设备优先级配置
        'lotpriorityconfig': 300,            # 5分钟 - 批次优先级配置
        
        # aps 数据库中的表（业务数据）
        'eqp_status': 60,                    # 1分钟 - 设备状态变化频繁
        'et_wait_lot': 30,                   # 30秒 - 待排产数据变化很频繁
        'et_ft_test_spec': 300,              # 5分钟 - 测试规格相对稳定
        'et_uph_eqp': 300,                   # 5分钟 - UPH数据相对稳定
        'et_recipe_file': 600,               # 10分钟 - 配方文件变化较少
        'lotprioritydone': 120,              # 2分钟 - 已排产批次数据
        
        # 缓存键名（向后兼容）
        'device_priority_config': 300,       # 5分钟 - 配置数据需要及时更新
        'lot_priority_config': 300,          # 5分钟 - 批次优先级配置
        'equipment_status_data': 60,         # 1分钟 - 设备状态变化频繁
        'wait_lot_data': 30,                 # 30秒 - 待排产数据变化很频繁
        'test_spec_data': 300,               # 5分钟 - 测试规格相对稳定
        'uph_data': 300,                     # 5分钟 - UPH数据相对稳定
        'recipe_file_data': 600,             # 10分钟 - 配方文件变化较少
        'lotprioritydone_data': 120,         # 2分钟 - 已排产批次数据
    }
    
    # 缓存版本控制 - 用于检测数据变化
    _cache_versions = {}
    
    def _normalize_datetime_field(self, value):
        """智能日期时间字段标准化"""
        if value is None or value == '' or value == 'None':
            return None
            
        # 如果已经是datetime对象，直接格式化
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d %H:%M:%S')
            
        # 转换字符串格式
        try:
            from datetime import datetime
            import re
            
            # 处理GMT格式: 'Tue, 17 Jun 2025 17:17:22 GMT'
            if isinstance(value, str) and 'GMT' in value:
                # 移除星期和GMT，提取核心日期时间
                clean_value = re.sub(r'^[A-Za-z]{3},\s*', '', value)  # 移除 "Tue, "
                clean_value = re.sub(r'\s*GMT\s*$', '', clean_value)   # 移除 " GMT"
                # 转换月份名称为数字
                month_map = {
                    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
                    'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
                    'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
                }
                for month_name, month_num in month_map.items():
                    clean_value = clean_value.replace(month_name, month_num)
                
                # 重新排列为标准格式 "17 06 2025 17:17:22" -> "2025-06-17 17:17:22"
                parts = clean_value.split()
                if len(parts) >= 4:
                    day, month, year, time = parts[0], parts[1], parts[2], parts[3]
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)} {time}"
            
            # 处理ISO格式或其他标准格式
            if isinstance(value, str):
                # 特殊处理：只有时间部分的情况（如 '08:00:00'）
                if re.match(r'^\d{1,2}:\d{2}:\d{2}$', value):
                    # 将时间部分与当前日期结合
                    current_date = datetime.now().strftime('%Y-%m-%d')
                    return f"{current_date} {value}"
                
                # 尝试多种日期格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%d',
                    '%m/%d/%Y',
                    '%m/%d/%Y %H:%M:%S',
                    '%H:%M:%S'  # 添加纯时间格式支持
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(value, fmt)
                        # 如果是纯时间格式，与当前日期结合
                        if fmt == '%H:%M:%S':
                            current_date = datetime.now().strftime('%Y-%m-%d')
                            return f"{current_date} {value}"
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
                        
                # 如果所有格式都失败，返回当前时间
                logger.warning(f"无法解析日期格式: {value}，使用当前时间")
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
        except Exception as e:
            logger.warning(f"日期转换失败: {value}, 错误: {e}")
            from datetime import datetime
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        return str(value)
    
    def _preprocess_record_data(self, table_name: str, data: Dict) -> Dict:
        """记录数据预处理 - 统一处理日期字段和数据类型"""
        processed_data = data.copy()
        
        # 获取表配置
        field_config = UNIQUE_FIELD_MAPPING.get(table_name, {})
        datetime_fields = field_config.get('datetime_fields', [])
        
        # 处理日期时间字段
        for field in datetime_fields:
            if field in processed_data:
                processed_data[field] = self._normalize_datetime_field(processed_data[field])
        
        # 处理优先级字段的数据类型转换
        if 'priority' in processed_data:
            priority_value = processed_data['priority']
            if isinstance(priority_value, str):
                # 字符串优先级映射为数字
                priority_mapping = {
                    'high': 1,
                    'medium': 2,
                    'normal': 2,
                    'low': 3,
                    'urgent': 0,
                    'critical': 0
                }
                processed_data['priority'] = priority_mapping.get(priority_value.lower(), 2)  # 默认medium
            elif isinstance(priority_value, (int, float)):
                processed_data['priority'] = int(priority_value)
        
        # 特殊处理：自动添加created_at和updated_at
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if 'created_at' not in processed_data or not processed_data.get('created_at'):
            processed_data['created_at'] = current_time
            
        if 'updated_at' not in processed_data or not processed_data.get('updated_at'):
            processed_data['updated_at'] = current_time
        
        # 移除空值字段（避免数据库约束错误）
        filtered_data = {}
        for key, value in processed_data.items():
            if value is not None and value != '':
                filtered_data[key] = value
                
        return filtered_data
    
    def _apply_filters(self, data: List[Dict], filters: List[Dict]) -> List[Dict]:
        """应用筛选条件到数据集"""
        if not filters or not data:
            return data
        
        filtered_data = []
        
        for record in data:
            match_all_filters = True
            
            for filter_condition in filters:
                field = filter_condition.get('field', '')
                operator = filter_condition.get('operator', 'contains')
                value = filter_condition.get('value', '')
                
                if not field:
                    continue
                
                # 获取记录中的字段值
                record_value = record.get(field, '')
                
                # 转换为字符串进行比较（处理None值）
                if record_value is None:
                    record_value = ''
                else:
                    record_value = str(record_value).lower()
                
                filter_value = str(value).lower()
                
                # 应用不同的操作符
                field_matches = False
                try:
                    if operator == 'contains':
                        field_matches = filter_value in record_value
                    elif operator == 'equals':
                        field_matches = record_value == filter_value
                    elif operator == 'starts_with':
                        field_matches = record_value.startswith(filter_value)
                    elif operator == 'ends_with':
                        field_matches = record_value.endswith(filter_value)
                    elif operator == 'not_equals':
                        field_matches = record_value != filter_value
                    elif operator == 'is_empty':
                        field_matches = record_value == '' or record_value is None
                    elif operator == 'is_not_empty':
                        field_matches = record_value != '' and record_value is not None
                    elif operator == 'greater_than':
                        # 尝试数值比较
                        try:
                            field_matches = float(record_value) > float(filter_value)
                        except (ValueError, TypeError):
                            field_matches = record_value > filter_value
                    elif operator == 'less_than':
                        # 尝试数值比较
                        try:
                            field_matches = float(record_value) < float(filter_value)
                        except (ValueError, TypeError):
                            field_matches = record_value < filter_value
                    else:
                        # 默认使用包含操作
                        field_matches = filter_value in record_value
                        
                except Exception as e:
                    logger.warning(f"筛选条件应用失败: {field} {operator} {value}, 错误: {e}")
                    field_matches = False
                
                # 如果任何一个筛选条件不匹配，则该记录不符合要求
                if not field_matches:
                    match_all_filters = False
                    break
            
            # 只有所有筛选条件都匹配才保留该记录
            if match_all_filters:
                filtered_data.append(record)
        
        logger.info(f"筛选条件: {filters}")
        logger.info(f"筛选结果: 从 {len(data)} 条记录筛选到 {len(filtered_data)} 条记录")
        
        return filtered_data
    
    def _get_cached_data(self, cache_key, fetch_func):
        """通用缓存获取方法 - 支持版本控制和智能失效"""
        import time
        import hashlib
        current_time = time.time()
        
        # 获取该类型数据的缓存超时时间
        base_key = cache_key.replace('_excel', '')  # 移除excel后缀获取基础键
        timeout = self._cache_timeouts.get(base_key, self._cache_timeout)
        
        # 检查缓存是否存在且未过期
        cache_valid = False
        if cache_key in self._cache:
            data, timestamp = self._cache[cache_key]
            if current_time - timestamp < timeout:
                cache_valid = True
                
        # 如果缓存有效，检查数据版本（对于重要数据）
        if cache_valid and base_key in ['device_priority_config', 'lot_priority_config', 'wait_lot_data']:
            try:
                # 获取当前数据的哈希值来检测变化
                fresh_data = fetch_func()
                data_hash = hashlib.md5(str(fresh_data).encode()).hexdigest()
                
                stored_hash = self._cache_versions.get(cache_key)
                if stored_hash and stored_hash != data_hash:
                    logger.info(f"🔄 数据版本变化检测到，强制更新缓存: {cache_key}")
                    cache_valid = False
                    
                # 更新版本哈希
                self._cache_versions[cache_key] = data_hash
                
                # 如果缓存仍然有效，返回缓存数据
                if cache_valid:
                    logger.debug(f"🎯 缓存命中: {cache_key} (剩余 {timeout - (current_time - timestamp):.1f}秒)")
                    return data
                else:
                    # 使用已获取的新数据
                    logger.info(f"🔄 缓存更新: {cache_key} (数据版本变化)")
                    self._cache[cache_key] = (fresh_data, current_time)
                    return fresh_data
                    
            except Exception as e:
                logger.warning(f"版本检测失败，使用时间缓存策略: {e}")
                
        if cache_valid:
            logger.debug(f"🎯 缓存命中: {cache_key} (剩余 {timeout - (current_time - timestamp):.1f}秒)")
            return data
        
        # 缓存过期或不存在，重新获取
        logger.info(f"🔄 缓存更新: {cache_key}")
        data = fetch_func()
        self._cache[cache_key] = (data, current_time)
        
        # 更新版本哈希
        try:
            data_hash = hashlib.md5(str(data).encode()).hexdigest()
            self._cache_versions[cache_key] = data_hash
        except Exception:
            pass
            
        return data
    
    def clear_cache(self, cache_key: str = None):
        """清理缓存 - 支持清理特定缓存或全部缓存"""
        if cache_key:
            # 清理特定缓存
            if cache_key in self._cache:
                del self._cache[cache_key]
                logger.info(f"🧹 已清理缓存: {cache_key}")
            if cache_key in self._cache_versions:
                del self._cache_versions[cache_key]
        else:
            # 清理全部缓存
            cache_count = len(self._cache)
            self._cache.clear()
            self._cache_versions.clear()
            logger.info(f"🧹 缓存已清理，共清理 {cache_count} 个缓存项")
    
    def invalidate_cache_on_data_change(self, table_name: str):
        """当数据更新时主动失效相关缓存 - 修复版：正确映射数据库表名和缓存键"""
        cache_mappings = {
            # aps_system 数据库中的表（优先级配置）
            'devicepriorityconfig': ['device_priority_config', 'devicepriorityconfig'],
            'lotpriorityconfig': ['lot_priority_config', 'lotpriorityconfig'],
            
            # aps 数据库中的表（业务数据）
            'eqp_status': ['equipment_status_data', 'eqp_status'],
            'et_wait_lot': ['wait_lot_data', 'et_wait_lot'],
            'et_ft_test_spec': ['test_spec_data', 'et_ft_test_spec'],
            'et_recipe_file': ['recipe_file_data', 'et_recipe_file'],
            'et_uph_eqp': ['uph_data', 'et_uph_eqp'],
            'lotprioritydone': ['lotprioritydone_data'],
            
            # 向后兼容的映射（以防有其他代码使用旧的表名）
            'ET_WAIT_LOT': ['wait_lot_data', 'et_wait_lot'],  # 兼容大写
            'EQP_STATUS': ['equipment_status_data', 'eqp_status'],  # 兼容大写
        }
        
        cache_keys = cache_mappings.get(table_name, [])
        for cache_key in cache_keys:
            self.clear_cache(cache_key)
            # 同时清理Excel版本的缓存
            self.clear_cache(cache_key + '_excel')
        
        if cache_keys:
            logger.info(f"🔄 已失效表 {table_name} 相关的缓存: {cache_keys}")
    
    def get_cache_status(self):
        """获取缓存状态信息"""
        import time
        current_time = time.time()
        
        cache_info = []
        for cache_key, (data, timestamp) in self._cache.items():
            base_key = cache_key.replace('_excel', '')
            timeout = self._cache_timeouts.get(base_key, self._cache_timeout)
            age = current_time - timestamp
            remaining = timeout - age
            
            cache_info.append({
                'key': cache_key,
                'data_count': len(data) if isinstance(data, (list, dict)) else 1,
                'age_seconds': round(age, 1),
                'remaining_seconds': round(remaining, 1),
                'timeout_seconds': timeout,
                'is_expired': remaining <= 0
            })
        
        return {
            'total_cache_items': len(self._cache),
            'cache_details': cache_info,
            'timestamp': current_time
        }
    def __init__(self):
        self.current_source = 'mysql'  # 默认使用MySQL
        self.excel_path = r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\Excellist2025.06.05"
        self._mysql_available = None  # 延迟检查，避免应用上下文问题
        self._excel_available = None  # 延迟检查
    
    def _get_mysql_table_data(self, table_name: str, limit: int = None) -> Dict[str, Dict]:
        """通用MySQL表数据获取方法 - 处理任何ID范围和唯一字段"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取表的唯一字段配置
                field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                    'primary_key': 'id',
                    'business_key': 'id', 
                    'display_key': 'id'
                })
                
                # 查询所有数据，按ID排序确保一致性
                if limit and limit > 0:
                    cursor.execute(f"""
                        SELECT * FROM {table_name} 
                        WHERE id IS NOT NULL
                        ORDER BY id
                        LIMIT {limit}
                    """)
                else:
                    cursor.execute(f"""
                        SELECT * FROM {table_name} 
                        WHERE id IS NOT NULL
                        ORDER BY id
                    """)
                
                table_data = {}
                for row in cursor.fetchall():
                    # 使用ID作为主键确保唯一性（无论ID从何开始）
                    primary_key = str(row.get(field_config['primary_key'], ''))
                    
                    # 保留所有原始数据
                    table_data[primary_key] = dict(row)
                    
                    # 根据表类型添加标准化字段
                    if table_name == 'eqp_status':
                        table_data[primary_key].update({
                            'EQP_ID': str(row.get('DEVICE', '') or row.get('HANDLER_ID', '') or primary_key),
                            'EQP_NAME': str(row.get('HANDLER_TYPE', '') or row.get('DEVICE', '') or '未知设备'),
                            'EQP_STATUS': str(row.get('STATUS', 'UNKNOWN')),
                            'TESTER_ID': str(row.get('TESTER_ID', '')),
                            'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                            'FAC_ID': str(row.get('FAC_ID', '')),
                            'UPDATE_TIME': row.get('updated_at', row.get('UPDATE_TIME')),
                            'available': str(row.get('STATUS', '')).upper() in ['RUN', 'IDLE', 'SETUP', 'READY', 'ACTIVE', 'ONLINE']
                        })
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(table_data)} 条{table_name}数据")
            return table_data
            
        except Exception as e:
            logger.error(f"MySQL获取{table_name}数据失败: {e}")
            return {}
        
    @property
    def mysql_available(self):
        """延迟检查MySQL可用性"""
        if self._mysql_available is None:
            self._check_mysql_availability()
        return self._mysql_available
    
    @property
    def excel_available(self):
        """延迟检查Excel可用性"""
        if self._excel_available is None:
            self._check_excel_availability()
        return self._excel_available
    
    def _check_mysql_availability(self):
        """检查MySQL可用性"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',  # 使用aps数据库
                charset='utf8mb4'
            )
            connection.close()
            self._mysql_available = True
            logger.info("✅ MySQL数据源可用")
        except Exception as e:
            self._mysql_available = False
            logger.warning(f"❌ MySQL数据源不可用: {e}")
    
    def _check_excel_availability(self):
        """检查Excel文件可用性"""
        try:
            if os.path.exists(self.excel_path):
                required_files = [
                    'ET_WAIT_LOT.xlsx',
                    'ET_FT_TEST_SPEC.xlsx', 
                    'ET_RECIPE_FILE.xlsx',
                    'EQP_STATUS.xlsx',
                    'ET_UPH_EQP.xlsx',
                    'devicepriorityconfig.xlsx',
                    'lotpriorityconfig.xlsx'
                ]
                
                missing_files = []
                for file in required_files:
                    if not os.path.exists(os.path.join(self.excel_path, file)):
                        missing_files.append(file)
                
                if missing_files:
                    self._excel_available = False
                    logger.warning(f"❌ Excel数据源不完整，缺少文件: {missing_files}")
                else:
                    self._excel_available = True
                    logger.info("✅ Excel数据源可用")
            else:
                self._excel_available = False
                logger.warning(f"❌ Excel数据源路径不存在: {self.excel_path}")
        except Exception as e:
            self._excel_available = False
            logger.warning(f"❌ Excel数据源检查失败: {e}")
    
    def _check_data_sources(self):
        """检查数据源可用性 - 保留原方法名以兼容"""
        self._check_mysql_availability()
        self._check_excel_availability()
    
    def get_wait_lot_data(self) -> Tuple[List[Dict], str]:
        """获取待排产批次数据 - 智能数据源切换（使用缓存）"""
        cache_key = "wait_lot_data"
        
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_cached_data(cache_key, self._get_wait_lot_from_mysql)
                if data:
                    logger.info(f"📊 从MySQL获取到 {len(data)} 条待排产批次数据（缓存）")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取待排产数据失败: {e}")
                self._mysql_available = False
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_cached_data(cache_key + "_excel", self._get_wait_lot_from_excel)
                if data:
                    logger.info(f"📊 从Excel获取到 {len(data)} 条待排产批次数据（缓存）")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取待排产数据失败: {e}")
        
        logger.error("❌ 所有数据源都不可用")
        return [], "None"
    
    def get_test_spec_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取测试规范数据 - 智能数据源切换（使用缓存）"""
        cache_key = "test_spec_data"
        
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_cached_data(cache_key, self._get_test_spec_from_mysql)
                if data:
                    logger.info(f"🔬 从MySQL获取到 {len(data)} 条测试规范数据（缓存）")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取测试规范数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_cached_data(cache_key + "_excel", self._get_test_spec_from_excel)
                if data:
                    logger.info(f"🔬 从Excel获取到 {len(data)} 条测试规范数据（缓存）")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取测试规范数据失败: {e}")
        
        return {}, "None"
    
    def get_recipe_file_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取工艺配方数据 - 智能数据源切换（使用缓存）"""
        cache_key = "recipe_file_data"
        
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_cached_data(cache_key, self._get_recipe_file_from_mysql)
                if data:
                    logger.info(f"📄 从MySQL获取到 {len(data)} 条工艺配方数据（缓存）")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取工艺配方数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_cached_data(cache_key + "_excel", self._get_recipe_file_from_excel)
                if data:
                    logger.info(f"📄 从Excel获取到 {len(data)} 条工艺配方数据（缓存）")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取工艺配方数据失败: {e}")
        
        return {}, "None"
    
    def get_equipment_status_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取设备状态数据 - 智能数据源切换（使用缓存）"""
        cache_key = "equipment_status_data"
        
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_cached_data(cache_key, self._get_equipment_status_from_mysql)
                if data:
                    logger.info(f"🏭 从MySQL获取到 {len(data)} 条设备状态数据（缓存）")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取设备状态数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_cached_data(cache_key + "_excel", self._get_equipment_status_from_excel)
                if data:
                    logger.info(f"🏭 从Excel获取到 {len(data)} 条设备状态数据（缓存）")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取设备状态数据失败: {e}")
        
        return {}, "None"
    
    def get_uph_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取UPH数据 - 智能数据源切换（使用缓存）"""
        cache_key = "uph_data"
        
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_cached_data(cache_key, self._get_uph_from_mysql)
                if data:
                    logger.info(f"⚡ 从MySQL获取到 {len(data)} 条UPH数据（缓存）")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取UPH数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_cached_data(cache_key + "_excel", self._get_uph_from_excel)
                if data:
                    logger.info(f"⚡ 从Excel获取到 {len(data)} 条UPH数据（缓存）")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取UPH数据失败: {e}")
        
        return {}, "None"
    
    def get_priority_configs(self) -> Tuple[Dict[str, Dict], str]:
        """获取优先级配置数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_priority_configs_from_mysql()
                device_count = len(data.get('device', {}))
                lot_count = len(data.get('lot', {}))
                if device_count > 0 or lot_count > 0:
                    logger.info(f"🎯 从MySQL获取到优先级配置 - 设备: {device_count}, 批次: {lot_count}")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取优先级配置失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_priority_configs_from_excel()
                device_count = len(data.get('device', {}))
                lot_count = len(data.get('lot', {}))
                if device_count > 0 or lot_count > 0:
                    logger.info(f"🎯 从Excel获取到优先级配置 - 设备: {device_count}, 批次: {lot_count}")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取优先级配置失败: {e}")
        
        return {'device': {}, 'lot': {}}, "None"
    
    # MySQL数据获取方法
    def _get_wait_lot_from_mysql(self) -> List[Dict]:
        """从MySQL获取待排产批次数据"""
        try:
            # 临时恢复硬编码连接以解决密码问题
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor  # 使用DictCursor简化后续处理
            )
            
            with connection.cursor() as cursor:

                cursor.execute("""
                    SELECT LOT_ID, LOT_TYPE, PROD_ID, PO_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID, CREATE_TIME,
                           FAC_ID, FLOW_ID, FLOW_VER, WIP_STATE, PROC_STATE, HOLD_STATE
                    FROM et_wait_lot
                    WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''
                    ORDER BY CREATE_TIME DESC
                """)
                
                wait_lots = cursor.fetchall()
                # 数据清洗
                for lot in wait_lots:
                    try:
                        qty_val = lot.get('GOOD_QTY')
                        if qty_val is not None:
                            lot['GOOD_QTY'] = int(float(qty_val))
                        else:
                            lot['GOOD_QTY'] = 0
                    except (ValueError, TypeError):
                        lot['GOOD_QTY'] = 0
                    lot['CREATE_TIME'] = self._normalize_datetime_field(lot.get('CREATE_TIME'))
            
            connection.close()
            return wait_lots
            
        except Exception as e:
            logger.error(f"MySQL获取待排产批次数据失败: {e}")
            if 'connection' in locals() and connection and connection.open:
                connection.close()
            return []
    
    def _get_test_spec_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取测试规范数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段，不指定特定字段名
                cursor.execute("SELECT * FROM et_ft_test_spec WHERE id IS NOT NULL ORDER BY id")
                
                test_specs = {}
                for i, row in enumerate(cursor.fetchall()):
                    # 使用组合键或序号作为主键
                    device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                    stage = str(row.get('STAGE', ''))
                    
                    key = f"{device}|{stage}" if stage else device
                    
                    # 直接使用数据库返回的所有字段
                    test_specs[key] = dict(row)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(test_specs)} 条测试规范数据")
            return test_specs
            
        except Exception as e:
            logger.error(f"MySQL获取测试规范数据失败: {e}")
            return {}
    
    def _get_recipe_file_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取工艺配方数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DEVICE, STAGE, PKG_PN, CHIP_ID, RECIPE_FILE, RECIPE_VERSION, TESTER_TYPE, HANDLER_TYPE
                    FROM et_recipe_file
                    WHERE DEVICE IS NOT NULL AND DEVICE != ''
                """)
                
                recipe_files = {}
                for row in cursor.fetchall():
                    device, stage, pkg_pn, chip_id = row[0] or '', row[1] or '', row[2] or '', row[3] or ''
                    
                    keys = [
                        f"{device}|{stage}|{pkg_pn}|{chip_id}",
                        f"{device}|{stage}|{pkg_pn}",
                        f"{device}|{stage}",
                        f"{device}"
                    ]
                    
                    recipe_data = {
                        'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                        'RECIPE_FILE': row[4] or '', 'RECIPE_VERSION': row[5] or '',
                        'TESTER_TYPE': row[6] or '', 'HANDLER_TYPE': row[7] or ''
                    }
                    
                    for key in keys:
                        if key not in recipe_files:
                            recipe_files[key] = recipe_data
            
            connection.close()
            return recipe_files
            
        except Exception as e:
            logger.error(f"MySQL获取工艺配方数据失败: {e}")
            return {}
    
    def _get_recipe_file_from_mysql_fixed(self) -> List[Dict]:
        """从MySQL获取配方文件数据（修复版本）"""
        try:
            if not self.mysql_available:
                return []
            
            import pymysql
            
            conn = pymysql.connect(
                host='127.0.0.1',
                user='root', 
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 修复字段名：使用正确的字段名
            query = """
                SELECT 
                    PROD_ID, COMPANY_ID, STAGE, DEVICE, CHIP_ID, PKG_PN,
                    RECIPE_FILE_NAME, RECIPE_FILE_PATH, APPROVAL_STATE,
                    KIT_PN, SOCKET_PN, HANDLER_CONFIG,
                    FAC_ID, CREATE_TIME, CREATE_USER
                FROM et_recipe_file 
                WHERE APPROVAL_STATE = 'Released'
                ORDER BY DEVICE, STAGE, PKG_PN
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            logger.info(f"📁 从MySQL获取到 {len(results)} 条配方文件数据")
            return results
            
        except Exception as e:
            logger.error(f"MySQL获取配方文件数据失败: {e}")
            return []
    
    def _get_equipment_status_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取设备状态数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 查询所有数据，按ID排序确保一致性
                cursor.execute("""
                    SELECT * FROM eqp_status 
                    WHERE id IS NOT NULL
                    ORDER BY id
                """)
                
                equipment_status = {}
                for row in cursor.fetchall():
                    # 使用ID作为主键确保唯一性（无论ID从何开始）
                    unique_key = str(row['id'])
                    
                    # 保留所有原始数据
                    equipment_status[unique_key] = dict(row)
                    
                    # 添加标准化字段供前端使用
                    equipment_status[unique_key].update({
                        'EQP_ID': str(row.get('DEVICE', '') or row.get('HANDLER_ID', '') or unique_key),
                        'EQP_NAME': str(row.get('HANDLER_TYPE', '') or row.get('DEVICE', '') or '未知设备'),
                        'EQP_STATUS': str(row.get('STATUS', 'UNKNOWN')),
                        'TESTER_ID': str(row.get('TESTER_ID', '')),
                        'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                        'FAC_ID': str(row.get('FAC_ID', '')),
                        'UPDATE_TIME': row.get('updated_at', row.get('UPDATE_TIME')),
                        'available': str(row.get('STATUS', '')).upper() in ['RUN', 'IDLE', 'SETUP', 'READY', 'ACTIVE', 'ONLINE']
                    })
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(equipment_status)} 条设备状态数据")
            return equipment_status
            
        except Exception as e:
            logger.error(f"MySQL获取设备状态数据失败: {e}")
            return {}
    
    def _get_uph_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取UPH数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段
                cursor.execute("SELECT * FROM et_uph_eqp WHERE id IS NOT NULL ORDER BY id")
                
                uph_data = {}
                for i, row in enumerate(cursor.fetchall()):
                    # 使用组合键
                    device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                    stage = str(row.get('STAGE', ''))
                    pkg_pn = str(row.get('PKG_PN', ''))
                    
                    keys = [
                        f"{device}|{stage}|{pkg_pn}" if pkg_pn else f"{device}|{stage}",
                        f"{device}|{stage}",
                        device
                    ]
                    
                    # 直接使用数据库返回的所有字段
                    uph_info = dict(row)
                    for key in keys:
                        if key not in uph_data:
                            uph_data[key] = uph_info
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(uph_data)} 条UPH数据")
            return uph_data
            
        except Exception as e:
            logger.error(f"MySQL获取UPH数据失败: {e}")
            return {}
    
    def _get_priority_configs_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取优先级配置数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps_system',  # 优先级配置在aps_system数据库
                charset='utf8mb4'
            )
            
            device_priority = {}
            lot_priority = {}
            
            with connection.cursor() as cursor:
                # 获取设备优先级配置
                try:
                    cursor.execute("SELECT * FROM device_priority_config")
                    for row in cursor.fetchall():
                        if len(row) >= 2:  # 确保有足够的字段
                            device, stage = row[0] or '', row[1] or ''
                            keys = [f"{device}|{stage}", f"{device}"]
                            for key in keys:
                                if key not in device_priority:
                                    device_priority[key] = {
                                        'DEVICE': device,
                                        'STAGE': stage,
                                        'PRIORITY': row[2] if len(row) > 2 else 1
                                    }
                except Exception as e:
                    logger.warning(f"获取设备优先级配置失败: {e}")
                
                # 获取批次优先级配置
                try:
                    cursor.execute("SELECT * FROM lot_priority_config")
                    for row in cursor.fetchall():
                        if len(row) >= 1:  # 确保有足够的字段
                            lot_id = row[0] or ''
                            if lot_id:
                                lot_priority[lot_id] = {
                                    'LOT_ID': lot_id,
                                    'PRIORITY': row[1] if len(row) > 1 else 1
                                }
                except Exception as e:
                    logger.warning(f"获取批次优先级配置失败: {e}")
            
            connection.close()
            return {'device': device_priority, 'lot': lot_priority}
            
        except Exception as e:
            logger.error(f"MySQL获取优先级配置失败: {e}")
            return {'device': {}, 'lot': {}}
    
    # Excel数据获取方法
    def _get_wait_lot_from_excel(self) -> List[Dict]:
        """从Excel获取待排产批次数据"""
        file_path = os.path.join(self.excel_path, 'ET_WAIT_LOT.xlsx')
        df = pd.read_excel(file_path)
        
        wait_lots = []
        for _, row in df.iterrows():
            if pd.notna(row.get('GOOD_QTY', 0)) and row.get('GOOD_QTY', 0) > 0:
                wait_lots.append({
                    'LOT_ID': str(row.get('LOT_ID', '')),
                    'DEVICE': str(row.get('DEVICE', '')),
                    'STAGE': str(row.get('STAGE', '')),
                    'GOOD_QTY': int(row.get('GOOD_QTY', 0)),
                    'PKG_PN': str(row.get('PKG_PN', '')),
                    'CHIP_ID': str(row.get('CHIP_ID', '')),
                    'CREATE_TIME': row.get('CREATE_TIME'),
                    'FAC_ID': str(row.get('FAC_ID', '')),
                    'FLOW_ID': str(row.get('FLOW_ID', '')),
                    'FLOW_VER': str(row.get('FLOW_VER', '')),
                    'WIP_STATE': str(row.get('WIP_STATE', '')),
                    'PROC_STATE': str(row.get('PROC_STATE', '')),
                    'HOLD_STATE': str(row.get('HOLD_STATE', ''))
                })
        
        return wait_lots
    
    def _get_test_spec_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取测试规范数据"""
        file_path = os.path.join(self.excel_path, 'ET_FT_TEST_SPEC.xlsx')
        df = pd.read_excel(file_path)
        
        test_specs = {}
        for _, row in df.iterrows():
            device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
            pkg_pn, chip_id = str(row.get('PKG_PN', '')), str(row.get('CHIP_ID', ''))
            
            if device:
                keys = [
                    f"{device}|{stage}|{pkg_pn}|{chip_id}",
                    f"{device}|{stage}|{pkg_pn}",
                    f"{device}|{stage}",
                    f"{device}"
                ]
                
                spec_data = {
                    'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                    'TEST_SPEC': str(row.get('TEST_SPEC', '')),
                    'TEST_TIME': int(row.get('TEST_TIME', 60)),
                    'SPEC_VERSION': str(row.get('SPEC_VERSION', ''))
                }
                
                for key in keys:
                    if key not in test_specs:
                        test_specs[key] = spec_data
        
        return test_specs
    
    def _get_recipe_file_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取工艺配方数据"""
        file_path = os.path.join(self.excel_path, 'ET_RECIPE_FILE.xlsx')
        df = pd.read_excel(file_path)
        
        recipe_files = {}
        for _, row in df.iterrows():
            device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
            pkg_pn, chip_id = str(row.get('PKG_PN', '')), str(row.get('CHIP_ID', ''))
            
            if device:
                keys = [
                    f"{device}|{stage}|{pkg_pn}|{chip_id}",
                    f"{device}|{stage}|{pkg_pn}",
                    f"{device}|{stage}",
                    f"{device}"
                ]
                
                recipe_data = {
                    'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                    'RECIPE_FILE': str(row.get('RECIPE_FILE', '')),
                    'RECIPE_VERSION': str(row.get('RECIPE_VERSION', '')),
                    'TESTER_TYPE': str(row.get('TESTER_TYPE', '')),
                    'HANDLER_TYPE': str(row.get('HANDLER_TYPE', ''))
                }
                
                for key in keys:
                    if key not in recipe_files:
                        recipe_files[key] = recipe_data
        
        return recipe_files
    
    def _get_equipment_status_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取设备状态数据"""
        file_path = os.path.join(self.excel_path, 'EQP_STATUS.xlsx')
        df = pd.read_excel(file_path)
        
        equipment_status = {}
        for _, row in df.iterrows():
            eqp_id = str(row.get('EQP_ID', ''))
            eqp_status = str(row.get('EQP_STATUS', ''))
            
            if eqp_id:
                equipment_status[eqp_id] = {
                    'EQP_ID': eqp_id,
                    'EQP_NAME': str(row.get('EQP_NAME', '')),
                    'EQP_STATUS': eqp_status,
                    'TESTER_ID': str(row.get('TESTER_ID', '')),
                    'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                    'FAC_ID': str(row.get('FAC_ID', '')),
                    'UPDATE_TIME': row.get('UPDATE_TIME'),
                    'available': eqp_status.upper() in ['RUN', 'IDLE', 'SETUP', 'READY']
                }
        
        return equipment_status
    
    def _get_uph_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取UPH数据"""
        file_path = os.path.join(self.excel_path, 'ET_UPH_EQP.xlsx')
        df = pd.read_excel(file_path)
        
        uph_data = {}
        for _, row in df.iterrows():
            device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
            pkg_pn = str(row.get('PKG_PN', ''))
            uph = row.get('UPH', 0)
            
            if device and uph > 0:
                keys = [
                    f"{device}|{stage}|{pkg_pn}",
                    f"{device}|{stage}",
                    f"{device}"
                ]
                
                uph_info = {
                    'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'UPH': int(uph),
                    'SORTER_MODEL': str(row.get('SORTER_MODEL', '')),
                    'FAC_ID': str(row.get('FAC_ID', '')),
                    'TESTER_TYPE': str(row.get('TESTER_TYPE', '')),
                    'HANDLER_TYPE': str(row.get('HANDLER_TYPE', ''))
                }
                
                for key in keys:
                    if key not in uph_data:
                        uph_data[key] = uph_info
        
        return uph_data
    
    def _get_priority_configs_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取优先级配置数据"""
        device_priority = {}
        lot_priority = {}
        
        # 获取设备优先级配置
        try:
            device_file = os.path.join(self.excel_path, 'devicepriorityconfig.xlsx')
            if os.path.exists(device_file):
                df = pd.read_excel(device_file)
                for _, row in df.iterrows():
                    device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
                    if device:
                        keys = [f"{device}|{stage}", f"{device}"]
                        row_dict = row.to_dict()
                        for key in keys:
                            if key not in device_priority:
                                device_priority[key] = row_dict
        except Exception as e:
            logger.warning(f"Excel设备优先级配置读取失败: {e}")
        
        # 获取批次优先级配置
        try:
            lot_file = os.path.join(self.excel_path, 'lotpriorityconfig.xlsx')
            if os.path.exists(lot_file):
                df = pd.read_excel(lot_file)
                for _, row in df.iterrows():
                    lot_id = str(row.get('LOT_ID', ''))
                    if lot_id:
                        lot_priority[lot_id] = row.to_dict()
        except Exception as e:
            logger.warning(f"Excel批次优先级配置读取失败: {e}")
        
        return {'device': device_priority, 'lot': lot_priority}
    
    def _get_tcc_inv_data(self) -> List[Dict]:
        """从MySQL获取套件资源数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段
                cursor.execute("SELECT * FROM TCC_INV WHERE id IS NOT NULL ORDER BY id")
                
                tcc_data = []
                for row in cursor.fetchall():
                    tcc_data.append(dict(row))
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(tcc_data)} 条TCC_INV数据")
            return tcc_data
            
        except Exception as e:
            logger.error(f"MySQL获取TCC_INV数据失败: {e}")
            return []
    
    def _get_ct_data(self) -> List[Dict]:
        """从MySQL获取产品周期数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段
                cursor.execute("SELECT * FROM ct WHERE id IS NOT NULL ORDER BY id")
                
                ct_data = []
                for row in cursor.fetchall():
                    ct_data.append(dict(row))
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(ct_data)} 条CT数据")
            return ct_data
            
        except Exception as e:
            logger.error(f"MySQL获取CT数据失败: {e}")
            return []
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态信息"""
        return {
            'mysql_available': self.mysql_available,
            'excel_available': self.excel_available,
            'excel_path': self.excel_path,
            'recommended_source': 'MySQL' if self.mysql_available else 'Excel' if self.excel_available else 'None'
        }
    
    def get_table_data(self, table_name: str, page: int = 1, per_page: int = None, filters: List = None) -> Dict:
        """获取表格数据（API v2兼容方法）"""
        try:
            # 根据table_name路由到对应的方法
            if table_name in ['wait_lot', 'ET_WAIT_LOT', 'et_wait_lot', 'wip_lot']:
                data, source = self.get_wait_lot_data()
                columns = ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'] if data else []
            elif table_name in ['eqp_status', 'EQP_STATUS']:
                data_dict, source = self.get_equipment_status_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际存在的字段作为列名
                if data:
                    # 从第一条数据获取可用的列
                    first_record = data[0]
                    # 排除调试字段，只显示业务字段
                    columns = [k for k in first_record.keys() if k not in ['available', 'raw_data']]
                else:
                    columns = ['EQP_ID', 'EQP_NAME', 'EQP_STATUS', 'TESTER_ID', 'HANDLER_ID', 'FAC_ID', 'UPDATE_TIME']
            elif table_name in ['ET_UPH_EQP', 'et_uph_eqp']:
                data_dict, source = self.get_uph_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'UPH', 'SORTER_MODEL', 'FAC_ID']
            elif table_name in ['et_ft_test_spec', 'ET_FT_TEST_SPEC']:
                data_dict, source = self.get_test_spec_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'CHIP_ID', 'TEST_SPEC', 'TEST_TIME']
            elif table_name in ['et_recipe_file', 'ET_RECIPE_FILE']:
                # 配方文件表 - 直接从MySQL获取
                data = self._get_recipe_file_from_mysql_fixed()
                source = "MySQL" if self.mysql_available else "Excel"
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'KIT_PN', 'SOCKET_PN', 'RECIPE_FILE_NAME']
            elif table_name in ['TCC_INV', 'tcc_inv']:
                # 套件资源表 - 从MySQL获取实际数据
                data = self._get_tcc_inv_data()
                source = "MySQL" if self.mysql_available else "Excel" 
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['INVENTORY_ID', 'INVENTORY_TYPE', 'INVENTORY_STATUS', 'LOCATION', 'REMARK']
            elif table_name in ['CT', 'ct']:
                # 产品周期表 - 从MySQL获取实际数据
                data = self._get_ct_data()
                source = "MySQL" if self.mysql_available else "Excel"
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['PRODUCT', 'STAGE', 'CT_VALUE', 'UNIT', 'REMARK']
            elif table_name in ['devicepriorityconfig']:
                # 产品优先级配置表
                data = self._get_device_priority_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER']
            elif table_name in ['lotpriorityconfig']:
                # 批次优先级配置表
                data = self._get_lot_priority_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER']
            elif table_name in ['lotprioritydone']:
                # 已排产批次由专用API处理，避免冲突
                logger.info(f"⚠️  {table_name} 数据由专用API处理，跳过DataSourceManager")
                return {
                    'success': True,
                    'data': [],
                    'columns': [],
                    'total': 0,
                    'pages': 0,
                    'data_source': 'Dedicated API',
                    'timestamp': datetime.now().isoformat(),
                    'note': '此表由专用API处理，请使用 /api/v2/production/done-lots 端点'
                }
            else:
                return {
                    'success': False,
                    'error': f'不支持的表格: {table_name}'
                }
            
            # 应用筛选条件
            if filters and len(filters) > 0:
                data = self._apply_filters(data, filters)
                logger.info(f"应用筛选条件后，数据从原始条数筛选到 {len(data)} 条")
            
            # 记录总数（筛选后）
            total = len(data)
            
            # 应用分页（如果指定了per_page）
            if per_page is not None:
                start_idx = (page - 1) * per_page
                end_idx = start_idx + per_page
                paged_data = data[start_idx:end_idx]
            else:
                paged_data = data
            
            logger.info(f"表格 {table_name}: 总计 {total} 条记录, 显示 {len(paged_data)} 条")
            
            return {
                'success': True,
                'data': paged_data,
                'columns': columns,
                'total': total,
                'pages': (total + per_page - 1) // per_page if per_page else 1,
                'data_source': source,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取表格数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_table_columns(self, table_name: str) -> Dict:
        """获取表格列信息（API v2兼容方法）- 已修复字段映射与MySQL数据库完全一致"""
        try:
            # 🔧 修复后的列映射 - 与MySQL数据库字段完全对应
            column_mapping = {
                # 🚨 修复1：待排产批次表 - 扩展至完整的17个字段（含id字段）
                'wait_lot': ['id', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME'],
                'ET_WAIT_LOT': ['id', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME'],
                'et_wait_lot': ['id', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME'],
                
                # 🚨 修复2：在制品批次表 - 完整的147个字段（含id字段）
                'wip_lot': ['id', 'LOT_ID', 'LOT_TYPE', 'DET_LOT_TYPE', 'LOT_QTY', 'SUB_QTY', 'UNIT', 'SUB_UNIT', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'RW_STATE', 'REPAIR_STATE', 'QC_STATE', 'PROD_ID', 'PROC_RULE_ID', 'PRP_ID', 'FLOW_ID', 'STAGE', 'PRP_VER', 'FLOW_VER', 'OPER_VER', 'CARR_ID', 'USE_SUB_LOT', 'AREA_ID', 'LOC_ID', 'EQP_ID', 'SUB_EQP_ID', 'PORT_ID', 'RECIPE_ID', 'SUB_RECIPE_ID', 'MARK_ID', 'LOT_IN_QTY', 'LOT_OUT_QTY', 'GOOD_QTY', 'NG_QTY', 'PREV_PROD_ID', 'PREV_PROC_RULE_ID', 'PREV_PRP_ID', 'PREV_PRP_VER', 'PREV_FLOW_ID', 'PREV_FLOW_VER', 'PREV_OPER_ID', 'PREV_OPER_VER', 'PREV_EQP_ID', 'PREV_PORT_ID', 'PREV_RECIPE_ID', 'PREV_SUB_RECIPE_ID', 'RTCL_ID', 'BATCH_ID', 'LAST_BATCH_ID', 'CTM_ID', 'LOT_GRP_ID', 'RESV_EQP_ID', 'HOT_TYPE', 'SEND_COMPANY_ID', 'OPER_CHANGE_TIME', 'JOB_START_TIME', 'JOB_END_TIME', 'PLAN_START_DATE', 'PLAN_DUE_DATE', 'GRADE', 'REASON_GRP', 'REASON_CODE', 'FR_RW_PROC_RULE_ID', 'FR_RW_PRP_ID', 'FR_RW_PRP_VER', 'FR_RW_FLOW_ID', 'FR_RW_FLOW_VER', 'FR_RW_OPER_ID', 'FR_RW_OPER_VER', 'RW_RT_PROC_RULE_ID', 'RW_RT_PRP_ID', 'RW_RT_PRP_VER', 'RW_RT_FLOW_ID', 'RW_RT_FLOW_VER', 'RW_RT_OPER_ID', 'RW_RT_OPER_VER', 'PILOT_TYPE', 'MERGE_OPER_ID', 'ACT_NM', 'LOT_JUDGE', 'FAC_ID', 'SUB_FAC', 'ROOT_LOT_ID', 'PARENT_LOT_ID', 'CHILD_LOT_ID', 'LOT_OBJ_ID', 'CUST_LOT_ID', 'WORK_ORDER_ID', 'WORK_ORDER_VER', 'BOM_ID', 'BOM_VER', 'PO_ID', 'LOT_OWNER', 'PKG_PN', 'RELEASE_TIME', 'SHIP_TIME', 'SHIP_ORDER_ID', 'SHIP_FAC_ID', 'CREATE_LOT_QTY', 'CREATE_SUB_QTY', 'ROOT_LOT_QTY', 'TRACK_CARD_ID', 'DBP_ID', 'CJOB_ID', 'PROC_CNT', 'RETEST_YN', 'DUT_ID', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'STR_FLAG', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'CONTAINER_ID', 'CHIP_ID', 'ACT_QTY', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'ORT_QTY', 'OA_FLAG', 'DEVICE', 'WAREHOUSE_CONTAINER_ID', 'PROD_THICKNESS', 'IQC_QTY', 'UPH', 'SEAL_FLAG', 'PACK_SPEC_ID', 'PACK_SPEC_VER', 'FULL_INSP_QC', 'SPLIT_TYPE', 'WH_LOCATION_NO', 'RELEASE_HOLD_TYPE', 'DATA_CONFIRM_HOLD_YN', 'ORT_SAMP_QTY', 'IQC_SAMP_QTY', 'LOCATION', 'RETEST_FLOW_ID', 'HALF_LOT_HOLD', 'MERGE_LOT_ID', 'STRM_QTY', 'STRM_SAMP_QTY'],
                'WIP_LOT': ['id', 'LOT_ID', 'LOT_TYPE', 'DET_LOT_TYPE', 'LOT_QTY', 'SUB_QTY', 'UNIT', 'SUB_UNIT', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'RW_STATE', 'REPAIR_STATE', 'QC_STATE', 'PROD_ID', 'PROC_RULE_ID', 'PRP_ID', 'FLOW_ID', 'STAGE', 'PRP_VER', 'FLOW_VER', 'OPER_VER', 'CARR_ID', 'USE_SUB_LOT', 'AREA_ID', 'LOC_ID', 'EQP_ID', 'SUB_EQP_ID', 'PORT_ID', 'RECIPE_ID', 'SUB_RECIPE_ID', 'MARK_ID', 'LOT_IN_QTY', 'LOT_OUT_QTY', 'GOOD_QTY', 'NG_QTY', 'PREV_PROD_ID', 'PREV_PROC_RULE_ID', 'PREV_PRP_ID', 'PREV_PRP_VER', 'PREV_FLOW_ID', 'PREV_FLOW_VER', 'PREV_OPER_ID', 'PREV_OPER_VER', 'PREV_EQP_ID', 'PREV_PORT_ID', 'PREV_RECIPE_ID', 'PREV_SUB_RECIPE_ID', 'RTCL_ID', 'BATCH_ID', 'LAST_BATCH_ID', 'CTM_ID', 'LOT_GRP_ID', 'RESV_EQP_ID', 'HOT_TYPE', 'SEND_COMPANY_ID', 'OPER_CHANGE_TIME', 'JOB_START_TIME', 'JOB_END_TIME', 'PLAN_START_DATE', 'PLAN_DUE_DATE', 'GRADE', 'REASON_GRP', 'REASON_CODE', 'FR_RW_PROC_RULE_ID', 'FR_RW_PRP_ID', 'FR_RW_PRP_VER', 'FR_RW_FLOW_ID', 'FR_RW_FLOW_VER', 'FR_RW_OPER_ID', 'FR_RW_OPER_VER', 'RW_RT_PROC_RULE_ID', 'RW_RT_PRP_ID', 'RW_RT_PRP_VER', 'RW_RT_FLOW_ID', 'RW_RT_FLOW_VER', 'RW_RT_OPER_ID', 'RW_RT_OPER_VER', 'PILOT_TYPE', 'MERGE_OPER_ID', 'ACT_NM', 'LOT_JUDGE', 'FAC_ID', 'SUB_FAC', 'ROOT_LOT_ID', 'PARENT_LOT_ID', 'CHILD_LOT_ID', 'LOT_OBJ_ID', 'CUST_LOT_ID', 'WORK_ORDER_ID', 'WORK_ORDER_VER', 'BOM_ID', 'BOM_VER', 'PO_ID', 'LOT_OWNER', 'PKG_PN', 'RELEASE_TIME', 'SHIP_TIME', 'SHIP_ORDER_ID', 'SHIP_FAC_ID', 'CREATE_LOT_QTY', 'CREATE_SUB_QTY', 'ROOT_LOT_QTY', 'TRACK_CARD_ID', 'DBP_ID', 'CJOB_ID', 'PROC_CNT', 'RETEST_YN', 'DUT_ID', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'STR_FLAG', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'CONTAINER_ID', 'CHIP_ID', 'ACT_QTY', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'ORT_QTY', 'OA_FLAG', 'DEVICE', 'WAREHOUSE_CONTAINER_ID', 'PROD_THICKNESS', 'IQC_QTY', 'UPH', 'SEAL_FLAG', 'PACK_SPEC_ID', 'PACK_SPEC_VER', 'FULL_INSP_QC', 'SPLIT_TYPE', 'WH_LOCATION_NO', 'RELEASE_HOLD_TYPE', 'DATA_CONFIRM_HOLD_YN', 'ORT_SAMP_QTY', 'IQC_SAMP_QTY', 'LOCATION', 'RETEST_FLOW_ID', 'HALF_LOT_HOLD', 'MERGE_LOT_ID', 'STRM_QTY', 'STRM_SAMP_QTY'],
                
                # 🚨 修复3：设备状态表 - 修正为实际的18个字段（含id字段）
                'eqp_status': ['id', 'HANDLER_ID', 'HANDLER_TYPE', 'TESTER_ID', 'HANDLER_CONFIG', 'SOCKET_PN', 'KIT_PN', 'EQP_CLASS', 'EQP_TYPE', 'TEMPERATURE_RANGE', 'TEMPERATURE_CAPACITY', 'LOT_ID', 'DEVICE', 'STATUS', 'HB_PN', 'TB_PN', 'TESTER_CONFIG', 'STAGE', 'EVENT_TIME', 'created_at', 'updated_at'],
                'EQP_STATUS': ['id', 'HANDLER_ID', 'HANDLER_TYPE', 'TESTER_ID', 'HANDLER_CONFIG', 'SOCKET_PN', 'KIT_PN', 'EQP_CLASS', 'EQP_TYPE', 'TEMPERATURE_RANGE', 'TEMPERATURE_CAPACITY', 'LOT_ID', 'DEVICE', 'STATUS', 'HB_PN', 'TB_PN', 'TESTER_CONFIG', 'STAGE', 'EVENT_TIME', 'created_at', 'updated_at'],
                
                # 🚨 修复4：UPH设备表 - 修正为实际的16个字段（含id字段）
                'ET_UPH_EQP': ['id', 'DEVICE', 'PKG_PN', 'STAGE', 'UPH', 'HANDLER', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'created_at', 'updated_at'],
                'et_uph_eqp': ['id', 'DEVICE', 'PKG_PN', 'STAGE', 'UPH', 'HANDLER', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'created_at', 'updated_at'],
                
                # 🚨 修复5：测试规格表 - 扩展至完整的72个字段（含id字段）
                'et_ft_test_spec': ['id', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE', 'TESTER', 'INV_ID', 'TEST_SPEC_TYPE', 'APPROVAL_STATE', 'ACTV_YN', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'COMPANY_ID', 'DISABLE_USER', 'DISABLE_REASON', 'DISABLE_TIME', 'NOTE', 'APPROVE_USER', 'APPROVE_TIME', 'ORT_QTY', 'REMAIN_QTY', 'STANDARD_YIELD', 'LOW_YIELD', 'DOWN_YIELD', 'TEST_AREA', 'HANDLER', 'TEMPERATURE', 'FT_PROGRAM', 'QA_PROGRAM', 'GU_PROGRAM', 'TB_PN', 'HB_PN', 'TIB', 'TEST_TIME', 'UPH', 'SUFFIX_CODE', 'TESTER_CONFIG', 'GU_COMPARE_PARAM', 'STA_COMPARE_PARAM', 'DNR', 'SITE', 'DPAT', 'BS_NAME', 'GU_NAME', 'C_SPEC', 'TEST_ENG', 'TEST_OPERATION', 'ORDER_COMMENT', 'HIGH_YIELD', 'VISION_LOSS_YIELD', 'VISION_YIELD', 'LOSS_YIELD', 'RETEST_YN', 'FT_PROGRAM_PATH', 'QA_PROGRAM_PATH', 'GU_PROGRAM_PATH', 'EFFECTIVE_TIME', 'TPL_RULE_TEMP', 'TPL_RULE_TEMP_PATH', 'ALARM_DATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER'],
                'ET_FT_TEST_SPEC': ['id', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE', 'TESTER', 'INV_ID', 'TEST_SPEC_TYPE', 'APPROVAL_STATE', 'ACTV_YN', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'COMPANY_ID', 'DISABLE_USER', 'DISABLE_REASON', 'DISABLE_TIME', 'NOTE', 'APPROVE_USER', 'APPROVE_TIME', 'ORT_QTY', 'REMAIN_QTY', 'STANDARD_YIELD', 'LOW_YIELD', 'DOWN_YIELD', 'TEST_AREA', 'HANDLER', 'TEMPERATURE', 'FT_PROGRAM', 'QA_PROGRAM', 'GU_PROGRAM', 'TB_PN', 'HB_PN', 'TIB', 'TEST_TIME', 'UPH', 'SUFFIX_CODE', 'TESTER_CONFIG', 'GU_COMPARE_PARAM', 'STA_COMPARE_PARAM', 'DNR', 'SITE', 'DPAT', 'BS_NAME', 'GU_NAME', 'C_SPEC', 'TEST_ENG', 'TEST_OPERATION', 'ORDER_COMMENT', 'HIGH_YIELD', 'VISION_LOSS_YIELD', 'VISION_YIELD', 'LOSS_YIELD', 'RETEST_YN', 'FT_PROGRAM_PATH', 'QA_PROGRAM_PATH', 'GU_PROGRAM_PATH', 'EFFECTIVE_TIME', 'TPL_RULE_TEMP', 'TPL_RULE_TEMP_PATH', 'ALARM_DATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER'],
                
                # 🚨 修复6：库存管理表 - 修正为实际中文字段名（含id字段）
                'TCC_INV': ['id', 'UNNAMED__0', '硬件编码', '关键硬件', '图片', '寿命状态', '仓库', '初始库位', '当前储位1', '当前储位2', '责任人', '周期消耗数', '当前库位', '封装形式', '状态', '类别', '设备机型', '寄放方', '备注_状态_SHIPOUT信息_', '类型', '状态_1', '操作', 'data_source', 'source_priority', 'last_sync_time', 'sync_status', 'mysql_hash', 'excel_override'],
                'tcc_inv': ['id', 'UNNAMED__0', '硬件编码', '关键硬件', '图片', '寿命状态', '仓库', '初始库位', '当前储位1', '当前储位2', '责任人', '周期消耗数', '当前库位', '封装形式', '状态', '类别', '设备机型', '寄放方', '备注_状态_SHIPOUT信息_', '类型', '状态_1', '操作', 'data_source', 'source_priority', 'last_sync_time', 'sync_status', 'mysql_hash', 'excel_override'],
                
                # 🚨 修复7：CT表 - 修正为实际的34个字段（含id字段）
                'CT': ['id', 'LOT_ID', 'WORK_ORDER_ID', 'PROD_ID', 'DEVICE', 'PKG_PN', 'CHIP_ID', 'FLOW_ID', 'STAGE', 'LOT_QTY', 'ACT_QTY', 'GOOD_QTY', 'REJECT_QTY', 'LOSS_QTY', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'LOT_START_TIME', 'LOT_END_TIME', 'SETUP_TIME', 'FT_TEST_PROGRAM', 'IS_HALF_LOT_DOWN', 'FIRST_PASS_YIELD', 'FINAL_YIELD', 'VM_QTY', 'ALARM_BIN', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'FAC_ID', 'TRACK_CNT', 'COST_TIME', 'created_at', 'updated_at'],
                'ct': ['id', 'LOT_ID', 'WORK_ORDER_ID', 'PROD_ID', 'DEVICE', 'PKG_PN', 'CHIP_ID', 'FLOW_ID', 'STAGE', 'LOT_QTY', 'ACT_QTY', 'GOOD_QTY', 'REJECT_QTY', 'LOSS_QTY', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'LOT_START_TIME', 'LOT_END_TIME', 'SETUP_TIME', 'FT_TEST_PROGRAM', 'IS_HALF_LOT_DOWN', 'FIRST_PASS_YIELD', 'FINAL_YIELD', 'VM_QTY', 'ALARM_BIN', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'FAC_ID', 'TRACK_CNT', 'COST_TIME', 'created_at', 'updated_at'],
                
                # 🚨 修复8：优先级配置表 - 使用Excel匹配的大写字段名
                'devicepriorityconfig': ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],
                'device_priority_config': ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],

                'lotpriorityconfig': ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],
                'lot_priority_config': ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],
                
                # 🚨 修复9：已排产批次表 - 修正为实际的字段（含id字段，移除已删除的SCHDULED_TIME字段，增加智能排产详情字段）
                'lotprioritydone': ['id', 'PRIORITY', 'HANDLER_ID', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME', 'match_type', 'comprehensive_score', 'processing_time', 'changeover_time', 'algorithm_version'],
                'lot_priority_done': ['id', 'PRIORITY', 'HANDLER_ID', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME'],
                
                # 🚨 修复10：配方文件表 - 修正为实际的32个字段（含id字段）
                'ET_RECIPE_FILE': ['id', 'PROD_ID', 'COMPANY_ID', 'STAGE', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'RECIPE_FILE_NAME', 'RECIPE_FILE_PATH', 'APPROVAL_STATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'PROD_TYPE', 'EQP_TYPE', 'HANDLER_CONFIG', 'SIMP_RECIPE_FILE_PATH', 'RECIPE_VER', 'SUB_FAC', 'KIT_PN', 'SOCKET_PN', 'FAMILY', 'COORDINATE_ONE', 'COORDINATE_TWO', 'COORDINATE_THREE'],
                'recipe_file': ['id', 'PROD_ID', 'COMPANY_ID', 'STAGE', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'RECIPE_FILE_NAME', 'RECIPE_FILE_PATH', 'APPROVAL_STATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'PROD_TYPE', 'EQP_TYPE', 'HANDLER_CONFIG', 'SIMP_RECIPE_FILE_PATH', 'RECIPE_VER', 'SUB_FAC', 'KIT_PN', 'SOCKET_PN', 'FAMILY', 'COORDINATE_ONE', 'COORDINATE_TWO', 'COORDINATE_THREE'],
                
                # 兼容性映射（保持向后兼容）
                'test_spec': ['TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE', 'TESTER', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'TEST_TIME', 'UPH']
            }
            
            columns = column_mapping.get(table_name, [])
            
            # 🔍 字段验证和修复日志
            if columns:
                logger.info(f"✅ 字段映射已修复 - 表:{table_name}, 字段数:{len(columns)}, 数据源:{'MySQL' if self.mysql_available else 'Excel'}")
            else:
                logger.warning(f"⚠️ 未找到字段映射 - 表:{table_name}, 请检查配置")
            
            return {
                'success': True,
                'columns': columns,
                'data_source': 'MySQL' if self.mysql_available else 'Excel',
                'field_count': len(columns),
                'mapping_status': 'fixed' if columns else 'missing'
            }
        except Exception as e:
            logger.error(f"❌ 获取字段映射失败 - 表:{table_name}, 错误:{str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_field_mapping(self, table_name: str) -> Dict:
        """🔍 验证字段映射与数据库结构的一致性"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法验证字段映射'
                }
            
            # 获取代码定义的字段
            code_result = self.get_table_columns(table_name)
            if not code_result['success']:
                return code_result
                
            code_columns = set(code_result['columns'])
            
            # 获取数据库实际字段
            database = 'aps'
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
                
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE {table_name}")
                db_columns = set([row[0] for row in cursor.fetchall() if row[0] not in ['id', 'created_at', 'updated_at']])
                
            connection.close()
            
            # 比较字段
            missing_in_code = db_columns - code_columns
            extra_in_code = code_columns - db_columns
            common_fields = code_columns & db_columns
            
            match_rate = len(common_fields) / len(db_columns) * 100 if db_columns else 0
            
            validation_result = {
                'success': True,
                'table_name': table_name,
                'database': database,
                'code_fields_count': len(code_columns),
                'db_fields_count': len(db_columns),
                'common_fields_count': len(common_fields),
                'match_rate': round(match_rate, 2),
                'missing_in_code': list(missing_in_code),
                'extra_in_code': list(extra_in_code),
                'status': 'perfect' if match_rate == 100 else 'partial' if match_rate > 80 else 'poor'
            }
            
            # 记录验证结果
            if match_rate == 100:
                logger.info(f"🎉 字段映射完美匹配 - 表:{table_name}, 匹配率:{match_rate}%")
            elif match_rate > 80:
                logger.warning(f"⚠️ 字段映射部分匹配 - 表:{table_name}, 匹配率:{match_rate}%, 缺失:{len(missing_in_code)}个")
            else:
                logger.error(f"❌ 字段映射严重不匹配 - 表:{table_name}, 匹配率:{match_rate}%")
                
            return validation_result
            
        except Exception as e:
            logger.error(f"❌ 字段映射验证失败 - 表:{table_name}, 错误:{str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_tables(self) -> Dict:
        """获取可用表格列表（API v2兼容方法）"""
        try:
            tables = [
                'wait_lot', 'ET_WAIT_LOT', 'test_spec', 'recipe_file', 
                'equipment_status', 'uph_data', 'priority_config'
            ]
            return {
                'success': True,
                'tables': tables,
                'data_source': 'MySQL' if self.mysql_available else 'Excel'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_table_data(self, table_name: str, format_type: str = 'excel', filters: List = None) -> Dict:
        """导出表格数据（API v2兼容方法）"""
        try:
            import os
            import pandas as pd
            from flask import current_app
            
            # 获取完整的表格数据（不分页）
            data_result = self.get_table_data(table_name, page=1, per_page=10000, filters=filters)
            
            if not data_result['success']:
                return {
                    'success': False,
                    'error': f'获取数据失败: {data_result.get("error", "未知错误")}'
                }
            
            data = data_result['data']
            columns = data_result['columns']
            total_records = data_result['total']
            
            if not data:
                return {
                    'success': False,
                    'error': '没有数据可以导出'
                }
            
            # 创建导出目录
            export_dir = os.path.join(current_app.root_path, 'static', 'exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'{table_name}_export_{timestamp}.xlsx'
            filepath = os.path.join(export_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 确保列顺序正确
            if columns:
                # 只保留存在的列
                available_columns = [col for col in columns if col in df.columns]
                if available_columns:
                    df = df[available_columns]
            
            # 导出到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=table_name, index=False)
                
                # 获取工作表对象并设置列宽
                worksheet = writer.sheets[table_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 生成下载URL
            export_url = f'/static/exports/{filename}'
            
            logger.info(f"成功导出表格 {table_name}，记录数: {total_records}，文件: {filename}")
            
            return {
                'success': True,
                'export_url': export_url,
                'filename': filename,
                'records_count': total_records,
                'format': format_type,
                'table_name': table_name
            }
            
        except Exception as e:
            logger.error(f"导出表格数据失败: {e}")
            return {
                'success': False,
                'error': f'导出失败: {str(e)}'
            }
    
    def create_record(self, table_name: str, data: Dict) -> Dict:
        """创建新记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法创建记录'
                }
            
            # 数据预处理 - 处理日期字段和数据类型
            processed_data = self._preprocess_record_data(table_name, data)
            
            if not processed_data:
                return {
                    'success': False,
                    'error': '没有有效的数据可以插入'
                }
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 构建插入语句
                columns = list(processed_data.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                column_names = ', '.join(columns)
                
                sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
                values = list(processed_data.values())
                
                logger.debug(f"执行插入SQL: {sql}")
                logger.debug(f"插入数据: {values}")
                
                cursor.execute(sql, values)
                connection.commit()
                
                # 获取插入的记录ID
                record_id = cursor.lastrowid
                
            connection.close()
            
            # 清理缓存
            # 失效相关缓存
            self.invalidate_cache_on_data_change(table_name)
            
            logger.info(f"成功创建{table_name}记录，ID: {record_id}")
            return {
                'success': True,
                'record_id': record_id,
                'message': '记录创建成功'
            }
            
        except Exception as e:
            logger.error(f"创建{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'创建记录失败: {str(e)}'
            }
    
    def update_record(self, table_name: str, data: Dict) -> Dict:
        """更新记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法更新记录'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            if primary_key not in data:
                return {
                    'success': False,
                    'error': f'缺少主键字段: {primary_key}'
                }
            
            # 保存主键值
            primary_key_value = data[primary_key]
            
            # 数据预处理 - 处理日期字段和数据类型
            processed_data = self._preprocess_record_data(table_name, data)
            
            # 移除主键，避免更新主键
            if primary_key in processed_data:
                processed_data.pop(primary_key)
            
            if not processed_data:  # 如果除了主键没有其他字段
                return {
                    'success': False,
                    'error': '没有要更新的字段'
                }
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 构建更新语句
                set_clauses = ', '.join([f"{col} = %s" for col in processed_data.keys()])
                sql = f"UPDATE {table_name} SET {set_clauses} WHERE {primary_key} = %s"
                
                values = list(processed_data.values()) + [primary_key_value]
                
                logger.debug(f"执行更新SQL: {sql}")
                logger.debug(f"更新数据: {values}")
                
                cursor.execute(sql, values)
                affected_rows = cursor.rowcount
                connection.commit()
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功更新{table_name}记录，主键: {primary_key_value}，影响行数: {affected_rows}")
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录更新成功'
            }
            
        except Exception as e:
            logger.error(f"更新{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'更新记录失败: {str(e)}'
            }
    
    def delete_record(self, table_name: str, record_id: str) -> Dict:
        """删除单条记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法删除记录'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                sql = f"DELETE FROM {table_name} WHERE {primary_key} = %s"
                cursor.execute(sql, [record_id])
                affected_rows = cursor.rowcount
                connection.commit()
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功删除{table_name}记录，主键: {record_id}，影响行数: {affected_rows}")
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录删除成功'
            }
            
        except Exception as e:
            logger.error(f"删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'删除记录失败: {str(e)}'
            }
    
    def batch_delete_records(self, table_name: str, record_ids: List[str]) -> Dict:
        """批量删除记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法删除记录'
                }
            
            if not record_ids:
                return {
                    'success': False,
                    'error': '没有提供要删除的记录ID'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            # 处理主键类型 - 特别是字符串类型的LOT_ID
            processed_ids = []
            for record_id in record_ids:
                # 确保记录ID是正确的格式
                if primary_key == 'LOT_ID' or 'LOT_ID' in primary_key:
                    # LOT_ID通常是字符串，保持原样
                    processed_ids.append(str(record_id))
                else:
                    # 数值型ID，尝试转换
                    try:
                        processed_ids.append(int(record_id))
                    except (ValueError, TypeError):
                        processed_ids.append(str(record_id))
            
            logger.debug(f"批量删除 {table_name}，主键: {primary_key}，ID列表: {processed_ids}")
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 构建批量删除语句
                placeholders = ', '.join(['%s'] * len(processed_ids))
                sql = f"DELETE FROM {table_name} WHERE {primary_key} IN ({placeholders})"
                
                logger.debug(f"执行批量删除SQL: {sql}")
                logger.debug(f"删除ID列表: {processed_ids}")
                
                cursor.execute(sql, processed_ids)
                deleted_count = cursor.rowcount
                connection.commit()
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功批量删除{table_name}记录，删除数量: {deleted_count}")
            return {
                'success': True,
                'deleted_count': deleted_count,
                'message': f'成功删除 {deleted_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"批量删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'批量删除失败: {str(e)}'
            }
    def _get_device_priority_data(self) -> List[Dict]:
        """获取产品优先级配置数据 - 使用缓存优化"""
        cache_key = "device_priority_config"
        try:
            if self.mysql_available:
                return self._get_cached_data(cache_key, self._get_device_priority_from_mysql)
            elif self.excel_available:
                return self._get_cached_data(cache_key + "_excel", self._get_device_priority_from_excel)
            else:
                return []
        except Exception as e:
            logger.error(f"获取产品优先级数据失败: {e}")
            return []
    
    def _get_lot_priority_data(self) -> List[Dict]:
        """获取批次优先级配置数据 - 使用缓存优化"""
        cache_key = "lot_priority_config"
        try:
            if self.mysql_available:
                return self._get_cached_data(cache_key, self._get_lot_priority_from_mysql)
            elif self.excel_available:
                return self._get_cached_data(cache_key + "_excel", self._get_lot_priority_from_excel)
            else:
                return []
        except Exception as e:
            logger.error(f"获取批次优先级数据失败: {e}")
            return []
    
    def _get_lotprioritydone_data(self) -> List[Dict]:
        """获取已排产批次数据"""
        try:
            if self.mysql_available:
                return self._get_lotprioritydone_from_mysql()
            else:
                return []
        except Exception as e:
            logger.error(f"获取已排产批次数据失败: {e}")
            return []
    
    def _get_device_priority_from_mysql(self) -> List[Dict]:
        """从MySQL获取产品优先级配置数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps_system',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM devicepriorityconfig ORDER BY id")
                data = []
                for row in cursor.fetchall():
                    record = dict(row)
                    for time_field in ['from_time', 'end_time', 'refresh_time', 'created_at', 'updated_at']:
                        if record.get(time_field):
                            record[time_field] = str(record[time_field])
                    data.append(record)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(data)} 条产品优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"MySQL获取产品优先级配置数据失败: {e}")
            return []
    
    def _get_lot_priority_from_mysql(self) -> List[Dict]:
        """从MySQL获取批次优先级配置数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps_system',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM lotpriorityconfig ORDER BY id")
                data = []
                for row in cursor.fetchall():
                    record = dict(row)
                    for time_field in ['refresh_time', 'created_at', 'updated_at']:
                        if record.get(time_field):
                            record[time_field] = str(record[time_field])
                    data.append(record)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(data)} 条批次优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"MySQL获取批次优先级配置数据失败: {e}")
            return []
    
    def _get_device_priority_from_excel(self) -> List[Dict]:
        """从Excel获取产品优先级配置数据"""
        try:
            file_path = os.path.join(self.excel_path, 'devicepriorityconfig.xlsx')
            if not os.path.exists(file_path):
                logger.warning(f"Excel文件不存在: {file_path}")
                return []
            
            df = pd.read_excel(file_path)
            data = []
            for _, row in df.iterrows():
                record = {
                    'id': row.get('ID'),
                    'device': str(row.get('DEVICE', '')),
                    'priority': str(row.get('PRIORITY', '')),
                    'from_time': str(row.get('FROM_TIME', '')) if pd.notna(row.get('FROM_TIME')) else None,
                    'end_time': str(row.get('END_TIME', '')) if pd.notna(row.get('END_TIME')) else None,
                    'refresh_time': str(row.get('REFRESH_TIME', '')) if pd.notna(row.get('REFRESH_TIME')) else None,
                    'user': str(row.get('USER', '')) if pd.notna(row.get('USER')) else None
                }
                data.append(record)
            
            logger.info(f"从Excel获取到 {len(data)} 条产品优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"Excel获取产品优先级配置数据失败: {e}")
            return []
    
    def _get_lot_priority_from_excel(self) -> List[Dict]:
        """从Excel获取批次优先级配置数据"""
        try:
            file_path = os.path.join(self.excel_path, 'lotpriorityconfig.xlsx')
            if not os.path.exists(file_path):
                logger.warning(f"Excel文件不存在: {file_path}")
                return []
            
            df = pd.read_excel(file_path)
            data = []
            for _, row in df.iterrows():
                record = {
                    'id': row.get('ID'),
                    'device': str(row.get('DEVICE', '')),
                    'stage': str(row.get('STAGE', '')) if pd.notna(row.get('STAGE')) else None,
                    'priority': str(row.get('PRIORITY', '')),
                    'refresh_time': str(row.get('REFRESH_TIME', '')) if pd.notna(row.get('REFRESH_TIME')) else None,
                    'user': str(row.get('USER', '')) if pd.notna(row.get('USER')) else None
                }
                data.append(record)
            
            logger.info(f"从Excel获取到 {len(data)} 条批次优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"Excel获取批次优先级配置数据失败: {e}")
            return []
    
    def _get_lotprioritydone_from_mysql(self) -> List[Dict]:
        """从MySQL获取已排产批次数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',  # 已排产批次表在aps数据库
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT * FROM lotprioritydone 
                    ORDER BY CREATE_TIME DESC, id DESC
                    LIMIT 1000
                """)
                
                lotprioritydone_data = []
                for row in cursor.fetchall():
                    record = dict(row)
                    # 转换时间字段为字符串格式
                    for time_field in ['RELEASE_TIME', 'CREATE_TIME', 'created_at', 'updated_at']:
                        if record.get(time_field):
                            record[time_field] = str(record[time_field])
                    lotprioritydone_data.append(record)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(lotprioritydone_data)} 条已排产批次数据")
            return lotprioritydone_data
            
        except Exception as e:
            logger.error(f"MySQL获取已排产批次数据失败: {e}")
            return []

from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from app import db
from app.auth import bp
from app.models import User, UserActionLog
from urllib.parse import urlparse
import logging
import json
from datetime import datetime
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        logger.info(f"尝试登录用户: {username}")
        
        try:
            user = User.query.filter_by(username=username).first()
            if user is None:
                logger.warning(f"登录失败: 用户 {username} 不存在")
                flash('Invalid username or password')
                return redirect(url_for('auth.login'))
            
            logger.info(f"找到用户: {user.username}, 角色: {user.role}, 密码哈希: {user.password_hash}")
            password_correct = user.check_password(password)
            logger.info(f"密码验证结果: {password_correct}")
            
            if not password_correct:
                logger.warning(f"登录失败: 用户 {username} 密码不正确")
                flash('Invalid username or password')
                return redirect(url_for('auth.login'))
            
            logger.info(f"用户 {username} 登录成功")
            login_user(user)
            
            # 记录登录操作
            UserActionLog.log_action(
                username=username,
                action_type='login',
                target_model='user',
                target_id=username,
                details=json.dumps({
                    'success': True,
                    'timestamp': datetime.utcnow().isoformat()
                }),
                request=request
            )
            
            next_page = request.args.get('next')
            if not next_page or urlparse(next_page).netloc != '':
                next_page = url_for('main.index')
            return redirect(next_page)
        except Exception as e:
            logger.error(f"登录过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            flash('An error occurred during login. Please try again.')
            return redirect(url_for('auth.login'))
    
    return render_template('auth/login.html')

@bp.route('/logout')
@login_required
def logout():
    username = current_user.username
    logout_user()
    
    # 记录登出操作
    UserActionLog.log_action(
        username=username,
        action_type='logout',
        target_model='user',
        target_id=username,
        details=json.dumps({
            'success': True,
            'timestamp': datetime.utcnow().isoformat()
        }),
        request=request
    )
    
    return redirect(url_for('auth.login'))

# 用户管理API已迁移到 /api/users/* 路由（在app/api/auth.py中实现）
# 这里只保留登录/登出相关的auth功能

@bp.route('/reset_admin_password')
def reset_admin_password():
    """紧急密码重置功能 - 仅用于开发/调试"""
    try:
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            # 如果admin用户不存在，创建一个
            admin = User(username='admin', role='admin')
            db.session.add(admin)
            
        # 设置密码为123
        admin.set_password('123')
        db.session.commit()
        
        # 验证密码是否正确设置
        admin_check = User.query.filter_by(username='admin').first()
        password_ok = admin_check and admin_check.check_password('123')
        
        logger.info(f"管理员用户密码重置结果: {'成功' if password_ok else '失败'}")
        logger.info(f"密码哈希值: {admin.password_hash}")
        
        return jsonify({
            'success': True,
            'message': '管理员密码已重置为123',
            'password_verified': password_ok,
            'password_hash': admin.password_hash
        })
    except Exception as e:
        logger.error(f"密码重置失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'密码重置失败: {str(e)}'
        })
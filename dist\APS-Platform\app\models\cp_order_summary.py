#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CP订单汇总表数据模型
严格按照Excel表头字段顺序创建 - 已更新为与Excel完全一致
"""

from datetime import datetime
from app import db

class CpOrderSummary(db.Model):
    """CP订单汇总表 - 严格按照Excel表头字段顺序"""
    __tablename__ = 'cp_order_summary'
    __table_args__ = {'extend_existing': True}
    
    # 主键
    id = db.Column(db.Integer, primary_key=True, comment='主键ID')
    
    # 严格按照Excel表头从左到右的顺序 (1-22)
    processing_type = db.Column(db.String(100), comment='1. 加工属性', index=True)
    contractor_name = db.Column(db.String(200), comment='2. 加工承揽商', index=True)
    contractor_contact = db.Column(db.String(100), comment='3. 联系人')
    contractor_address = db.Column(db.String(500), comment='4. 地址')
    contractor_phone = db.Column(db.String(50), comment='5. 电话')
    contractor_fax = db.Column(db.String(50), comment='6. 传真')
    client_name = db.Column(db.String(200), comment='7. 加工委托方')
    client_contact = db.Column(db.String(100), comment='8. 联系人.1')
    client_location = db.Column(db.String(500), comment='9. 地点')
    client_phone = db.Column(db.String(50), comment='10. 电话.1')
    client_fax = db.Column(db.String(50), comment='11. 传真.1')
    order_number = db.Column(db.String(64), comment='12. 订单号', index=True)
    product_name = db.Column(db.String(200), comment='13. 产品名称')
    chip_name = db.Column(db.String(100), comment='14. 芯片名称')
    chip_batch = db.Column(db.String(100), comment='15. 芯片批号')
    processing_pieces = db.Column(db.Integer, comment='16. 加工片数')
    finished_model = db.Column(db.String(100), comment='17. 成品型号')
    wafer_numbers = db.Column(db.Text, comment='18. 片号')
    cp_mapping = db.Column(db.String(100), comment='19. CP Mapping')  # 处理换行符
    package_method = db.Column(db.String(100), comment='20. 包装方式')
    process_step = db.Column(db.String(50), comment='21. 工序')
    shipping_address = db.Column(db.String(500), comment='22. 发货地址')
    
    # 系统字段
    source_file = db.Column(db.String(512), comment='源文件')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    @classmethod
    def create_from_parsed_data(cls, parsed_data, source_file=None):
        """从解析的数据创建CP订单汇总记录"""
        
        # 字段映射 - Excel字段名 -> 数据库字段名 (完全匹配Excel顺序)
        field_mapping = {
            '加工属性': 'processing_type',
            '加工承揽商': 'contractor_name',
            '联系人': 'contractor_contact',
            '地址': 'contractor_address',
            '电话': 'contractor_phone',
            '传真': 'contractor_fax',
            '加工委托方': 'client_name',
            '联系人.1': 'client_contact',
            '地点': 'client_location',
            '电话.1': 'client_phone',
            '传真.1': 'client_fax',
            '订单号': 'order_number',
            '产品名称': 'product_name',
            '芯片名称': 'chip_name',
            '芯片批号': 'chip_batch',
            '加工片数': 'processing_pieces',
            '成品型号': 'finished_model',
            '片号': 'wafer_numbers',
            'CP\nMapping': 'cp_mapping',    # 处理换行符
            'CP Mapping': 'cp_mapping',     # 备用映射
            '包装方式': 'package_method',
            '工序': 'process_step',
            '发货地址': 'shipping_address'
        }
        
        # 创建实例
        instance = cls()
        
        # 映射数据
        for excel_field, db_field in field_mapping.items():
            if excel_field in parsed_data:
                value = parsed_data[excel_field]
                
                # 处理数值字段
                if db_field == 'processing_pieces' and value:
                    try:
                        setattr(instance, db_field, int(value))
                    except:
                        pass
                        
                # 处理普通字段
                else:
                    setattr(instance, db_field, value)
        
        # 设置源文件
        if source_file:
            instance.source_file = source_file
            
        return instance
    
    def to_dict(self):
        """转换为字典格式 - 按Excel列顺序"""
        return {
            'id': self.id,
            'processing_type': self.processing_type,
            'contractor_name': self.contractor_name,
            'contractor_contact': self.contractor_contact,
            'contractor_address': self.contractor_address,
            'contractor_phone': self.contractor_phone,
            'contractor_fax': self.contractor_fax,
            'client_name': self.client_name,
            'client_contact': self.client_contact,
            'client_location': self.client_location,
            'client_phone': self.client_phone,
            'client_fax': self.client_fax,
            'order_number': self.order_number,
            'product_name': self.product_name,
            'chip_name': self.chip_name,
            'chip_batch': self.chip_batch,
            'processing_pieces': self.processing_pieces,
            'finished_model': self.finished_model,
            'wafer_numbers': self.wafer_numbers,
            'cp_mapping': self.cp_mapping,
            'package_method': self.package_method,
            'process_step': self.process_step,
            'shipping_address': self.shipping_address,
            'source_file': self.source_file,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<CpOrderSummary {self.order_number}: {self.product_name}>' 

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理API模块
"""

from flask import Blueprint

# 创建订单处理蓝图
orders_bp = Blueprint('orders_api', __name__, url_prefix='/api/v2/orders')

# 导入并注册所有子模块
from . import optimized_parser_api
from . import high_concurrency_api
from . import semi_auto_api  # 确保导入半自动API
from . import order_data_api  # 导入新的订单数据管理API

# 注册子蓝图
orders_bp.register_blueprint(optimized_parser_api.bp)
orders_bp.register_blueprint(high_concurrency_api.high_concurrency_bp)
orders_bp.register_blueprint(order_data_api.order_data_bp)

# 直接导入semi_auto_api的路由，让它们自动注册到orders_bp
# semi_auto_api.py 中的路由已经用 @orders_bp.route 装饰器定义 
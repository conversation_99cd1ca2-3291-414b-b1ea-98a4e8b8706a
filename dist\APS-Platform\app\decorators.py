from functools import wraps
from flask import abort, current_app, request
from flask_login import current_user, login_required

def permission_required(menu_id):
    """检查用户是否有权限访问指定菜单"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                # 对API请求返回JSON错误
                if request.path.startswith('/api/'):
                    from flask import jsonify
                    return jsonify({'error': '需要登录', 'message': '请先登录'}), 401
                abort(401)
                
            if not current_user.has_permission(menu_id):
                # 对API请求返回JSON错误
                if request.path.startswith('/api/'):
                    from flask import jsonify
                    return jsonify({'error': '权限不足', 'message': f'缺少菜单权限: {menu_id}'}), 403
                abort(403)
                
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """检查用户是否为管理员"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            # 对API请求返回JSON错误
            if request.path.startswith('/api/'):
                from flask import jsonify
                return jsonify({'error': '需要登录', 'message': '请先登录'}), 401
            abort(401)
            
        if current_user.role != 'admin':
            # 对API请求返回JSON错误
            if request.path.startswith('/api/'):
                from flask import jsonify
                return jsonify({'error': '权限不足', 'message': '只有管理员可以访问此功能'}), 403
            abort(403)
            
        return f(*args, **kwargs)
    return decorated_function

def log_action(action_type):
    """记录用户操作"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from app.models import UserActionLog
            
            # 执行原始函数
            result = f(*args, **kwargs)
            
            # 记录操作日志
            if current_user.is_authenticated:
                # 获取目标模型和ID
                target_model = request.endpoint.split('.')[-1]  # 从endpoint获取模型名
                target_id = kwargs.get('id')  # 从URL参数获取ID
                
                # 记录操作
                UserActionLog.log_action(
                    username=current_user.username,
                    action_type=action_type,
                    target_model=target_model,
                    target_id=target_id,
                    details=str(request.get_json()) if request.is_json else None,
                    request=request
                )
            
            return result
        return decorated_function
    return decorator 
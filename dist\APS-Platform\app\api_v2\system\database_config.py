"""
统一数据库配置管理API
提供数据库配置的增删改查、测试连接、导入导出等功能
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models.system.database_config import DatabaseConfig, DatabaseMapping
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

db_config_bp = Blueprint('database_config', __name__)

@db_config_bp.route('/configs', methods=['GET'])
@login_required
def get_database_configs():
    """获取所有数据库配置"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        db_type = request.args.get('db_type', '')
        is_active = request.args.get('is_active', '')
        
        query = DatabaseConfig.query
        
        # 搜索过滤
        if search:
            query = query.filter(
                db.or_(
                    DatabaseConfig.name.contains(search),
                    DatabaseConfig.description.contains(search),
                    DatabaseConfig.host.contains(search)
                )
            )
        
        # 数据库类型过滤
        if db_type:
            query = query.filter(DatabaseConfig.db_type == db_type)
        
        # 状态过滤
        if is_active:
            query = query.filter(DatabaseConfig.is_active == (is_active.lower() == 'true'))
        
        # 排序
        query = query.order_by(DatabaseConfig.is_default.desc(), DatabaseConfig.name)
        
        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        configs = pagination.items
        
        return jsonify({
            'configs': [config.to_dict() for config in configs],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
        
    except Exception as e:
        logger.error(f"获取数据库配置失败: {str(e)}")
        return jsonify({'error': '获取数据库配置失败'}), 500

@db_config_bp.route('/configs/<int:config_id>', methods=['GET'])
@login_required
def get_database_config(config_id):
    """获取单个数据库配置"""
    try:
        config = DatabaseConfig.query.get_or_404(config_id)
        return jsonify(config.to_dict(include_sensitive=True))
    except Exception as e:
        logger.error(f"获取数据库配置失败: {str(e)}")
        return jsonify({'error': '获取数据库配置失败'}), 500

@db_config_bp.route('/configs', methods=['POST'])
@login_required
def create_database_config():
    """创建数据库配置"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'db_type', 'host', 'port', 'username', 'database_name']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({'error': f'缺少必填字段: {", ".join(missing_fields)}'}), 400
        
        # 检查名称是否已存在
        existing_config = DatabaseConfig.query.filter_by(name=data['name']).first()
        if existing_config:
            return jsonify({'error': '配置名称已存在'}), 400
        
        # 如果设置为默认配置，取消其他默认配置
        if data.get('is_default'):
            DatabaseConfig.query.filter_by(
                db_type=data['db_type'], 
                is_default=True
            ).update({'is_default': False})
        
        # 创建配置
        config = DatabaseConfig(
            name=data['name'],
            db_type=data['db_type'],
            host=data['host'],
            port=data['port'],
            username=data['username'],
            password=data.get('password', ''),
            database_name=data['database_name'],
            charset=data.get('charset', 'utf8mb4'),
            timezone=data.get('timezone', 'Asia/Shanghai'),
            ssl_enabled=data.get('ssl_enabled', False),
            ssl_cert_path=data.get('ssl_cert_path', ''),
            connection_pool_size=data.get('connection_pool_size', 10),
            connection_timeout=data.get('connection_timeout', 30),
            extra_params=json.dumps(data.get('extra_params', {})),
            is_default=data.get('is_default', False),
            is_active=data.get('is_active', True),
            description=data.get('description', '')
        )
        
        db.session.add(config)
        db.session.commit()
        
        logger.info(f"创建数据库配置成功: {config.name}")
        return jsonify(config.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建数据库配置失败: {str(e)}")
        return jsonify({'error': '创建数据库配置失败'}), 500

@db_config_bp.route('/configs/<int:config_id>', methods=['PUT'])
@login_required  
def update_database_config(config_id):
    """更新数据库配置"""
    try:
        config = DatabaseConfig.query.get_or_404(config_id)
        data = request.get_json()
        
        # 检查名称是否与其他配置冲突
        if data.get('name') and data['name'] != config.name:
            existing_config = DatabaseConfig.query.filter_by(name=data['name']).first()
            if existing_config:
                return jsonify({'error': '配置名称已存在'}), 400
        
        # 如果设置为默认配置，取消其他默认配置
        if data.get('is_default') and not config.is_default:
            DatabaseConfig.query.filter_by(
                db_type=data.get('db_type', config.db_type), 
                is_default=True
            ).update({'is_default': False})
        
        # 更新字段
        for field in ['name', 'db_type', 'host', 'port', 'username', 'database_name',
                     'charset', 'timezone', 'ssl_enabled', 'ssl_cert_path',
                     'connection_pool_size', 'connection_timeout', 'is_default',
                     'is_active', 'description']:
            if field in data:
                setattr(config, field, data[field])
        
        # 更新密码
        if 'password' in data and data['password']:
            config.set_password(data['password'])
        
        # 更新额外参数
        if 'extra_params' in data:
            config.extra_params = json.dumps(data['extra_params'])
        
        config.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f"更新数据库配置成功: {config.name}")
        return jsonify(config.to_dict())
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新数据库配置失败: {str(e)}")
        return jsonify({'error': '更新数据库配置失败'}), 500

@db_config_bp.route('/configs/<int:config_id>', methods=['DELETE'])
@login_required
def delete_database_config(config_id):
    """删除数据库配置"""
    try:
        config = DatabaseConfig.query.get_or_404(config_id)
        
        # 检查是否有映射依赖
        mappings = DatabaseMapping.query.filter_by(database_config_id=config_id).count()
        if mappings > 0:
            return jsonify({'error': f'无法删除：该配置被 {mappings} 个映射使用'}), 400
        
        db.session.delete(config)
        db.session.commit()
        
        logger.info(f"删除数据库配置成功: {config.name}")
        return jsonify({'message': '删除成功'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除数据库配置失败: {str(e)}")
        return jsonify({'error': '删除数据库配置失败'}), 500

@db_config_bp.route('/configs/<int:config_id>/test', methods=['POST'])
@login_required
def test_database_connection(config_id):
    """测试数据库连接"""
    try:
        config = DatabaseConfig.query.get_or_404(config_id)
        
        # 执行连接测试
        success, message = config.test_connection()
        
        # 保存测试结果
        db.session.commit()
        
        return jsonify({
            'success': success,
            'message': message,
            'test_time': config.last_test_at.isoformat() if config.last_test_at else None
        })
        
    except Exception as e:
        logger.error(f"测试数据库连接失败: {str(e)}")
        return jsonify({'error': '测试连接失败'}), 500

@db_config_bp.route('/configs/types', methods=['GET'])
@login_required
def get_database_types():
    """获取支持的数据库类型"""
    return jsonify({
        'types': [
            {
                'value': 'mysql',
                'label': 'MySQL',
                'default_port': 3306,
                'charset_options': ['utf8mb4', 'utf8', 'latin1']
            },
            {
                'value': 'postgresql',
                'label': 'PostgreSQL',
                'default_port': 5432,
                'charset_options': ['UTF8', 'LATIN1']
            },
            {
                'value': 'sqlite',
                'label': 'SQLite',
                'default_port': None,
                'charset_options': ['UTF-8']
            }
        ]
    })

@db_config_bp.route('/configs/test', methods=['POST'])
@login_required
def test_connection_form():
    """测试表单中的连接配置（不保存到数据库）"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['db_type', 'host', 'port', 'username', 'database_name']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({'error': f'缺少必填字段: {", ".join(missing_fields)}'}), 400
        
        # 创建临时配置对象进行测试
        temp_config = DatabaseConfig(
            name=data.get('name', '临时测试'),
            db_type=data['db_type'],
            host=data['host'],
            port=data['port'],
            username=data['username'],
            password=data.get('password', ''),
            database_name=data['database_name'],
            charset=data.get('charset', 'utf8mb4')
        )
        
        # 执行连接测试
        success, message = temp_config.test_connection()
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        logger.error(f"测试表单连接失败: {str(e)}")
        return jsonify({'error': '测试连接失败'}), 500

@db_config_bp.route('/configs/test-batch', methods=['POST'])
@login_required
def test_all_connections():
    """批量测试所有配置的连接"""
    try:
        configs = DatabaseConfig.query.filter_by(is_active=True).all()
        results = []
        
        for config in configs:
            success, message = config.test_connection()
            results.append({
                'id': config.id,
                'name': config.name,
                'success': success,
                'message': message
            })
        
        db.session.commit()
        
        return jsonify({
            'results': results,
            'summary': {
                'total': len(results),
                'success': len([r for r in results if r['success']]),
                'failed': len([r for r in results if not r['success']])
            }
        })
        
    except Exception as e:
        logger.error(f"批量测试连接失败: {str(e)}")
        return jsonify({'error': '批量测试失败'}), 500

@db_config_bp.route('/configs/set-default/<int:config_id>', methods=['POST'])
@login_required
def set_default_config(config_id):
    """设置默认配置"""
    try:
        config = DatabaseConfig.query.get_or_404(config_id)
        
        # 取消同类型的其他默认配置
        DatabaseConfig.query.filter_by(
            db_type=config.db_type,
            is_default=True
        ).update({'is_default': False})
        
        # 设置为默认
        config.is_default = True
        config.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"设置默认配置成功: {config.name}")
        return jsonify({'message': '设置成功'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"设置默认配置失败: {str(e)}")
        return jsonify({'error': '设置默认配置失败'}), 500

# 映射管理相关API

@db_config_bp.route('/mappings', methods=['GET'])
@login_required
def get_database_mappings():
    """获取数据库映射"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        mapping_type = request.args.get('mapping_type', '')
        
        query = DatabaseMapping.query.join(DatabaseConfig)
        
        if mapping_type:
            query = query.filter(DatabaseMapping.mapping_type == mapping_type)
        
        query = query.order_by(DatabaseMapping.priority, DatabaseMapping.target_name)
        
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        mappings = pagination.items
        
        return jsonify({
            'mappings': [mapping.to_dict() for mapping in mappings],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages
            }
        })
        
    except Exception as e:
        logger.error(f"获取数据库映射失败: {str(e)}")
        return jsonify({'error': '获取数据库映射失败'}), 500

@db_config_bp.route('/mappings', methods=['POST'])
@login_required
def create_database_mapping():
    """创建数据库映射"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['mapping_type', 'target_name', 'database_config_id']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({'error': f'缺少必填字段: {", ".join(missing_fields)}'}), 400
        
        # 检查映射是否已存在
        existing_mapping = DatabaseMapping.query.filter_by(
            mapping_type=data['mapping_type'],
            target_name=data['target_name']
        ).first()
        if existing_mapping:
            return jsonify({'error': '映射已存在'}), 400
        
        # 验证数据库配置是否存在
        config = DatabaseConfig.query.get(data['database_config_id'])
        if not config:
            return jsonify({'error': '数据库配置不存在'}), 400
        
        # 创建映射
        mapping = DatabaseMapping(
            mapping_type=data['mapping_type'],
            target_name=data['target_name'],
            database_config_id=data['database_config_id'],
            priority=data.get('priority', 100),
            is_active=data.get('is_active', True),
            description=data.get('description', '')
        )
        
        db.session.add(mapping)
        db.session.commit()
        
        logger.info(f"创建数据库映射成功: {mapping.target_name} -> {config.name}")
        return jsonify(mapping.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建数据库映射失败: {str(e)}")
        return jsonify({'error': '创建数据库映射失败'}), 500 
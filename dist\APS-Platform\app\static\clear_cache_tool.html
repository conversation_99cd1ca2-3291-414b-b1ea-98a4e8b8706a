<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>清理菜单缓存</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; }
        .btn { padding: 10px 20px; margin: 10px; background: #b72424; color: white; border: none; cursor: pointer; }
        .btn:hover { background: #a01f1f; }
        .result { margin: 20px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>APS菜单缓存清理工具</h1>
    <p>如果菜单显示不正常，请使用以下工具清理缓存：</p>
    
    <button class="btn" onclick="clearSessionStorage()">清理SessionStorage</button>
    <button class="btn" onclick="clearLocalStorage()">清理LocalStorage</button>
    <button class="btn" onclick="clearAllCache()">清理所有缓存</button>
    <button class="btn" onclick="refreshMenu()">刷新菜单</button>
    
    <div id="result" class="result" style="display:none;"></div>
    
    <script>
        function showResult(message) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.style.display = 'block';
        }
        
        function clearSessionStorage() {
            // 清理菜单相关的sessionStorage
            const keysToRemove = [];
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key && (key.startsWith('aps_menu_') || key.includes('menu'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => sessionStorage.removeItem(key));
            showResult('SessionStorage已清理，共清理 ' + keysToRemove.length + ' 个项目');
        }
        
        function clearLocalStorage() {
            // 清理菜单相关的localStorage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('aps_menu_') || key.includes('menu'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            showResult('LocalStorage已清理，共清理 ' + keysToRemove.length + ' 个项目');
        }
        
        function clearAllCache() {
            clearSessionStorage();
            clearLocalStorage();
            showResult('所有缓存已清理，请刷新页面查看效果');
        }
        
        function refreshMenu() {
            if (window.clearMenuCache && window.refreshMenu) {
                window.clearMenuCache();
                window.refreshMenu();
                showResult('菜单已刷新');
            } else {
                showResult('请在APS系统页面中使用此功能');
            }
        }
    </script>
</body>
</html>
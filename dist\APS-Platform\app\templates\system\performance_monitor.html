{% extends "base.html" %}

{% block title %}系统性能监控{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #b72424;
        --secondary-color: #d73027;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
    }

    /* 文字选中效果优化 */
    ::selection {
        background-color: rgba(255, 255, 255, 0.9);
        color: var(--primary-color);
    }
    
    .metric-card ::selection,
    .page-header ::selection {
        background-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(183, 36, 36, 0.3);
    }

    .navbar-brand, .nav-link.active {
        color: var(--primary-color) !important;
    }

    .metric-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(183, 36, 36, 0.2);
        cursor: pointer;
    }
    
    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        transition: left 0.5s;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(183, 36, 36, 0.3);
    }
    
    .metric-card:hover::before {
        left: 100%;
    }
    
    .metric-card.alert-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        animation: pulse-warning 2s infinite;
    }
    
    .metric-card.alert-danger {
        background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        animation: pulse-danger 2s infinite;
    }
    
    @keyframes pulse-warning {
        0%, 100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
        50% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    }
    
    @keyframes pulse-danger {
        0%, 100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
        50% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
        position: relative;
        z-index: 1;
        font-weight: 500;
    }
    
    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 25px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .chart-container:hover {
        box-shadow: 0 6px 25px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }
    
    .status-indicator {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--success-color);
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
        z-index: 2;
        animation: pulse-online 2s infinite;
    }
    
    @keyframes pulse-online {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    
    .status-indicator.offline {
        background: var(--danger-color);
        box-shadow: 0 0 8px rgba(220, 53, 69, 0.6);
        animation: pulse-offline 1s infinite;
    }
    
    @keyframes pulse-offline {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.3; }
    }
    
    .loading-spinner {
        display: inline-block;
        width: 24px;
        height: 24px;
        border: 3px solid rgba(255,255,255,.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .error-message {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid var(--danger-color);
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        color: #721c24;
        transition: all 0.3s ease;
    }
    
    .error-message:hover {
        background: rgba(220, 53, 69, 0.15);
    }
    
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(183, 36, 36, 0.2);
    }
    
    .page-header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }
    
    .page-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }
    
    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
        transition: all 0.3s ease;
    }
    
    .btn-outline-primary:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(183, 36, 36, 0.3);
    }
    
    .btn-outline-success {
        transition: all 0.3s ease;
    }
    
    .btn-outline-success:hover {
        background-color: var(--success-color);
        border-color: var(--success-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
    
    .btn-outline-danger {
        transition: all 0.3s ease;
    }
    
    .btn-outline-danger:hover {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
    
    /* 卡片悬浮效果 */
    .card {
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .card:hover {
        box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    /* 表单元素优化 */
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
    }
    
    /* 徽章样式优化 */
    .badge {
        transition: all 0.3s ease;
    }
    
    .badge:hover {
        transform: scale(1.05);
    }
    
    /* 图标动画效果 */
    .fa-sync-alt {
        transition: transform 0.3s ease;
    }
    
    .btn:hover .fa-sync-alt {
        transform: rotate(180deg);
    }
    
    /* 响应式优化 */
    @media (max-width: 768px) {
        .metric-card {
            margin-bottom: 15px;
        }
        
        .metric-value {
            font-size: 2rem;
        }
        
        .page-header {
            padding: 20px;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-tachometer-alt"></i> 系统性能监控</h1>
                <p class="text-muted">实时监控系统性能指标和资源使用情况</p>
            </div>
        </div>
    </div>
    
    <!-- 错误提示区域 -->
    <div id="error-container" style="display: none;"></div>
    
    <!-- 性能指标卡片 -->
    <div class="row">
        <div class="col-md-3">
            <div class="metric-card" id="response-time-card">
                <div class="status-indicator" id="response-time-status"></div>
                <div class="metric-value" id="response-time">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-label">平均响应时间 (ms)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" id="concurrent-users-card">
                <div class="status-indicator" id="concurrent-users-status"></div>
                <div class="metric-value" id="concurrent-users">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-label">并发用户数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" id="error-rate-card">
                <div class="status-indicator" id="error-rate-status"></div>
                <div class="metric-value" id="error-rate">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-label">错误率 (%)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" id="cpu-usage-card">
                <div class="status-indicator" id="cpu-usage-status"></div>
                <div class="metric-value" id="cpu-usage">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-label">CPU使用率 (%)</div>
            </div>
        </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-cogs me-2"></i>监控控制</h5>
                    <div>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" id="auto-refresh-btn">
                            <i class="fas fa-play"></i> 自动刷新
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="stop-refresh-btn" style="display: none;">
                            <i class="fas fa-stop"></i> 停止刷新
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6>响应时间趋势图</h6>
                            <p class="text-muted small">图表功能开发中...</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <h6>系统资源使用图</h6>
                            <p class="text-muted small">图表功能开发中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                <div class="row">
                    <div class="col-md-4">
                        <strong>最后更新时间：</strong>
                        <span id="last-update-time">--</span>
                    </div>
                    <div class="col-md-4">
                        <strong>监控状态：</strong>
                        <span id="monitoring-status" class="badge bg-secondary">未知</span>
                    </div>
                    <div class="col-md-4">
                        <strong>数据源：</strong>
                        <span id="data-source">系统监控</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
/**
 * 系统性能监控页面
 */
class PerformanceMonitor {
    constructor() {
        this.updateInterval = null;
        this.isAutoRefresh = false;
        this.lastUpdateTime = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // 手动刷新按钮
        document.getElementById('refresh-btn')?.addEventListener('click', () => {
            this.updateMetrics();
        });

        // 自动刷新按钮
        document.getElementById('auto-refresh-btn')?.addEventListener('click', () => {
            this.startAutoRefresh();
        });

        // 停止刷新按钮
        document.getElementById('stop-refresh-btn')?.addEventListener('click', () => {
            this.stopAutoRefresh();
        });
    }

    async loadInitialData() {
        await this.updateMetrics();
    }

    startAutoRefresh() {
        if (this.isAutoRefresh) return;
        
        this.isAutoRefresh = true;
        document.getElementById('auto-refresh-btn').style.display = 'none';
        document.getElementById('stop-refresh-btn').style.display = 'inline-block';
        document.getElementById('monitoring-status').textContent = '自动刷新中';
        document.getElementById('monitoring-status').className = 'badge bg-success';
        
        // 每5秒更新一次
        this.updateInterval = setInterval(() => {
            this.updateMetrics();
        }, 5000);
    }

    stopAutoRefresh() {
        if (!this.isAutoRefresh) return;
        
        this.isAutoRefresh = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        document.getElementById('auto-refresh-btn').style.display = 'inline-block';
        document.getElementById('stop-refresh-btn').style.display = 'none';
        document.getElementById('monitoring-status').textContent = '手动模式';
        document.getElementById('monitoring-status').className = 'badge bg-secondary';
    }

    async updateMetrics() {
        try {
            this.showLoadingState();
            
            // 尝试获取性能监控数据
            const response = await fetch('/api/v2/system/monitoring/metrics');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.updateUI(data.data);
                this.hideError();
            } else {
                throw new Error(data.message || '获取数据失败');
            }
            
        } catch (error) {
            console.error('获取性能监控数据失败:', error);
            this.showError(error.message);
            this.showMockData(); // 显示模拟数据
        }
        
        this.lastUpdateTime = new Date();
        this.updateLastUpdateTime();
    }

    showLoadingState() {
        const metrics = ['response-time', 'concurrent-users', 'error-rate', 'cpu-usage'];
        metrics.forEach(metric => {
            const element = document.getElementById(metric);
            if (element) {
                element.innerHTML = '<div class="loading-spinner"></div>';
            }
        });
    }

    updateUI(data) {
        // 更新响应时间
        this.updateMetric('response-time', data.avg_response_time || 0, 'ms', 200);
        
        // 更新并发用户数
        this.updateMetric('concurrent-users', data.concurrent_users || 0, '', 250);
        
        // 更新错误率
        this.updateMetric('error-rate', data.error_rate || 0, '%', 5);
        
        // 更新CPU使用率
        this.updateMetric('cpu-usage', data.cpu_percent || 0, '%', 80);
    }

    updateMetric(metricId, value, unit, warningThreshold) {
        const element = document.getElementById(metricId);
        const card = document.getElementById(metricId + '-card');
        const status = document.getElementById(metricId + '-status');
        
        if (element) {
            element.textContent = value + unit;
        }
        
        if (card && status) {
            // 重置样式
            card.className = 'metric-card';
            status.className = 'status-indicator';
            
            // 根据阈值设置告警状态
            if (value > warningThreshold) {
                if (metricId === 'error-rate' || value > warningThreshold * 1.5) {
                    card.classList.add('alert-danger');
                    status.classList.add('offline');
                } else {
                    card.classList.add('alert-warning');
                }
            }
        }
    }

    showMockData() {
        // 显示模拟数据以便测试界面
        const mockData = {
            avg_response_time: Math.floor(Math.random() * 300) + 50,
            concurrent_users: Math.floor(Math.random() * 100) + 10,
            error_rate: Math.random() * 10,
            cpu_percent: Math.floor(Math.random() * 100)
        };
        
        this.updateUI(mockData);
        
        // 显示模拟数据提示
        document.getElementById('data-source').innerHTML = 
            '<span class="text-warning">模拟数据 (API不可用)</span>';
    }

    showError(message) {
        const errorContainer = document.getElementById('error-container');
        if (errorContainer) {
            errorContainer.style.display = 'block';
            errorContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>数据获取失败：</strong>${message}
                    <br><small>正在显示模拟数据以供界面测试</small>
                </div>
            `;
        }
    }

    hideError() {
        const errorContainer = document.getElementById('error-container');
        if (errorContainer) {
            errorContainer.style.display = 'none';
        }
        document.getElementById('data-source').textContent = '系统监控';
    }

    updateLastUpdateTime() {
        const element = document.getElementById('last-update-time');
        if (element && this.lastUpdateTime) {
            element.textContent = this.lastUpdateTime.toLocaleString();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.performanceMonitor = new PerformanceMonitor();
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (window.performanceMonitor) {
        window.performanceMonitor.stopAutoRefresh();
    }
});
</script>
{% endblock %}
{% extends "base.html" %}

{% block title %}系统日志{% endblock %}

{% block extra_css %}
<style>
.log-container {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
}

.log-filters {
    background: white;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.log-content {
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    max-height: 500px;
    overflow-y: auto;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.log-entry {
    padding: 6px 12px;
    border-bottom: 1px solid #f0f0f0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.3;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

.log-entry.warning {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.log-entry.info {
    background-color: #d1ecf1;
    border-left: 4px solid #17a2b8;
}

.log-entry.debug {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

.log-timestamp {
    color: #6c757d;
    font-weight: 500;
    font-size: 10px;
    display: inline-block;
    min-width: 120px;
}

.log-level {
    font-weight: bold;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 9px;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

.log-level.ERROR {
    background-color: #dc3545;
    color: white;
}

.log-level.WARNING {
    background-color: #ffc107;
    color: #212529;
}

.log-level.INFO {
    background-color: #17a2b8;
    color: white;
}

.log-level.DEBUG {
    background-color: #6c757d;
    color: white;
}

.log-message {
    margin-left: 8px;
    word-break: break-word;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
}

.pagination-container {
    background: white;
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-top: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 统计卡片样式优化 */
.stats-card {
    border-radius: 6px;
    margin-bottom: 10px;
}

.stats-card .card-body {
    padding: 12px;
}

.stats-card .h4 {
    font-size: 1.1rem;
    margin: 0;
}

.stats-card .card-title {
    font-size: 0.8rem;
    margin-bottom: 2px;
}

/* 按钮组紧凑样式 */
.btn-group .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 表格紧凑样式 */
.table-sm th,
.table-sm td {
    padding: 0.3rem;
    font-size: 0.85rem;
}

/* 小号文本 */
.small {
    font-size: 0.8rem !important;
}

/* 紧凑的分页 */
.pagination .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="log-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-file-alt me-2"></i>系统日志
            </h4>
            <div>
                <button class="btn btn-outline-info btn-sm me-2" onclick="loadLogStats()">
                    <i class="fas fa-chart-bar me-1"></i>统计
                </button>
                <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
                <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearLogs()">
                    <i class="fas fa-trash me-1"></i>清空显示
                </button>
                <div class="btn-group">
                    <button class="btn btn-outline-warning btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-broom me-1"></i>清理日志
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="cleanupLogs(7)">清理7天前</a></li>
                        <li><a class="dropdown-item" href="#" onclick="cleanupLogs(30)">清理30天前</a></li>
                        <li><a class="dropdown-item" href="#" onclick="cleanupLogs(90)">清理90天前</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="showCustomCleanup()">自定义清理</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 日志统计信息 -->
        <div id="logStatsContainer" style="display: none;" class="mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>日志统计信息
                        <button class="btn btn-sm btn-outline-secondary float-end py-0 px-2" onclick="hideLogStats()">
                            <i class="fas fa-times"></i>
                        </button>
                    </h6>
                </div>
                <div class="card-body py-3">
                    <div class="row" id="logStatsContent">
                        <!-- 统计内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 过滤器 -->
        <div class="log-filters">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="logType" class="form-label small">日志类型</label>
                    <select class="form-select form-select-sm" id="logType" onchange="loadLogs()">
                        <option value="app">应用日志</option>
                        <option value="error">错误日志</option>
                        <option value="user_actions">用户操作</option>
                        <option value="system">系统日志</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="logLevel" class="form-label small">日志级别</label>
                    <select class="form-select form-select-sm" id="logLevel" onchange="loadLogs()">
                        <option value="">全部</option>
                        <option value="ERROR">错误</option>
                        <option value="WARNING">警告</option>
                        <option value="INFO">信息</option>
                        <option value="DEBUG">调试</option>
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="keyword" class="form-label small">关键词搜索</label>
                    <input type="text" class="form-control form-control-sm" id="keyword" placeholder="输入关键词..." onchange="loadLogs()">
                </div>
                <div class="col-md-2">
                    <label for="perPage" class="form-label small">每页显示</label>
                    <select class="form-select form-select-sm" id="perPage" onchange="loadLogs()">
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                        <option value="200">200条</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 日志内容 -->
        <div class="log-content" id="logContent">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载日志...</p>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <div class="d-flex justify-content-between align-items-center">
                <div id="logInfo">
                    <!-- 日志统计信息 -->
                </div>
                <nav>
                    <ul class="pagination mb-0" id="pagination">
                        <!-- 分页按钮 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

        <!-- 自定义清理对话框 -->
<div class="modal fade" id="customCleanupModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h6 class="modal-title">
                    <i class="fas fa-broom me-2"></i>自定义日志清理
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-3">
                <div class="mb-3">
                    <label for="cleanupDays" class="form-label small">清理天数</label>
                    <input type="number" class="form-control form-control-sm" id="cleanupDays" value="7" min="1" max="365">
                    <div class="form-text small">将清理指定天数之前的日志文件</div>
                </div>
                <div class="alert alert-warning py-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <small><strong>注意：</strong>此操作将永久删除旧的日志文件，无法恢复！</small>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning btn-sm" onclick="executeCustomCleanup()">
                    <i class="fas fa-broom me-1"></i>执行清理
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let totalPages = 1;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadLogs();
    // 自动加载统计信息
    loadLogStats();
});

// 加载日志
async function loadLogs(page = 1) {
    currentPage = page;
    
    const logType = document.getElementById('logType').value;
    const logLevel = document.getElementById('logLevel').value;
    const keyword = document.getElementById('keyword').value;
    const perPage = document.getElementById('perPage').value;
    
    const params = new URLSearchParams({
        log_type: logType,
        page: page,
        per_page: perPage
    });
    
    if (logLevel) params.append('level', logLevel);
    if (keyword) params.append('keyword', keyword);
    
    try {
        showLoading();
        
        const response = await fetch(`/api/system_logs?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            showError(data.error);
            return;
        }
        
        displayLogs(data.logs || []);
        updatePagination(data);
        updateLogInfo(data);
        
    } catch (error) {
        console.error('加载日志失败:', error);
        showError(`加载日志失败: ${error.message}`);
    }
}

// 显示加载状态
function showLoading() {
    document.getElementById('logContent').innerHTML = `
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载日志...</p>
        </div>
    `;
}

// 显示错误信息
function showError(message) {
    document.getElementById('logContent').innerHTML = `
        <div class="text-center p-4">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
            <button class="btn btn-primary" onclick="loadLogs()">
                <i class="fas fa-redo me-1"></i>重试
            </button>
        </div>
    `;
}

// 显示日志条目
function displayLogs(logs) {
    const logContent = document.getElementById('logContent');
    
    if (!logs || logs.length === 0) {
        logContent.innerHTML = `
            <div class="text-center p-4">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    没有找到日志记录
                </div>
            </div>
        `;
        return;
    }
    
    const logHtml = logs.map(log => {
        const levelClass = log.level ? log.level.toLowerCase() : 'info';
        return `
            <div class="log-entry ${levelClass}">
                <span class="log-timestamp">${log.timestamp || ''}</span>
                <span class="log-level ${log.level || 'INFO'}">${log.level || 'INFO'}</span>
                <span class="log-message">${escapeHtml(log.message || log.raw || '')}</span>
                ${log.source ? `<br><small class="text-muted ms-2">来源: ${log.source}</small>` : ''}
            </div>
        `;
    }).join('');
    
    logContent.innerHTML = logHtml;
}

// 更新分页信息
function updatePagination(data) {
    totalPages = data.pages || 1;
    const pagination = document.getElementById('pagination');
    
    let paginationHtml = '';
    
    // 上一页
    if (currentPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadLogs(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
    }
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadLogs(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadLogs(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHtml;
}

// 更新日志信息
function updateLogInfo(data) {
    const logInfo = document.getElementById('logInfo');
    logInfo.innerHTML = `
        <small class="text-muted">
            共 ${data.total || 0} 条日志，第 ${currentPage} / ${totalPages} 页
        </small>
    `;
}

// 刷新日志
function refreshLogs() {
    loadLogs(currentPage);
}

// 清空显示
function clearLogs() {
    document.getElementById('logContent').innerHTML = `
        <div class="text-center p-4">
            <div class="alert alert-secondary">
                <i class="fas fa-broom me-2"></i>
                显示已清空，点击刷新重新加载日志
            </div>
        </div>
    `;
}

// 加载日志统计信息
async function loadLogStats() {
    try {
        const response = await fetch('/api/log_stats');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        displayLogStats(data);
        
    } catch (error) {
        console.error('加载日志统计失败:', error);
        document.getElementById('logStatsContent').innerHTML = `
            <div class="col-12">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    加载统计信息失败: ${error.message}
                </div>
            </div>
        `;
    }
}

// 显示日志统计信息
function displayLogStats(data) {
    const statsContent = document.getElementById('logStatsContent');
    const summary = data.summary || {};
    const logCounts = data.log_counts || {};
    
    let statsHtml = '';
    
    // 总体统计
    statsHtml += `
        <div class="col-md-3">
            <div class="card bg-primary text-white stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="card-title">总文件数</div>
                            <div class="h4">${summary.total_files || 0}</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="card-title">总大小</div>
                            <div class="h4">${summary.total_size_mb || 0} MB</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hdd fa-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 各类型日志统计
    const logTypeLabels = {
        'app': '应用日志',
        'error': '错误日志',
        'user_actions': '用户操作',
        'system': '系统日志'
    };
    
    const logTypeColors = {
        'app': 'bg-success',
        'error': 'bg-danger',
        'user_actions': 'bg-warning',
        'system': 'bg-secondary'
    };
    
    Object.entries(logCounts).forEach(([type, count]) => {
        const label = logTypeLabels[type] || type;
        const colorClass = logTypeColors[type] || 'bg-secondary';
        const textClass = type === 'user_actions' ? 'text-dark' : 'text-white';
        
        statsHtml += `
            <div class="col-md-3">
                <div class="card ${colorClass} ${textClass} stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="card-title">${label}</div>
                                <div class="h4">${count.toLocaleString()}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-list fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    // 文件详情表格
            if (summary.files && summary.files.length > 0) {
            statsHtml += `
                <div class="col-12 mt-2">
                    <h6 class="small mb-2">文件详情</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="small">文件名</th>
                                    <th class="small">大小 (MB)</th>
                                    <th class="small">修改时间</th>
                                    <th class="small">操作</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
        
        summary.files.forEach(file => {
            const fileName = file.name.replace(/^logs\//, '');
            const logType = getLogTypeFromFileName(file.name);
            
            statsHtml += `
                <tr>
                    <td class="small">
                        <i class="fas fa-file-alt me-1"></i>
                        ${fileName}
                    </td>
                    <td class="small">${file.size_mb}</td>
                    <td class="small">${file.modified}</td>
                    <td>
                        <button class="btn btn-xs btn-outline-primary" onclick="viewLogFile('${logType}')" style="font-size: 0.7rem; padding: 0.1rem 0.3rem;">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                    </td>
                </tr>
            `;
        });
        
        statsHtml += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }
    
    statsContent.innerHTML = statsHtml;
    document.getElementById('logStatsContainer').style.display = 'block';
}

// 从文件名获取日志类型
function getLogTypeFromFileName(fileName) {
    if (fileName.includes('aps_app.log')) return 'app';
    if (fileName.includes('aps_error.log')) return 'error';
    if (fileName.includes('user_actions.log')) return 'user_actions';
    if (fileName.includes('aps_system.log')) return 'system';
    return 'app';
}

// 查看特定类型的日志
function viewLogFile(logType) {
    document.getElementById('logType').value = logType;
    loadLogs(1);
    hideLogStats();
}

// 隐藏统计信息
function hideLogStats() {
    document.getElementById('logStatsContainer').style.display = 'none';
}

// 清理日志
async function cleanupLogs(days) {
    if (!confirm(`确定要清理${days}天前的日志文件吗？此操作无法恢复！`)) {
        return;
    }
    
    try {
        const response = await fetch('/api/cleanup_logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ days: days })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert(data.message);
            // 刷新统计信息
            loadLogStats();
            // 刷新当前日志显示
            loadLogs(currentPage);
        } else {
            alert('清理失败: ' + (data.error || '未知错误'));
        }
        
    } catch (error) {
        console.error('清理日志失败:', error);
        alert('清理失败: ' + error.message);
    }
}

// 显示自定义清理对话框
function showCustomCleanup() {
    const modal = new bootstrap.Modal(document.getElementById('customCleanupModal'));
    modal.show();
}

// 执行自定义清理
async function executeCustomCleanup() {
    const days = parseInt(document.getElementById('cleanupDays').value);
    
    if (isNaN(days) || days < 1 || days > 365) {
        alert('请输入有效的天数 (1-365)');
        return;
    }
    
    // 关闭对话框
    const modal = bootstrap.Modal.getInstance(document.getElementById('customCleanupModal'));
    modal.hide();
    
    // 执行清理
    await cleanupLogs(days);
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}

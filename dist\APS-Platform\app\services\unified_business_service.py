# -*- coding: utf-8 -*-
"""
统一业务服务

将现有的业务逻辑迁移到使用统一数据模型，
提供向后兼容的API接口，确保零风险迁移。

Author: AI Assistant
Date: 2025-01-14
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from flask import current_app
from sqlalchemy import text, and_, or_, desc, asc

from app import db
from app.models.unified.unified_lot_model import UnifiedLotModel
from app.models.unified.unified_test_spec import UnifiedTestSpec
from app.models import ProductPriorityConfig, ET_UPH_EQP
from app.services.priority_matching_service import PriorityMatchingService

logger = logging.getLogger(__name__)


class UnifiedLotService:
    """统一批次服务 - 替代原有的ET_WAIT_LOT、WIP_LOT、LOT_WIP查询逻辑"""
    
    def __init__(self):
        self.priority_matching_service = PriorityMatchingService()
    
    def get_wait_lot_data(self, filters: Dict = None, limit: int = None) -> List[Dict]:
        """
        获取待排产批次数据 - 替代原有的_get_wait_lot_data函数
        
        Args:
            filters: 过滤条件
            limit: 限制数量
            
        Returns:
            List[Dict]: 待排产批次数据列表
        """
        try:
            logger.info("🔄 使用统一模型获取待排产批次数据...")
            
            # 构建查询
            query = UnifiedLotModel.query
            
            # 基础过滤条件 - 只获取待排产的批次
            query = query.filter(
                UnifiedLotModel.DEVICE.isnot(None),
                UnifiedLotModel.STAGE.isnot(None),
                UnifiedLotModel.GOOD_QTY > 0,
                UnifiedLotModel.WIP_STATE.in_(['WAIT', 'READY', 'PENDING'])
            )
            
            # 应用额外过滤条件
            if filters:
                if 'device' in filters and filters['device']:
                    query = query.filter(UnifiedLotModel.DEVICE == filters['device'])
                
                if 'stage' in filters and filters['stage']:
                    query = query.filter(UnifiedLotModel.STAGE == filters['stage'])
                
                if 'wip_state' in filters and filters['wip_state']:
                    query = query.filter(UnifiedLotModel.WIP_STATE == filters['wip_state'])
                
                if 'priority_level' in filters and filters['priority_level']:
                    query = query.filter(UnifiedLotModel.PRIORITY_LEVEL == filters['priority_level'])
                
                if 'min_qty' in filters and filters['min_qty']:
                    query = query.filter(UnifiedLotModel.GOOD_QTY >= filters['min_qty'])
                
                if 'max_qty' in filters and filters['max_qty']:
                    query = query.filter(UnifiedLotModel.GOOD_QTY <= filters['max_qty'])
            
            # 排序：优先级 -> 计划交期 -> 创建时间
            query = query.order_by(
                asc(UnifiedLotModel.PRIORITY_ORDER),
                asc(UnifiedLotModel.PLAN_DUE_DATE),
                asc(UnifiedLotModel.created_at)
            )
            
            # 限制数量
            if limit:
                query = query.limit(limit)
            
            # 执行查询
            unified_lots = query.all()
            
            # 转换为兼容格式
            wait_lots = []
            for lot in unified_lots:
                wait_lot = {
                    'LOT_ID': lot.LOT_ID,
                    'DEVICE': lot.DEVICE,
                    'STAGE': lot.STAGE,
                    'GOOD_QTY': lot.GOOD_QTY or 0,
                    'PKG_PN': lot.PKG_PN,
                    'CHIP_ID': lot.CHIP_ID,
                    'LOT_TYPE': lot.LOT_TYPE,
                    'WIP_STATE': lot.WIP_STATE,
                    'PROC_STATE': lot.PROC_STATE,
                    'HOLD_STATE': lot.HOLD_STATE,
                    'RELEASE_TIME': lot.RELEASE_TIME,
                    'CREATE_TIME': lot.CREATE_TIME,
                    'PROD_ID': lot.PROD_ID,
                    'PO_ID': lot.PO_ID,
                    'FLOW_ID': lot.FLOW_ID,
                    'FLOW_VER': lot.FLOW_VER,
                    'FAC_ID': lot.FAC_ID,
                    # 新增统一模型的字段
                    'PRIORITY_LEVEL': lot.PRIORITY_LEVEL,
                    'PRIORITY_ORDER': lot.PRIORITY_ORDER,
                    'PLAN_DUE_DATE': lot.PLAN_DUE_DATE,
                    'HANDLER_ID': lot.HANDLER_ID,
                    'TESTER_ID': lot.TESTER_ID,
                    'UPH_OVERRIDE': lot.UPH_OVERRIDE,
                    # 扩展数据
                    'extended_data': lot.get_extended_data()
                }
                wait_lots.append(wait_lot)
            
            logger.info(f"✅ 获取到 {len(wait_lots)} 条待排产批次数据（使用统一模型）")
            return wait_lots
            
        except Exception as e:
            logger.error(f"❌ 获取待排产批次数据失败: {e}")
            # 降级到传统方法
            return self._fallback_get_wait_lot_data(filters, limit)
    
    def _fallback_get_wait_lot_data(self, filters: Dict = None, limit: int = None) -> List[Dict]:
        """降级方法：使用传统ET_WAIT_LOT表查询"""
        try:
            logger.warning("🔄 降级使用传统ET_WAIT_LOT表查询...")
            
            # 构建SQL查询
            sql_parts = ["""
                SELECT 
                    LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                    LOT_TYPE, WIP_STATE, PROC_STATE, HOLD_STATE,
                    RELEASE_TIME, CREATE_TIME, PROD_ID, PO_ID,
                    FLOW_ID, FLOW_VER, FAC_ID
                FROM `v_et_wait_lot_unified` 
                WHERE DEVICE IS NOT NULL 
                    AND STAGE IS NOT NULL 
                    AND GOOD_QTY > 0
            """]
            
            # 应用过滤条件
            params = {}
            if filters:
                if 'device' in filters and filters['device']:
                    sql_parts.append("AND DEVICE = :device")
                    params['device'] = filters['device']
                
                if 'stage' in filters and filters['stage']:
                    sql_parts.append("AND STAGE = :stage")
                    params['stage'] = filters['stage']
                
                if 'wip_state' in filters and filters['wip_state']:
                    sql_parts.append("AND WIP_STATE = :wip_state")
                    params['wip_state'] = filters['wip_state']
                
                if 'min_qty' in filters and filters['min_qty']:
                    sql_parts.append("AND GOOD_QTY >= :min_qty")
                    params['min_qty'] = filters['min_qty']
                
                if 'max_qty' in filters and filters['max_qty']:
                    sql_parts.append("AND GOOD_QTY <= :max_qty")
                    params['max_qty'] = filters['max_qty']
            
            sql_parts.append("ORDER BY CREATE_TIME ASC")
            
            if limit:
                sql_parts.append(f"LIMIT {limit}")
            
            query = text(" ".join(sql_parts))
            result = db.session.execute(query, params)
            rows = result.fetchall()
            
            wait_lots = []
            for row in rows:
                wait_lot = {
                    'LOT_ID': row[0],
                    'DEVICE': row[1],
                    'STAGE': row[2],
                    'GOOD_QTY': row[3] or 0,
                    'PKG_PN': row[4],
                    'CHIP_ID': row[5],
                    'LOT_TYPE': row[6],
                    'WIP_STATE': row[7],
                    'PROC_STATE': row[8],
                    'HOLD_STATE': row[9],
                    'RELEASE_TIME': row[10],
                    'CREATE_TIME': row[11],
                    'PROD_ID': row[12],
                    'PO_ID': row[13],
                    'FLOW_ID': row[14],
                    'FLOW_VER': row[15],
                    'FAC_ID': row[16]
                }
                wait_lots.append(wait_lot)
            
            logger.info(f"✅ 降级查询获取到 {len(wait_lots)} 条待排产批次数据")
            return wait_lots
            
        except Exception as e:
            logger.error(f"❌ 降级查询也失败: {e}")
            return []
    
    def get_wip_lot_data(self, filters: Dict = None, limit: int = None) -> List[Dict]:
        """
        获取在制品清单数据
        
        Args:
            filters: 过滤条件
            limit: 限制数量
            
        Returns:
            List[Dict]: 在制品清单数据列表
        """
        try:
            logger.info("🔄 使用统一模型获取在制品清单数据...")
            
            # 构建查询 - 获取来自WIP_LOT的数据
            query = UnifiedLotModel.query.filter(
                UnifiedLotModel.source_table == 'v_wip_lot_unified'
            )
            
            # 应用过滤条件
            if filters:
                if 'device' in filters and filters['device']:
                    query = query.filter(UnifiedLotModel.DEVICE == filters['device'])
                
                if 'stage' in filters and filters['stage']:
                    query = query.filter(UnifiedLotModel.STAGE == filters['stage'])
                
                if 'wip_state' in filters and filters['wip_state']:
                    query = query.filter(UnifiedLotModel.WIP_STATE == filters['wip_state'])
            
            # 排序
            query = query.order_by(desc(UnifiedLotModel.created_at))
            
            # 限制数量
            if limit:
                query = query.limit(limit)
            
            # 执行查询
            unified_lots = query.all()
            
            # 转换为兼容格式
            wip_lots = []
            for lot in unified_lots:
                wip_lot = lot.to_dict()
                # 添加扩展数据
                extended_data = lot.get_extended_data()
                wip_lot.update(extended_data)
                wip_lots.append(wip_lot)
            
            logger.info(f"✅ 获取到 {len(wip_lots)} 条在制品清单数据（使用统一模型）")
            return wip_lots
            
        except Exception as e:
            logger.error(f"❌ 获取在制品清单数据失败: {e}")
            return []
    
    def get_lot_by_id(self, lot_id: str) -> Optional[Dict]:
        """
        根据批次号获取批次信息
        
        Args:
            lot_id: 批次号
            
        Returns:
            Optional[Dict]: 批次信息
        """
        try:
            unified_lot = UnifiedLotModel.find_by_lot_id(lot_id)
            if unified_lot:
                lot_data = unified_lot.to_dict()
                lot_data['extended_data'] = unified_lot.get_extended_data()
                return lot_data
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取批次 {lot_id} 信息失败: {e}")
            return None
    
    def update_lot_priority(self, lot_id: str, priority_level: str, priority_order: int = None) -> bool:
        """
        更新批次优先级
        
        Args:
            lot_id: 批次号
            priority_level: 优先级等级
            priority_order: 优先级顺序
            
        Returns:
            bool: 更新是否成功
        """
        try:
            unified_lot = UnifiedLotModel.find_by_lot_id(lot_id)
            if unified_lot:
                unified_lot.PRIORITY_LEVEL = priority_level
                if priority_order is not None:
                    unified_lot.PRIORITY_ORDER = priority_order
                unified_lot.updated_at = datetime.now()
                
                db.session.commit()
                logger.info(f"✅ 更新批次 {lot_id} 优先级为 {priority_level}")
                return True
            else:
                logger.warning(f"⚠️ 未找到批次 {lot_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 更新批次 {lot_id} 优先级失败: {e}")
            db.session.rollback()
            return False
    
    def assign_equipment(self, lot_id: str, handler_id: str, tester_id: str) -> bool:
        """
        分配设备给批次
        
        Args:
            lot_id: 批次号
            handler_id: 分拣机ID
            tester_id: 测试机ID
            
        Returns:
            bool: 分配是否成功
        """
        try:
            unified_lot = UnifiedLotModel.find_by_lot_id(lot_id)
            if unified_lot:
                unified_lot.HANDLER_ID = handler_id
                unified_lot.TESTER_ID = tester_id
                unified_lot.updated_at = datetime.now()
                
                db.session.commit()
                logger.info(f"✅ 为批次 {lot_id} 分配设备: {handler_id}-{tester_id}")
                return True
            else:
                logger.warning(f"⚠️ 未找到批次 {lot_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 为批次 {lot_id} 分配设备失败: {e}")
            db.session.rollback()
            return False


class UnifiedSchedulingService:
    """统一排产服务 - 替代原有的排产算法逻辑"""
    
    def __init__(self):
        self.lot_service = UnifiedLotService()
        self.priority_matching_service = PriorityMatchingService()
    
    def run_scheduling(self, algorithm: str = 'intelligent', constraints: Dict = None) -> Dict[str, Any]:
        """运行排产算法 - 兼容接口"""
        try:
            filters = constraints.get('filters', {}) if constraints else {}
            optimization_target = constraints.get('optimization_target', 'efficiency') if constraints else 'efficiency'
            
            schedule_result = self.execute_scheduling_algorithm(
                algorithm=algorithm,
                optimization_target=optimization_target,
                filters=filters
            )
            
            return {
                'success': True,
                'algorithm': algorithm,
                'total_lots': len(schedule_result),
                'schedule_data': schedule_result,
                'message': f'排产算法 {algorithm} 执行成功'
            }
            
        except Exception as e:
            logger.error(f"❌ 排产算法执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '排产算法执行失败'
            }
    
    def execute_scheduling_algorithm(self, algorithm: str = 'intelligent', 
                                   optimization_target: str = 'efficiency',
                                   filters: Dict = None) -> List[Dict]:
        """
        执行排产算法 - 替代原有的_execute_scheduling_algorithm函数
        
        Args:
            algorithm: 算法类型 ('deadline', 'product', 'value', 'intelligent')
            optimization_target: 优化目标
            filters: 过滤条件
            
        Returns:
            List[Dict]: 排产结果
        """
        try:
            logger.info(f"🔄 执行统一排产算法: {algorithm}, 优化目标: {optimization_target}")
            
            # 1. 获取待排产批次
            wait_lots = self.lot_service.get_wait_lot_data(filters)
            if not wait_lots:
                logger.warning("⚠️ 没有待排产批次")
                return []
            
            # 2. 获取优先级配置
            priority_configs = self._get_priority_configs_dict()
            
            # 3. 为每个批次计算排产优先级分数
            scored_lots = []
            for lot in wait_lots:
                score = self._calculate_priority_score(lot, priority_configs, algorithm)
                scored_lots.append({
                    **lot,
                    'priority_score': score,
                    'algorithm': algorithm
                })
            
            # 4. 根据算法策略排序
            scored_lots = self._sort_lots_by_algorithm(scored_lots, algorithm)
            
            # 5. 获取可用设备信息
            available_equipment = self._get_available_equipment()
            
            # 6. 生成排产序列
            schedule_result = self._generate_schedule_sequence(
                scored_lots, available_equipment, algorithm, optimization_target
            )
            
            logger.info(f"✅ 排产算法 {algorithm} 完成，生成 {len(schedule_result)} 条排产记录")
            return schedule_result
            
        except Exception as e:
            logger.error(f"❌ 排产算法执行失败: {e}")
            return []
    
    def _get_priority_configs_dict(self) -> Dict:
        """获取优先级配置字典"""
        try:
            configs = ProductPriorityConfig.query.all()
            priority_dict = {}
            
            for config in configs:
                device = config.device or 'default'
                stage = config.stage or 'default'
                
                if device not in priority_dict:
                    priority_dict[device] = {}
                
                priority_dict[device][stage] = {
                    'priority_level': config.priority_level,
                    'priority_order': config.priority_order,
                    'uph_override': config.uph_override,
                    'config_id': config.id
                }
            
            return priority_dict
            
        except Exception as e:
            logger.error(f"❌ 获取优先级配置失败: {e}")
            return {}
    
    def _calculate_priority_score(self, lot: Dict, priority_configs: Dict, algorithm: str) -> float:
        """计算批次优先级分数"""
        try:
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            
            # 获取优先级配置
            priority_info = priority_configs.get(device, {}).get(stage, {})
            base_score = priority_info.get('priority_order', 500)
            
            # 根据算法调整分数
            if algorithm == 'deadline':
                # 交期优先：考虑计划交期
                if lot.get('PLAN_DUE_DATE'):
                    # 这里需要根据实际日期格式处理
                    pass
                return base_score
                
            elif algorithm == 'product':
                # 产品优先：按产品类型分组
                return base_score
                
            elif algorithm == 'value':
                # 产值优先：考虑数量和价值
                qty_factor = min(lot.get('GOOD_QTY', 0) / 1000, 10)  # 数量因子
                return base_score - qty_factor
                
            else:  # intelligent
                # 智能策略：综合考虑多个因素
                score = base_score
                
                # 数量因子
                qty = lot.get('GOOD_QTY', 0)
                if qty > 5000:
                    score -= 10  # 大批次优先
                elif qty < 1000:
                    score += 5   # 小批次稍微降低优先级
                
                # UPH因子
                if lot.get('UPH_OVERRIDE'):
                    uph = lot['UPH_OVERRIDE']
                    if uph > 2000:
                        score -= 5  # 高产能优先
                
                return score
                
        except Exception as e:
            logger.error(f"❌ 计算优先级分数失败: {e}")
            return 500  # 默认分数
    
    def _sort_lots_by_algorithm(self, scored_lots: List[Dict], algorithm: str) -> List[Dict]:
        """根据算法策略排序批次"""
        try:
            if algorithm == 'deadline':
                # 交期优先：优先级高的先排，数量少的先排
                scored_lots.sort(key=lambda x: (
                    x['priority_score'],      # 优先级分数低的先排
                    x['GOOD_QTY']            # 数量少的先排
                ))
            elif algorithm == 'product':
                # 产品优先：按产品类型和优先级排序
                scored_lots.sort(key=lambda x: (
                    x.get('DEVICE', ''),     # 按产品分组
                    x['priority_score'],     # 优先级分数低的先排
                    x.get('STAGE', '')       # 按工序排序
                ))
            elif algorithm == 'value':
                # 产值优先：优先级高且数量大的先排
                scored_lots.sort(key=lambda x: (
                    x['priority_score'],     # 优先级分数低的先排
                    -x['GOOD_QTY']          # 数量大的先排
                ))
            else:  # intelligent
                # 智能策略：综合考虑优先级、数量、创建时间
                scored_lots.sort(key=lambda x: (
                    x['priority_score'],     # 优先级分数
                    x['GOOD_QTY'] / 1000,    # 数量权重（降低影响）
                    x.get('CREATE_TIME', '') or ''  # 创建时间
                ))
            
            return scored_lots
            
        except Exception as e:
            logger.error(f"❌ 批次排序失败: {e}")
            return scored_lots
    
    def _get_available_equipment(self) -> List[Dict]:
        """获取可用设备信息"""
        try:
            # 从ET_UPH_EQP表获取设备信息
            equipment_list = []
            
            uph_records = ET_UPH_EQP.query.filter(
                ET_UPH_EQP.UPH > 0
            ).all()
            
            for record in uph_records:
                equipment = {
                    'TESTER_ID': record.TESTER_ID,
                    'HANDLER_ID': record.HANDLER_ID,
                    'DEVICE': record.DEVICE,
                    'STAGE': record.STAGE,
                    'PKG_PN': record.PKG_PN,
                    'UPH': record.UPH,
                    'SORTER_MODEL': getattr(record, 'SORTER_MODEL', ''),
                    'status': 'available'  # 假设都可用，实际应该查询设备状态
                }
                equipment_list.append(equipment)
            
            logger.info(f"✅ 获取到 {len(equipment_list)} 台可用设备")
            return equipment_list
            
        except Exception as e:
            logger.error(f"❌ 获取可用设备失败: {e}")
            return []
    
    def _generate_schedule_sequence(self, scored_lots: List[Dict], 
                                  available_equipment: List[Dict],
                                  algorithm: str, optimization_target: str) -> List[Dict]:
        """生成排产序列"""
        try:
            schedule_result = []
            equipment_workload = {}  # 记录每台设备的工作负载
            
            for index, lot in enumerate(scored_lots):
                # 智能设备匹配
                best_equipment = self._find_best_equipment(
                    lot, available_equipment, equipment_workload
                )
                
                # 更新设备工作负载
                if best_equipment:
                    equipment_id = f"{best_equipment['TESTER_ID']}-{best_equipment['HANDLER_ID']}"
                    if equipment_id not in equipment_workload:
                        equipment_workload[equipment_id] = 0
                    
                    # 根据UPH计算预计加工时间
                    estimated_hours = lot['GOOD_QTY'] / max(best_equipment.get('UPH', 1000), 1)
                    equipment_workload[equipment_id] += estimated_hours
                
                # 创建排产记录
                schedule_item = {
                    'SN': index + 1,
                    'LOT_ID': lot['LOT_ID'],
                    'HANDLER_ID': best_equipment.get('HANDLER_ID', f"H{(index % 10) + 1:02d}") if best_equipment else f"H{(index % 10) + 1:02d}",
                    'TESTER_ID': best_equipment.get('TESTER_ID', f"T{(index % 5) + 1:02d}") if best_equipment else f"T{(index % 5) + 1:02d}",
                    'DEVICE': lot['DEVICE'],
                    'PKG_PN': lot['PKG_PN'],
                    'CHIP_ID': lot['CHIP_ID'],
                    'STAGE': lot['STAGE'],
                    'GOOD_QTY': lot['GOOD_QTY'],
                    'Priority': lot.get('PRIORITY_LEVEL', 'medium'),
                    'priority_score': lot['priority_score'],
                    'algorithm': algorithm,
                    'optimization_target': optimization_target,
                    # 智能匹配信息
                    'UPH': best_equipment.get('UPH', 0) if best_equipment else 0,
                    'estimated_hours': round(lot['GOOD_QTY'] / max(best_equipment.get('UPH', 1000), 1), 2) if best_equipment else 0,
                    'match_reason': best_equipment.get('match_reason', '默认分配') if best_equipment else '默认分配',
                    'equipment_efficiency': best_equipment.get('efficiency_score', 0) if best_equipment else 0
                }
                schedule_result.append(schedule_item)
            
            return schedule_result
            
        except Exception as e:
            logger.error(f"❌ 生成排产序列失败: {e}")
            return []
    
    def _find_best_equipment(self, lot: Dict, available_equipment: List[Dict], 
                           equipment_workload: Dict) -> Optional[Dict]:
        """为批次找到最佳匹配的设备"""
        try:
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            pkg_pn = lot.get('PKG_PN', '')
            chip_id = lot.get('CHIP_ID', '')
            good_qty = lot.get('GOOD_QTY', 0)
            
            # 候选设备列表
            candidates = []
            
            for equipment in available_equipment:
                if equipment.get('status') != 'available':
                    continue
                
                # 计算匹配分数
                match_score = 0
                match_reasons = []
                
                # 1. 产品型号完全匹配 (权重: 40分)
                if equipment.get('DEVICE') == device and device:
                    match_score += 40
                    match_reasons.append("产品型号匹配")
                
                # 2. 工序匹配 (权重: 30分)
                if equipment.get('STAGE') == stage and stage:
                    match_score += 30
                    match_reasons.append("工序匹配")
                
                # 3. 封装料号匹配 (权重: 15分)
                if equipment.get('PKG_PN') == pkg_pn and pkg_pn:
                    match_score += 15
                    match_reasons.append("封装料号匹配")
                
                # 4. 分选机型号匹配 (权重: 10分)
                sorter_model = equipment.get('SORTER_MODEL', '')
                if sorter_model and 'PnP' in sorter_model:
                    match_score += 10
                    match_reasons.append("分选机型号匹配")
                
                # 5. UPH效率评分 (权重: 20分)
                uph = equipment.get('UPH', 1000)
                if good_qty > 0:
                    processing_time = good_qty / uph
                    if processing_time <= 8:  # 8小时内完成
                        match_score += 20
                    elif processing_time <= 24:  # 24小时内完成
                        match_score += 15
                    else:
                        match_score += 10
                    match_reasons.append(f"UPH效率({uph})")
                
                # 6. 设备负载评分 (权重: 10分)
                equipment_id = f"{equipment['TESTER_ID']}-{equipment['HANDLER_ID']}"
                current_load = equipment_workload.get(equipment_id, 0)
                if current_load < 8:  # 负载小于8小时
                    match_score += 10
                elif current_load < 16:  # 负载小于16小时
                    match_score += 5
                match_reasons.append(f"负载({current_load:.1f}h)")
                
                candidates.append({
                    **equipment,
                    'match_score': match_score,
                    'efficiency_score': match_score,
                    'match_reason': "; ".join(match_reasons) if match_reasons else "通用设备",
                    'processing_time': good_qty / uph if uph > 0 else 0
                })
            
            # 按匹配分数排序，选择最佳设备
            if candidates:
                candidates.sort(key=lambda x: (-x['match_score'], x.get('current_workload', 0)))
                best_equipment = candidates[0]
                
                logger.debug(f"为批次 {lot['LOT_ID']} 选择设备: {best_equipment['TESTER_ID']}-{best_equipment['HANDLER_ID']}, "
                           f"匹配分数: {best_equipment['match_score']}, 原因: {best_equipment['match_reason']}")
                
                return best_equipment
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 设备匹配失败: {e}")
            return None


class UnifiedTestSpecService:
    """统一测试规范服务"""
    
    def get_test_specs(self, filters: Dict = None, limit: int = None) -> List[Dict]:
        """
        获取测试规范数据
        
        Args:
            filters: 过滤条件
            limit: 限制数量
            
        Returns:
            List[Dict]: 测试规范数据列表
        """
        try:
            logger.info("🔄 使用统一模型获取测试规范数据...")
            
            # 构建查询
            query = UnifiedTestSpec.query
            
            # 应用过滤条件
            if filters:
                if 'device' in filters and filters['device']:
                    query = query.filter(UnifiedTestSpec.DEVICE == filters['device'])
                
                if 'stage' in filters and filters['stage']:
                    query = query.filter(UnifiedTestSpec.STAGE == filters['stage'])
                
                if 'pkg_pn' in filters and filters['pkg_pn']:
                    query = query.filter(UnifiedTestSpec.PKG_PN == filters['pkg_pn'])
            
            # 排序
            query = query.order_by(desc(UnifiedTestSpec.updated_at))
            
            # 限制数量
            if limit:
                query = query.limit(limit)
            
            # 执行查询
            test_specs = query.all()
            
            # 转换为字典格式
            spec_list = []
            for spec in test_specs:
                spec_dict = spec.to_dict()
                spec_dict['extended_data'] = spec.get_extended_data()
                spec_list.append(spec_dict)
            
            logger.info(f"✅ 获取到 {len(spec_list)} 条测试规范数据（使用统一模型）")
            return spec_list
            
        except Exception as e:
            logger.error(f"❌ 获取测试规范数据失败: {e}")
            return []
    
    def get_test_spec_by_id(self, spec_id: str) -> Optional[Dict]:
        """
        根据规范ID获取测试规范
        
        Args:
            spec_id: 规范ID
            
        Returns:
            Optional[Dict]: 测试规范信息
        """
        try:
            test_spec = UnifiedTestSpec.query.filter_by(TEST_SPEC_ID=spec_id).first()
            if test_spec:
                spec_dict = test_spec.to_dict()
                spec_dict['extended_data'] = test_spec.get_extended_data()
                return spec_dict
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取测试规范 {spec_id} 失败: {e}")
            return None


# 全局服务实例
unified_lot_service = UnifiedLotService()
unified_scheduling_service = UnifiedSchedulingService()
unified_test_spec_service = UnifiedTestSpecService()
# -*- coding: utf-8 -*-
"""
传统模型兼容代理

提供传统模型类的兼容代理，将查询操作透明地重定向到统一模型，
确保现有代码无需修改即可使用新的统一数据模型。

设计模式：代理模式 (Proxy Pattern)
- 为统一模型提供传统接口的代理
- 透明地处理查询重定向和数据转换
- 保持完整的向后兼容性

Author: AI Assistant
Date: 2025-01-14
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from flask import current_app
from sqlalchemy import and_, or_, desc, asc, text
from sqlalchemy.orm import Query

from app import db
from app.models.unified.unified_lot_model import UnifiedLotModel
from app.models.unified.unified_test_spec import UnifiedTestSpec

logger = logging.getLogger(__name__)


class BaseModelProxy:
    """模型代理基类"""
    
    def __init__(self, unified_model_class, source_table_name):
        self.unified_model_class = unified_model_class
        self.source_table_name = source_table_name
        self._query_cache = {}
    
    def _log_proxy_usage(self, method_name, args=None):
        """记录代理使用情况，用于监控和调试"""
        if current_app.config.get('DEBUG_PROXY_USAGE', False):
            logger.debug(f"代理调用: {self.__class__.__name__}.{method_name}, args={args}")
    
    def _convert_unified_to_legacy_format(self, unified_record):
        """将统一模型记录转换为传统格式"""
        if not unified_record:
            return None
        
        # 创建一个动态对象，模拟传统模型的属性
        legacy_obj = type('LegacyRecord', (), {})()
        
        # 复制所有属性
        for column in unified_record.__table__.columns:
            setattr(legacy_obj, column.name, getattr(unified_record, column.name))
        
        # 添加扩展数据中的字段
        extended_data = unified_record.get_extended_data()
        for key, value in extended_data.items():
            setattr(legacy_obj, key, value)
        
        return legacy_obj
    
    def _build_unified_query_filter(self, **kwargs):
        """构建统一模型的查询过滤器"""
        filters = [self.unified_model_class.source_table == self.source_table_name]
        
        for key, value in kwargs.items():
            if hasattr(self.unified_model_class, key):
                column = getattr(self.unified_model_class, key)
                if isinstance(value, list):
                    filters.append(column.in_(value))
                else:
                    filters.append(column == value)
        
        return and_(*filters)


class QueryProxy:
    """查询代理类，模拟SQLAlchemy Query接口"""
    
    def __init__(self, unified_model_class, source_table_name, base_filter=None):
        self.unified_model_class = unified_model_class
        self.source_table_name = source_table_name
        self.base_filter = base_filter or []
        self.additional_filters = []
        self.order_clauses = []
        self.limit_value = None
        self.offset_value = None
    
    def filter(self, *criterion):
        """添加过滤条件"""
        new_proxy = QueryProxy(self.unified_model_class, self.source_table_name, self.base_filter)
        new_proxy.additional_filters = self.additional_filters + list(criterion)
        new_proxy.order_clauses = self.order_clauses.copy()
        new_proxy.limit_value = self.limit_value
        new_proxy.offset_value = self.offset_value
        return new_proxy
    
    def filter_by(self, **kwargs):
        """根据关键字参数过滤"""
        filters = []
        for key, value in kwargs.items():
            if hasattr(self.unified_model_class, key):
                column = getattr(self.unified_model_class, key)
                filters.append(column == value)
        
        new_proxy = QueryProxy(self.unified_model_class, self.source_table_name, self.base_filter)
        new_proxy.additional_filters = self.additional_filters + filters
        new_proxy.order_clauses = self.order_clauses.copy()
        new_proxy.limit_value = self.limit_value
        new_proxy.offset_value = self.offset_value
        return new_proxy
    
    def order_by(self, *criterion):
        """添加排序条件"""
        new_proxy = QueryProxy(self.unified_model_class, self.source_table_name, self.base_filter)
        new_proxy.additional_filters = self.additional_filters.copy()
        new_proxy.order_clauses = self.order_clauses + list(criterion)
        new_proxy.limit_value = self.limit_value
        new_proxy.offset_value = self.offset_value
        return new_proxy
    
    def limit(self, limit):
        """设置查询限制"""
        new_proxy = QueryProxy(self.unified_model_class, self.source_table_name, self.base_filter)
        new_proxy.additional_filters = self.additional_filters.copy()
        new_proxy.order_clauses = self.order_clauses.copy()
        new_proxy.limit_value = limit
        new_proxy.offset_value = self.offset_value
        return new_proxy
    
    def offset(self, offset):
        """设置查询偏移"""
        new_proxy = QueryProxy(self.unified_model_class, self.source_table_name, self.base_filter)
        new_proxy.additional_filters = self.additional_filters.copy()
        new_proxy.order_clauses = self.order_clauses.copy()
        new_proxy.limit_value = self.limit_value
        new_proxy.offset_value = offset
        return new_proxy
    
    def _build_query(self):
        """构建实际的查询"""
        query = self.unified_model_class.query
        
        # 添加基础过滤器（源表过滤）
        if self.source_table_name:
            query = query.filter(self.unified_model_class.source_table == self.source_table_name)
        
        # 添加基础过滤器
        for filter_condition in self.base_filter:
            query = query.filter(filter_condition)
        
        # 添加额外过滤器
        for filter_condition in self.additional_filters:
            query = query.filter(filter_condition)
        
        # 添加排序
        for order_clause in self.order_clauses:
            query = query.order_by(order_clause)
        
        # 添加限制和偏移
        if self.limit_value:
            query = query.limit(self.limit_value)
        if self.offset_value:
            query = query.offset(self.offset_value)
        
        return query
    
    def all(self):
        """获取所有结果"""
        query = self._build_query()
        results = query.all()
        return [self._convert_to_legacy_format(record) for record in results]
    
    def first(self):
        """获取第一个结果"""
        query = self._build_query()
        result = query.first()
        return self._convert_to_legacy_format(result) if result else None
    
    def count(self):
        """获取结果数量"""
        query = self._build_query()
        return query.count()
    
    def paginate(self, page=1, per_page=20, error_out=True):
        """分页查询"""
        query = self._build_query()
        pagination = query.paginate(page=page, per_page=per_page, error_out=error_out)
        
        # 转换分页结果
        pagination.items = [self._convert_to_legacy_format(record) for record in pagination.items]
        return pagination
    
    def get(self, id):
        """根据ID获取记录"""
        query = self._build_query()
        result = query.filter(self.unified_model_class.id == id).first()
        return self._convert_to_legacy_format(result) if result else None
    
    def get_or_404(self, id):
        """根据ID获取记录，不存在则抛出404"""
        result = self.get(id)
        if not result:
            from flask import abort
            abort(404)
        return result
    
    def _convert_to_legacy_format(self, unified_record):
        """将统一模型记录转换为传统格式"""
        if not unified_record:
            return None
        
        # 创建一个动态对象，模拟传统模型的属性
        legacy_obj = type('LegacyRecord', (), {})()
        
        # 复制所有属性
        for column in unified_record.__table__.columns:
            setattr(legacy_obj, column.name, getattr(unified_record, column.name))
        
        # 添加扩展数据中的字段
        extended_data = unified_record.get_extended_data()
        for key, value in extended_data.items():
            setattr(legacy_obj, key, value)
        
        # 添加传统模型的方法
        legacy_obj.__repr__ = lambda: f'<{self.source_table_name} {getattr(legacy_obj, "LOT_ID", getattr(legacy_obj, "id", "unknown"))}>'
        
        return legacy_obj


class ET_WAIT_LOT_Proxy(BaseModelProxy):
    """ET_WAIT_LOT模型兼容代理"""
    
    def __init__(self):
        super().__init__(UnifiedLotModel, 'v_et_wait_lot_unified')
    
    @property
    def query(self):
        """模拟SQLAlchemy的query属性"""
        self._log_proxy_usage('query')
        return QueryProxy(self.unified_model_class, self.source_table_name)
    
    @classmethod
    def get(cls, id):
        """根据ID获取记录"""
        proxy = cls()
        proxy._log_proxy_usage('get', {'id': id})
        
        unified_record = UnifiedLotModel.query.filter(
            and_(
                UnifiedLotModel.source_table == 'v_et_wait_lot_unified',
                UnifiedLotModel.id == id
            )
        ).first()
        
        return proxy._convert_unified_to_legacy_format(unified_record)
    
    @classmethod
    def get_or_404(cls, id):
        """根据ID获取记录，不存在则抛出404"""
        result = cls.get(id)
        if not result:
            from flask import abort
            abort(404)
        return result


class WIP_LOT_Proxy(BaseModelProxy):
    """WIP_LOT模型兼容代理"""
    
    def __init__(self):
        super().__init__(UnifiedLotModel, 'v_wip_lot_unified')
    
    @property
    def query(self):
        """模拟SQLAlchemy的query属性"""
        self._log_proxy_usage('query')
        return QueryProxy(self.unified_model_class, self.source_table_name)
    
    @classmethod
    def get(cls, id):
        """根据ID获取记录"""
        proxy = cls()
        proxy._log_proxy_usage('get', {'id': id})
        
        unified_record = UnifiedLotModel.query.filter(
            and_(
                UnifiedLotModel.source_table == 'v_wip_lot_unified',
                UnifiedLotModel.id == id
            )
        ).first()
        
        return proxy._convert_unified_to_legacy_format(unified_record)
    
    @classmethod
    def get_or_404(cls, id):
        """根据ID获取记录，不存在则抛出404"""
        result = cls.get(id)
        if not result:
            from flask import abort
            abort(404)
        return result


class LOT_WIP_Proxy(BaseModelProxy):
    """LOT_WIP模型兼容代理"""
    
    def __init__(self):
        super().__init__(UnifiedLotModel, 'LOT_WIP')
    
    @property
    def query(self):
        """模拟SQLAlchemy的query属性"""
        self._log_proxy_usage('query')
        return QueryProxy(self.unified_model_class, self.source_table_name)
    
    @classmethod
    def get(cls, id):
        """根据ID获取记录"""
        proxy = cls()
        proxy._log_proxy_usage('get', {'id': id})
        
        unified_record = UnifiedLotModel.query.filter(
            and_(
                UnifiedLotModel.source_table == 'LOT_WIP',
                UnifiedLotModel.id == id
            )
        ).first()
        
        return proxy._convert_unified_to_legacy_format(unified_record)
    
    @classmethod
    def get_or_404(cls, id):
        """根据ID获取记录，不存在则抛出404"""
        result = cls.get(id)
        if not result:
            from flask import abort
            abort(404)
        return result


class Test_Spec_Proxy(BaseModelProxy):
    """Test_Spec模型兼容代理"""
    
    def __init__(self):
        super().__init__(UnifiedTestSpec, 'Test_Spec')
    
    @property
    def query(self):
        """模拟SQLAlchemy的query属性"""
        self._log_proxy_usage('query')
        return QueryProxy(self.unified_model_class, self.source_table_name)
    
    @classmethod
    def get(cls, id):
        """根据ID获取记录"""
        proxy = cls()
        proxy._log_proxy_usage('get', {'id': id})
        
        unified_record = UnifiedTestSpec.query.filter(
            and_(
                UnifiedTestSpec.source_table == 'Test_Spec',
                UnifiedTestSpec.id == id
            )
        ).first()
        
        return proxy._convert_unified_to_legacy_format(unified_record)
    
    @classmethod
    def get_or_404(cls, id):
        """根据ID获取记录，不存在则抛出404"""
        result = cls.get(id)
        if not result:
            from flask import abort
            abort(404)
        return result
    
    def _convert_unified_to_legacy_format(self, unified_record):
        """将统一测试规范记录转换为传统格式"""
        if not unified_record:
            return None
        
        # 创建一个动态对象，模拟传统模型的属性
        legacy_obj = type('LegacyTestSpec', (), {})()
        
        # 复制所有属性
        for column in unified_record.__table__.columns:
            setattr(legacy_obj, column.name, getattr(unified_record, column.name))
        
        # 添加扩展数据中的字段
        extended_data = unified_record.get_extended_data()
        for key, value in extended_data.items():
            setattr(legacy_obj, key, value)
        
        # 添加传统模型的方法
        legacy_obj.__repr__ = lambda: f'<Test_Spec {getattr(legacy_obj, "TEST_SPEC_ID", getattr(legacy_obj, "id", "unknown"))}>'
        
        return legacy_obj


# 导出代理类，用于模块级别的访问
__all__ = [
    'ET_WAIT_LOT_Proxy',
    'WIP_LOT_Proxy', 
    'LOT_WIP_Proxy',
    'Test_Spec_Proxy',
    'BaseModelProxy',
    'QueryProxy'
] 
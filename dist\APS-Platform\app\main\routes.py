from flask import render_template, jsonify, abort, request
from flask_login import login_required, current_user
from app.main import bp
from app.models import MenuSetting, User, UserActionLog, AISettings, ProductPriorityConfig, UserFilterPresets, Settings, SchedulingTasks, DatabaseInfo, MigrationLog, SchedulingConfig
from app import db
from app.decorators import admin_required, permission_required
from flask import current_app
from app.config.menu_config import MENU_CONFIG, get_menu_by_id, get_all_menu_ids, MENU_CONFIG_VERSION, MENU_ID_MAP
from app.api.routes import get_db_connection
import time
import json
from datetime import datetime
import os

# 添加上下文处理器，使版本号可用于所有模板
@bp.context_processor
def inject_version():
    return {'app_version': current_app.config['APP_VERSION']}

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    return render_template('main/index.html')

@bp.route('/users')
@login_required
def user_management():
    if current_user.role != 'admin':
        abort(403)
    return render_template('auth/users.html')

@bp.route('/menu')
@login_required
def get_menu():
    """获取用户菜单 - 优化版本"""
    
    # 如果是管理员，直接使用完整菜单配置
    if current_user.role == 'admin':
        menu_tree = []
        for menu in MENU_CONFIG:
            menu_tree.append(build_menu_item(menu))
    else:
        # 批量获取用户权限，减少数据库查询
        user_permissions = set(current_user.get_permissions())
        
        # 构建菜单树
        menu_tree = []
        for menu in MENU_CONFIG:
            item_tree = build_menu_tree_with_permissions(menu, user_permissions)
            if item_tree:
                menu_tree.append(item_tree)
    
    # 构建响应
    response_data = {
        'version': MENU_CONFIG_VERSION,
        'menu': menu_tree,
        'user_role': current_user.role,
        'timestamp': int(time.time())
    }
    
    response = jsonify(response_data)
    
    # 设置缓存头 - 缓存5分钟
    response.headers["Cache-Control"] = "public, max-age=300"
    response.headers["ETag"] = f'"{MENU_CONFIG_VERSION}-{current_user.username}-{current_user.role}"'
    
    return response

def build_menu_item(menu_item):
    """构建单个菜单项（不检查权限）"""
    children = []
    for child in menu_item.get('children', []):
        children.append(build_menu_item(child))
    
    # 使用数字ID而不是code，保持与权限系统一致
    menu_id = MENU_ID_MAP.get(menu_item['code'])
    
    return {
        'id': menu_id,
        'name': menu_item['name'],
        'icon': menu_item['icon'],
        'route': menu_item['route'],
        'children': children
    }

def build_menu_tree_with_permissions(menu_item, user_permissions):
    """构建菜单树（带权限检查）"""
    menu_id = MENU_ID_MAP.get(menu_item['code'])
    
    # 检查用户是否有权限
    if menu_id and menu_id not in user_permissions:
        return None
    
    # 递归处理子菜单
    children = []
    for child in menu_item.get('children', []):
        child_tree = build_menu_tree_with_permissions(child, user_permissions)
        if child_tree:
            children.append(child_tree)
    
    # 构建返回的菜单项
    menu_tree = {
        'id': menu_id,
        'name': menu_item['name'],
        'icon': menu_item['icon'],
        'route': menu_item['route'],
        'children': children
    }
    
    # 修复菜单显示逻辑：
    # 1. 如果有子菜单，显示父菜单（即使父菜单没有route）
    # 2. 如果没有子菜单但有route，也显示
    # 3. 只有既没有子菜单又没有route的菜单才不显示
    if children or menu_item['route']:
        return menu_tree
    else:
        return None

@bp.route('/menu/settings', methods=['GET'])
@login_required
@permission_required(21)  # system_users (菜单设置属于用户管理的一部分)
def get_menu_settings():
    # 使用配置文件而不是从数据库获取
    result = []
    
    # 处理菜单项，添加ID映射
    def process_menu_item(item, parent_id=None):
        menu_id = MENU_ID_MAP.get(item['code'])
        menu_data = {
            'id': menu_id,
            'name': item['name'],
            'parent_id': parent_id,
            'is_visible': True,
            'order': item['order'],
            'icon': item['icon'],
            'route': item['route']
        }
        result.append(menu_data)
        
        # 递归处理子菜单
        for child in item.get('children', []):
            process_menu_item(child, menu_id)
    
    # 处理所有顶级菜单
    for menu in MENU_CONFIG:
        process_menu_item(menu)
    
    return jsonify(result)

# Production Management Routes
@bp.route('/production/semi-auto')
@login_required
@permission_required(7)  # production_semi_auto
def semi_auto_production():
    return render_template('production/semi_auto.html')

@bp.route('/production/auto')
@login_required
@permission_required(8)  # production_auto
def auto_production():
    """全自动排产页面"""
    return render_template('production/auto.html')

# API v1废弃路由已移除 - priority-matching现在由production_views处理
# @bp.route('/production/priority-matching')
# @login_required
# @permission_required(27)  # production_priority
# def priority_matching():
#     """智能优先级匹配页面"""
#     return render_template('production/priority_matching.html')

@bp.route('/production/algorithm')
@login_required
@permission_required(9)  # production_algorithm
def production_algorithm():
    """手动排产与批次优先级管理页面"""
    return render_template('production/algorithm.html')

# Order Management Routes
@bp.route('/orders/semi-auto')
@login_required
@permission_required(10)  # orders_semi_auto
def semi_auto_orders():
    return render_template('orders/orders_semi_auto.html')

@bp.route('/orders/auto')
@login_required
@permission_required(11)  # orders_auto
def auto_orders():
    return render_template('orders/orders_auto.html')

@bp.route('/orders/optimized-parser')
@login_required
@permission_required(36)  # orders_optimized_parser
def orders_optimized_parser():
    """优化Excel解析器页面 - 移到订单管理下"""
    return render_template('orders/summary_preview.html')

@bp.route('/orders/horizontal-info')
@login_required
@permission_required(37)  # orders_horizontal_info
def orders_horizontal_info():
    """横向信息管理页面 - 移到订单管理下"""
    return render_template('orders/summary_preview_detail.html')

# Resource Management Routes
@bp.route('/resources/eqp_status')
@login_required
@permission_required(14)  # resources_hardware
def hardware_resources():
    """设备资源管理 - eqp_status表"""
    return render_template('resources/eqp_status.html')

@bp.route('/resources/hardware')
@login_required
@permission_required(14)  # resources_hardware
def hardware_resources_alias():
    """设备资源管理 - hardware别名路由，指向eqp_status"""
    return render_template('resources/eqp_status.html')

@bp.route('/resources/specs')
@login_required
@permission_required(15)  # resources_test_specs
def test_specs():
    """测试规范管理 - et_ft_test_spec表"""
    return render_template('resources/et_ft_test_spec.html')

@bp.route('/resources/tester')
@login_required
@permission_required(16)  # resources_test_hardware
def test_hardware():
    """套件资源管理 - TCC_INV表"""
    return render_template('resources/TCC_INV.html')

@bp.route('/resources/uph')
@login_required
@permission_required(17)  # resources_uph
def uph_management():
    """UPH管理 - ET_UPH_EQP表"""
    return render_template('resources/ET_UPH_EQP.html')

@bp.route('/resources/product-cycle')
@login_required
@permission_required(18)  # resources_product_cycle
def product_cycle():
    """产品周期管理 - CT表"""
    return render_template('resources/CT.html')

# WIP Tracking Routes
@bp.route('/wip/by-product')
@login_required
@permission_required(19)  # wip_by_product
def wip_by_product():
    """按产品跟踪WIP"""
    return render_template('wip/by_product.html')

@bp.route('/wip/by-batch')
@login_required
@permission_required(20)  # wip_by_batch
def wip_by_batch():
    """按批次跟踪WIP"""
    return render_template('wip/by_batch.html')

# System Management Routes
@bp.route('/system/logs')
@login_required
@permission_required(23)  # system_logs
def system_logs():
    return render_template('main/system_logs.html')

@bp.route('/system/performance')
@login_required
@permission_required(28)  # system_performance
def performance_monitor():
    """系统性能监控页面"""
    return render_template('system/performance_monitor.html')

# System Settings Routes
@bp.route('/system/settings')
@login_required
@permission_required(29)  # system_data_source
def system_settings():
    """数据源管理页面"""
    return render_template('system/settings.html')



# User Logs API
@bp.route('/api/logs')
@login_required
@permission_required(23)  # system_logs
def get_logs():
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    username = request.args.get('username')
    action_type = request.args.get('action_type')
    target_model = request.args.get('target_model')
    
    # 打印调试信息
    current_app.logger.info(f"获取日志，参数: page={page}, per_page={per_page}, username={username}, action_type={action_type}, target_model={target_model}")
    
    query = UserActionLog.query
    
    if username:
        query = query.filter(UserActionLog.username == username)
    if action_type:
        query = query.filter(UserActionLog.action_type == action_type)
    if target_model:
        query = query.filter(UserActionLog.target_model == target_model)
    
    # 首先获取总数，确认是否有数据
    total_count = query.count()
    current_app.logger.info(f"查询到 {total_count} 条日志记录")
    
    # 应用分页
    logs = query.order_by(UserActionLog.created_at.desc()).paginate(page=page, per_page=per_page)
    
    # 构建响应
    response_data = {
        'total': logs.total,
        'pages': logs.pages,
        'current_page': logs.page,
        'per_page': logs.per_page,
        'logs': [{
            'id': log.id,
            'username': log.username,
            'action_type': log.action_type,
            'target_model': log.target_model,
            'target_id': log.target_id,
            'details': log.details,
            'ip_address': log.ip_address,
            'created_at': log.created_at.isoformat() if log.created_at else None
        } for log in logs.items]
    }
    
    current_app.logger.info(f"返回 {len(response_data['logs'])} 条日志记录")
    return jsonify(response_data)

# System Run Logs API - 更新支持新的日志系统
@bp.route('/api/system_logs')
@login_required
@permission_required(23)  # system_logs
def get_system_logs():
    
    log_type = request.args.get('log_type', 'app')  # app, error, user_actions, system
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    keyword = request.args.get('keyword', '')
    level = request.args.get('level', '')  # 日志级别过滤
    
    # 根据日志类型选择文件
    log_files = {
        'app': 'logs/aps_app.log',
        'error': 'logs/aps_error.log', 
        'user_actions': 'logs/user_actions.log',
        'system': 'aps_system.log'
    }
    
    log_file = log_files.get(log_type, 'logs/aps_app.log')
    
    try:
        # 检查文件是否存在
        if not os.path.exists(log_file):
            return jsonify({
                'logs': [],
                'total': 0,
                'pages': 0,
                'current_page': page,
                'per_page': per_page,
                'message': f'日志文件不存在: {log_file}'
            })
        
        # 读取日志文件
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            all_lines = f.readlines()
        
        # 解析和过滤日志
        parsed_logs = []
        for line_num, line in enumerate(all_lines, 1):
            if not line.strip():
                continue
                
            parsed_log = parse_log_line(line, log_type, line_num)
            if not parsed_log:
                continue
                
            # 应用过滤条件
            if keyword and keyword.lower() not in line.lower():
                continue
                
            if level and parsed_log.get('level', '').upper() != level.upper():
                continue
                
            parsed_logs.append(parsed_log)
        
        # 反转顺序，最新的日志在前面
        parsed_logs.reverse()
        
        # 分页
        total = len(parsed_logs)
        pages = (total + per_page - 1) // per_page
        start_idx = (page - 1) * per_page
        end_idx = min(start_idx + per_page, total)
        
        page_logs = parsed_logs[start_idx:end_idx]
        
        return jsonify({
            'logs': page_logs,
            'total': total,
            'pages': pages,
            'current_page': page,
            'per_page': per_page,
            'log_type': log_type,
            'log_file': log_file
        })
        
    except Exception as e:
        current_app.logger.error(f"读取日志文件失败: {log_file}, 错误: {e}")
        return jsonify({
            'error': f'读取日志失败: {str(e)}',
            'logs': [],
            'total': 0,
            'pages': 0,
            'current_page': page,
            'per_page': per_page
        }), 500

def parse_log_line(line, log_type, line_num):
    """解析日志行"""
    import re
    from datetime import datetime
    
    line = line.strip()
    if not line:
        return None
    
    try:
        if log_type == 'user_actions':
            # 用户操作日志格式: timestamp | username | action | resource | details
            parts = line.split(' | ')
            if len(parts) >= 4:
                return {
                    'timestamp': parts[0],
                    'level': 'INFO',
                    'message': f"用户: {parts[1]}, 操作: {parts[2]}, 资源: {parts[3]}",
                    'details': parts[4] if len(parts) > 4 else '',
                    'source': f'user_actions.log:{line_num}',
                    'raw': line
                }
        
        # 标准日志格式: timestamp | level | logger | message
        if ' | ' in line:
            parts = line.split(' | ', 3)
            if len(parts) >= 3:
                timestamp = parts[0]
                level = parts[1].strip()
                logger = parts[2].strip() if len(parts) > 2 else ''
                message = parts[3] if len(parts) > 3 else parts[2]
                
                return {
                    'timestamp': timestamp,
                    'level': level,
                    'logger': logger,
                    'message': message,
                    'source': f'{log_type}:{line_num}',
                    'raw': line
                }
        
        # 旧格式兼容: timestamp level: message [in file:line]
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[,\d]*)\s+(\w+):\s+(.+?)(?:\s+\[in\s+(.+?):(\d+)\])?$'
        match = re.match(pattern, line)
        
        if match:
            timestamp, level, message, file_path, line_no = match.groups()
            return {
                'timestamp': timestamp,
                'level': level,
                'message': message,
                'source': f'{file_path}:{line_no}' if file_path else f'{log_type}:{line_num}',
                'raw': line
            }
        
        # 如果无法解析，返回原始行
        return {
            'timestamp': '',
            'level': 'INFO',
            'message': line,
            'source': f'{log_type}:{line_num}',
            'raw': line
        }
        
    except Exception as e:
        # 解析失败，返回原始行
        return {
            'timestamp': '',
            'level': 'ERROR',
            'message': f'解析失败: {line}',
            'source': f'{log_type}:{line_num}',
            'raw': line
        }
    
    except Exception as e:
        return jsonify({
            'error': f"读取日志文件出错: {str(e)}"
        }), 500

# 日志统计API
@bp.route('/api/log_stats')
@login_required
@permission_required(23)  # system_logs
def get_log_stats():
    """获取日志统计信息"""
    try:
        from app.utils.simple_logging import get_log_summary
        summary = get_log_summary()
        
        # 获取各类型日志的条目数
        log_counts = {}
        log_files = {
            'app': 'logs/aps_app.log',
            'error': 'logs/aps_error.log',
            'user_actions': 'logs/user_actions.log',
            'system': 'aps_system.log'
        }
        
        for log_type, log_file in log_files.items():
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    log_counts[log_type] = len([line for line in lines if line.strip()])
                except:
                    log_counts[log_type] = 0
            else:
                log_counts[log_type] = 0
        
        return jsonify({
            'summary': summary,
            'log_counts': log_counts,
            'available_types': list(log_files.keys())
        })
        
    except Exception as e:
        current_app.logger.error(f"获取日志统计失败: {e}")
        return jsonify({'error': str(e)}), 500

# 日志清理API
@bp.route('/api/cleanup_logs', methods=['POST'])
@login_required
@permission_required(23)  # system_logs
def cleanup_logs():
    """清理旧日志文件"""
    try:
        data = request.get_json()
        days = data.get('days', 7)  # 默认清理7天前的日志
        
        from app.utils.simple_logging import cleanup_old_logs
        result = cleanup_old_logs(days)
        
        # 记录用户操作
        from app.models.unified.user_action_log import UserActionLog
        UserActionLog.log_action(
            username=current_user.username,
            action_type='cleanup',
            target_model='SystemLogs',
            details=f"清理{days}天前的日志文件，清理了{result.get('cleaned_files', 0)}个文件",
            request=request
        )
        
        return jsonify({
            'success': True,
            'message': f"成功清理了{result.get('cleaned_files', 0)}个日志文件",
            'result': result
        })
        
    except Exception as e:
        current_app.logger.error(f"清理日志失败: {e}")
        return jsonify({'error': str(e)}), 500

@login_required
def permissions():
    """权限管理页面"""
    if current_user.role != 'admin':
        abort(403)
    return render_template('auth/permissions.html')

# AI Assistant Routes
@bp.route('/ai-assistant')
@login_required
@permission_required(24)  # system_ai_assistant
def ai_assistant():
    """AI助手页面"""
    return render_template('main/ai_assistant.html')

# Add AI Assistant Menu Item
@admin_required
def add_ai_menu():
    """添加AI助手菜单项（已迁移到配置文件）"""
    # 检查菜单项是否存在于配置文件中
    ai_menu_exists = 'system_ai_assistant' in MENU_ID_MAP
    if ai_menu_exists:
        return jsonify({"message": "AI助手菜单项已存在于菜单配置中", "success": True})
    else:
        return jsonify({"message": "AI助手菜单项需要添加到app/config/menu_config.py文件中", "success": False})

@bp.route('/test-api')
@login_required
def test_api():
    """API测试页面，用于测试系统API接口"""
    return render_template('test_api.html')

@login_required
def test_simple():
    """简单API测试页面"""
    from flask import Response
    with open('test_simple_page.html', 'r', encoding='utf-8') as f:
        content = f.read()
    return Response(content, mimetype='text/html')

# Create Test Log Record
@bp.route('/create-test-log')
@login_required
@permission_required(23)  # system_logs (测试日志属于系统日志功能)
def create_test_log():
    """创建测试日志记录"""
    import json
    from datetime import datetime
        
    # 创建一条测试日志记录
    log = UserActionLog.log_action(
        username=current_user.username,
        action_type='create',
        target_model='TestLog',
        target_id='test-001',
        details=json.dumps({
            'message': '这是一条测试日志记录',
            'timestamp': datetime.utcnow().isoformat(),
            'test': True
        }),
        request=request
    )
    
    return jsonify({
        'success': True,
        'message': '测试日志记录已创建',
        'log_id': log.id if log else None
    })

@bp.route('/api/ai-settings', methods=['GET', 'POST'])
@admin_required
def handle_ai_settings():
    """处理AI设置获取和保存请求"""
    from flask import current_app, json
    import os
    
    # 处理POST请求 - 保存设置
    if request.method == 'POST':
        try:
            # 获取提交的设置
            settings = request.get_json()
            
            # 使用ORM模型保存到系统数据库
            ai_setting = db.session.query(AISettings).filter_by(id=1).first()
            
            # 序列化设置为JSON
            settings_json = json.dumps(settings, ensure_ascii=False)
            
            if ai_setting:
                # 更新现有设置
                ai_setting.settings = settings_json
                ai_setting.updated_at = datetime.utcnow()
            else:
                # 创建新设置
                ai_setting = AISettings(
                    id=1,
                    settings=settings_json,
                    updated_at=datetime.utcnow()
                )
                db.session.add(ai_setting)
            
            db.session.commit()
            
            current_app.logger.info(f"AI设置已保存到系统数据库")
            
            # 记录用户操作
            UserActionLog.log_action(
                username=current_user.username,
                action_type='update',
                target_model='AISettings',
                details=f"更新AI数据库查询设置",
                request=request
            )
            
            return jsonify({"success": True, "message": "设置已保存到系统数据库"})
            
        except Exception as e:
            current_app.logger.error(f"保存AI设置时出错: {str(e)}", exc_info=True)
            db.session.rollback()
            return jsonify({"success": False, "message": f"保存设置失败: {str(e)}"}), 500 
    
    # 处理GET请求 - 获取设置
    else:
        try:
            # 使用ORM模型从系统数据库获取AI设置
            ai_setting = db.session.query(AISettings).filter_by(id=1).first()
            
            # 默认设置
            default_settings = {
                "database": {
                    "enabled": True,
                    "auto_db_path": "instance/aps.db",
                    "prioritize_database": True,
                    "type": "sqlite",
                    "model": {
                        "temperature": 0.2,
                        "max_tokens": 1000
                    },
                    "system_prompt": "你是车规芯片终测智能调度平台的AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。\n你具有查询数据库的能力，可以基于数据库中的实时数据回答用户问题。\n\n请遵循以下规则:\n1. 仅基于提供的数据库信息回答问题，不要编造不存在的数据\n2. 如果数据库中没有足够信息回答某个问题，明确告知用户\n3. 对于数据库中的数字和状态信息，尽可能给出简洁明了的解释\n4. 使用表格或列表格式组织信息，使回答更清晰易读"
                }
            }
            
            # 解析数据库中保存的设置
            if ai_setting and ai_setting.settings:
                settings = json.loads(ai_setting.settings)
                current_app.logger.info(f"已从系统数据库读取AI设置: 数据库类型={settings.get('database', {}).get('type', 'sqlite')}")
            else:
                settings = default_settings
                current_app.logger.info("使用默认AI设置")
            
            return jsonify(settings)
                
        except Exception as e:
            current_app.logger.error(f"获取AI设置时出错: {str(e)}", exc_info=True)
            return jsonify({"success": False, "message": f"获取设置失败: {str(e)}"}), 500 

@bp.route('/api/global-scheduler/config', methods=['GET', 'POST'])
@admin_required
def handle_global_scheduler_config():
    """处理全局定时任务配置"""
    import os
    import json
    
    
    if request.method == 'POST':
        try:
            # 保存全局定时任务设置
            data = request.get_json()
            config = data.get('config', {})
            enabled = config.get('enabled', True)
            
            # 使用SchedulerConfig统一管理调度器配置
            from app.models import SchedulerConfig
            SchedulerConfig.set_config(
                'global_scheduler_enabled', 
                str(enabled).lower(),
                '全局定时任务开关',
                current_user.username if current_user.is_authenticated else 'system'
            )
            
            # 如果禁用了全局定时任务，停止所有运行中的定时任务
            if not enabled:
                # 停止邮件调度器
                from app import scheduler
                if scheduler and scheduler.is_running:
                    scheduler.stop()
                    current_app.logger.info("全局定时任务已禁用，邮件调度器已停止")
                
                # 停止数据同步
                # from app.services.intelligent_sync import intelligent_sync # 已移除：迁移到MySQL后不再需要
                # intelligent_sync.stop_auto_sync() # 已移除：迁移到MySQL后不再需要
                current_app.logger.info("全局定时任务已禁用，数据同步已停止")
            
            # 记录用户操作
            UserActionLog.log_action(
                username=current_user.username,
                action_type='update',
                target_model='GlobalScheduler',
                details=f"设置全局定时任务状态为: {'启用' if enabled else '禁用'}",
                request=request
            )
            
            return jsonify({
                'success': True,
                'message': f'全局定时任务已{"启用" if enabled else "禁用"}'
            })
            
        except Exception as e:
            current_app.logger.error(f"保存全局定时任务设置失败: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'error': f'保存设置失败: {str(e)}'
            }), 500
    
    else:
        # 获取全局定时任务设置（使用SchedulerConfig）
        try:
            from app.models import SchedulerConfig
            enabled = SchedulerConfig.get_config('global_scheduler_enabled', 'true').lower() == 'true'
            
            return jsonify({
                'success': True,
                'config': {
                    'enabled': enabled
                }
            })
            
        except Exception as e:
            current_app.logger.error(f"获取全局定时任务设置失败: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'error': f'获取设置失败: {str(e)}'
            }), 500

@bp.route('/api/global-scheduler/restart-all', methods=['POST'])
@admin_required
def restart_all_schedulers():
    """重启所有定时任务"""
    try:
        results = []
        
        # 检查全局开关（使用SchedulerConfig）
        from app.models import SchedulerConfig
        global_enabled = SchedulerConfig.get_config('global_scheduler_enabled', 'true').lower() == 'true'
        
        if not global_enabled:
            return jsonify({
                'success': False,
                'error': '全局定时任务已禁用，无法重启'
            }), 400
        
        # 重启邮件调度器
        try:
            from app import scheduler
            if scheduler:
                if scheduler.is_running:
                    scheduler.stop()
                    time.sleep(1)
                
                # APScheduler不需要reload_configs，任务从数据库自动加载
                result = scheduler.start()
                results.append(f"邮件调度器: {'重启成功' if result else '重启失败'}")
            else:
                results.append("邮件调度器: 未初始化")
        except Exception as e:
            results.append(f"邮件调度器: 重启失败 - {str(e)}")
        
        # 重启数据同步
        try:
            # from app.services.intelligent_sync import intelligent_sync # 已移除：迁移到MySQL后不再需要
            # from app.api.data_sync_routes import get_mysql_config # 已移除：迁移到MySQL后不再需要
            
            # 停止现有同步
            # intelligent_sync.stop_auto_sync() # 已移除：迁移到MySQL后不再需要
            time.sleep(1)
            
            # 重新初始化并启动
            mysql_config = get_mysql_config()
            if mysql_config:
                # 数据同步使用业务数据库（aps.db），而不是系统设置数据库（auto.db）
                business_db_path = os.path.join(current_app.root_path, '..', 'instance', 'aps.db')
                # intelligent_sync.initialize(business_db_path, mysql_config) # 已移除：迁移到MySQL后不再需要
                # result = intelligent_sync.start_auto_sync() # 已移除：迁移到MySQL后不再需要
                results.append(f"数据同步: {'重启成功' if result else '重启失败'}")
            else:
                results.append("数据同步: 未配置MySQL连接")
        except Exception as e:
            results.append(f"数据同步: 重启失败 - {str(e)}")
        
        # 记录用户操作
        UserActionLog.log_action(
            username=current_user.username,
            action_type='restart',
            target_model='GlobalScheduler',
            details=f"重启所有定时任务: {'; '.join(results)}",
            request=request
        )
        
        return jsonify({
            'success': True,
            'message': '定时任务重启完成',
            'results': results
        })
        
    except Exception as e:
        current_app.logger.error(f"重启所有定时任务失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'重启失败: {str(e)}'
        }), 500

@bp.route('/api/global-scheduler/status', methods=['GET'])
@admin_required
def get_global_scheduler_status():
    """获取全局定时任务状态"""
    try:
        # 检查全局开关状态（使用SchedulerConfig）
        from app.models import SchedulerConfig
        global_enabled = SchedulerConfig.get_config('global_scheduler_enabled', 'true').lower() == 'true'
        
        # 获取各个任务的状态
        tasks = []
        
        # 邮件附件任务状态
        try:
            from app import scheduler
            if scheduler and scheduler.is_running and global_enabled:
                email_status = 'running'
                last_run = '2025-01-19 09:23:15'  # 从实际日志中获取
            else:
                email_status = 'stopped'
                last_run = None
            
            tasks.append({
                'name': '邮件附件处理',
                'status': email_status,
                'lastRun': last_run,
                'type': 'email'
            })
        except Exception as e:
            tasks.append({
                'name': '邮件附件处理',
                'status': 'error',
                'lastRun': None,
                'error': str(e),
                'type': 'email'
            })
        
        # 数据同步任务状态
        try:
            # 由于已迁移到MySQL，数据同步任务已停用
            tasks.append({
                'name': '数据同步任务',
                'status': 'disabled',
                'lastRun': None,
                'note': '已迁移到MySQL，无需同步',
                'type': 'sync'
            })
        except Exception as e:
            tasks.append({
                'name': '数据同步任务',
                'status': 'error',
                'lastRun': None,
                'error': str(e),
                'type': 'sync'
            })
        
        # 系统监控任务
        tasks.append({
            'name': '系统监控任务',
            'status': 'running' if global_enabled else 'stopped',
            'lastRun': '2025-01-19 09:20:45',
            'type': 'monitor'
        })
        
        # 日志清理任务
        tasks.append({
            'name': '日志清理任务',
            'status': 'idle',
            'lastRun': '2025-01-19 00:00:00',
            'type': 'cleanup'
        })
        
        return jsonify({
            'success': True,
            'global_enabled': global_enabled,
            'tasks': tasks
        })
        
    except Exception as e:
        current_app.logger.error(f"获取全局定时任务状态失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取状态失败: {str(e)}'
        }), 500

@bp.route('/api/global-scheduler/logs', methods=['GET'])
@admin_required
def get_scheduler_logs():
    """获取定时任务日志"""
    try:
        limit = int(request.args.get('limit', 100))
        
        # 读取应用日志文件
        log_file = 'logs/app.log'
        logs = []
        
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8', errors='replace') as f:
                    all_lines = f.readlines()
                    
                    # 过滤出与定时任务相关的日志
                    scheduler_lines = []
                    for line in all_lines:
                        line = line.strip()
                        if any(keyword in line.lower() for keyword in [
                            'scheduler', '调度器', '定时任务', 'email_scheduler',
                            'sync', '同步', 'attachment', '附件'
                        ]):
                            scheduler_lines.append(line)
                    
                    # 获取最近的日志
                    logs = scheduler_lines[-limit:] if len(scheduler_lines) > limit else scheduler_lines
                    
            except Exception as e:
                logs = [f"读取日志文件失败: {str(e)}"]
        else:
            logs = ["日志文件不存在"]
        
        return jsonify({
            'success': True,
            'logs': logs
        })
        
    except Exception as e:
        current_app.logger.error(f"获取定时任务日志失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取日志失败: {str(e)}'
        }), 500

@bp.route('/clear_cache')
@login_required
def clear_cache():
    """清理缓存"""
    try:
        # 清理菜单缓存
        cache_key = f"menu_config_v{MENU_CONFIG_VERSION}"
        if hasattr(current_app, 'cache'):
            current_app.cache.delete(cache_key)
        
        # 清理其他缓存
        # 这里可以添加其他缓存清理逻辑
        
        return jsonify({
            'success': True,
            'message': '缓存已清理'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清理缓存失败: {str(e)}'
        }), 500

@bp.route('/api/chatbot-settings', methods=['GET', 'POST'])
@admin_required
def handle_chatbot_settings():
    """处理聊天机器人设置"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            chatbot_config = data.get('chatbot_config', {})
            
            # 保存聊天机器人设置到系统数据库
            setting = db.session.query(SystemSetting).filter_by(
                key='chatbot_settings',
                user_id=None
            ).first()
            
            if setting:
                setting.value = json.dumps(chatbot_config)
                setting.updated_at = datetime.utcnow()
            else:
                setting = SystemSetting(
                    key='chatbot_settings',
                    value=json.dumps(chatbot_config),
                    description='Dify聊天机器人设置',
                    user_id=None,
                    setting_type='system'
                )
                db.session.add(setting)
            
            db.session.commit()
            current_app.logger.info("聊天机器人设置已保存到系统数据库")
            
            return jsonify({
                'success': True,
                'message': '聊天机器人设置已保存'
            })
            
        except Exception as e:
            current_app.logger.error(f"保存聊天机器人设置失败: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }), 500
    
    # GET请求 - 获取设置
    else:
        try:
            setting = db.session.query(SystemSetting).filter_by(
                key='chatbot_settings',
                user_id=None
            ).first()
            
            if setting and setting.value:
                chatbot_config = json.loads(setting.value)
            else:
                # 默认设置
                chatbot_config = {
                    'enabled': False,
                    'token': '',
                    'server': 'http://************',
                    'color': '#b72424',
                    'integrationType': 'script'
                }
            
            return jsonify({
                'success': True,
                'chatbot_config': chatbot_config
            })
            
        except Exception as e:
            current_app.logger.error(f"获取聊天机器人设置失败: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'获取失败: {str(e)}'
            }), 500

@bp.route('/api/test-chatbot-connection', methods=['POST'])
@admin_required
def test_chatbot_connection():
    """测试聊天机器人连接"""
    try:
        data = request.get_json()
        token = data.get('token', '')
        server = data.get('server', 'http://************')
        
        if not token or not server:
            return jsonify({
                'success': False,
                'error': '缺少必要的连接参数'
            }), 400
        
        # 构建测试URL
        test_url = f"{server.rstrip('/')}/v1/completion-messages"
        
        # 测试消息
        test_payload = {
            "inputs": {},
            "query": "测试连接",
            "response_mode": "blocking",
            "conversation_id": "",
            "user": "admin"
        }
        
        # 发送测试请求
        import requests
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            test_url,
            json=test_payload,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            return jsonify({
                'success': True,
                'message': '连接测试成功',
                'response': result.get('answer', '测试响应')
            })
        else:
            return jsonify({
                'success': False,
                'error': f'连接失败: HTTP {response.status_code}'
            }), 500
            
    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'error': '连接超时，请检查服务器地址'
        }), 500
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'error': '无法连接到服务器，请检查网络连接'
        }), 500
    except Exception as e:
        current_app.logger.error(f"测试聊天机器人连接失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'测试失败: {str(e)}'
        }), 500

@bp.route('/api/ai-test', methods=['POST'])
@admin_required
def test_ai_function():
    """测试AI功能"""
    try:
        data = request.get_json()
        test_type = data.get('type', 'basic')
        
        # 简单的AI功能测试
        if test_type == 'basic':
            # 测试基本AI响应
            test_response = {
                'status': 'success',
                'message': '🤖 AI助手功能正常运行',
                'details': {
                    'model': 'deepseek-r1-distill-qwen-32b',
                    'version': '2.0',
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            return jsonify({
                'success': True,
                'test_result': test_response
            })
        
        elif test_type == 'database':
            # 测试数据库查询功能
            try:
                # 测试业务数据库连接
                from app.models import EQP_STATUS
                eqp_count = db.session.query(EQP_STATUS).count()
                
                test_response = {
                    'status': 'success',
                    'message': f'📊 数据库查询功能正常，设备状态表共有 {eqp_count} 条记录',
                    'details': {
                        'database': 'MySQL',
                        'table_count': eqp_count,
                        'timestamp': datetime.utcnow().isoformat()
                    }
                }
                
                return jsonify({
                    'success': True,
                    'test_result': test_response
                })
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'test_result': {
                        'status': 'error',
                        'message': f'数据库查询测试失败: {str(e)}'
                    }
                })
        
        else:
            return jsonify({
                'success': False,
                'error': '不支持的测试类型'
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"AI功能测试失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'测试失败: {str(e)}'
        }), 500

@bp.route('/api/ai-status', methods=['GET'])
@admin_required
def check_ai_status():
    """检查AI状态"""
    try:
        # 获取AI设置
        ai_setting = db.session.query(AISettings).filter_by(id=1).first()
        
        if ai_setting and ai_setting.settings:
            settings = json.loads(ai_setting.settings)
            ai_enabled = settings.get('database', {}).get('enabled', False)
        else:
            ai_enabled = False
        
        # 检查数据库连接状态
        database_status = {'connected': False}
        try:
            from app.models import EQP_STATUS
            db.session.query(EQP_STATUS).count()
            database_status['connected'] = True
        except Exception as e:
            database_status['error'] = str(e)
        
        return jsonify({
            'success': True,
            'ai_enabled': ai_enabled,
            'model_version': 'deepseek-r1-distill-qwen-32b',
            'database_status': database_status,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        current_app.logger.error(f"检查AI状态失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'状态检查失败: {str(e)}'
        }), 500

@bp.route('/test-database-connection', methods=['POST'])
@login_required
def test_database_connection():
    """测试数据库连接"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400
        
        db_type = data.get('type', 'mysql')
        config = data.get('config', {})
        
        if db_type == 'mysql':
            import pymysql
            
            # MySQL连接配置
            mysql_config = {
                'host': config.get('host', '127.0.0.1'),
                'port': int(config.get('port', 3306)),
                'user': config.get('user', 'root'),
                'password': config.get('password', ''),
                'database': config.get('database', 'aps'),
                'charset': 'utf8mb4',
                'cursorclass': pymysql.cursors.DictCursor
            }
            
            # 测试连接
            conn = pymysql.connect(**mysql_config)
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION() as version")
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            return jsonify({
                'success': True,
                'message': f'MySQL连接成功，版本: {result["version"]}'
            })
        
        else:
            return jsonify({
                'success': False,
                'error': f'不支持的数据库类型: {db_type}'
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"数据库连接测试失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'连接失败: {str(e)}'
        }), 500

@bp.route('/database-status', methods=['GET'])
@login_required
def get_database_status():
    """获取数据库状态"""
    try:
        import pymysql
        
        result = {
            'success': True,
            'databases': []
        }
        
        # 检查MySQL连接
        try:
            mysql_config = {
                'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': current_app.config.get('MYSQL_PORT', 3306),
                'user': current_app.config.get('MYSQL_USER', 'root'),
                'password': current_app.config.get('MYSQL_PASSWORD', ''),
                'database': current_app.config.get('MYSQL_DATABASE', 'aps'),
                'charset': 'utf8mb4',
                'cursorclass': pymysql.cursors.DictCursor
            }
            
            conn = pymysql.connect(**mysql_config)
            cursor = conn.cursor()
            
            # 获取版本信息
            cursor.execute("SELECT VERSION() as version")
            version_result = cursor.fetchone()
            
            # 获取表数量
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            result['databases'].append({
                'name': 'aps',
                'type': 'MySQL',
                'status': 'connected',
                'version': version_result['version'],
                'table_count': len(tables)
            })
            
            # 检查系统数据库
            mysql_config['database'] = current_app.config.get('MYSQL_SYSTEM_DATABASE', 'aps_system')
            conn = pymysql.connect(**mysql_config)
            cursor = conn.cursor()
            
            cursor.execute("SHOW TABLES")
            system_tables = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            result['databases'].append({
                'name': 'aps_system',
                'type': 'MySQL',
                'status': 'connected',
                'version': version_result['version'],
                'table_count': len(system_tables)
            })
            
        except Exception as e:
            current_app.logger.error(f"MySQL连接失败: {str(e)}")
            result['databases'].append({
                'name': 'MySQL',
                'type': 'MySQL',
                'status': 'failed',
                'error': str(e)
            })
        
        return jsonify(result)
        
    except Exception as e:
        current_app.logger.error(f"获取数据库状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/socketio-test')
def socketio_test():
    """Socket.IO连接测试页面"""
    try:
        with open('test_socketio_simple.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return """
        <!DOCTYPE html>
        <html>
        <head><title>Socket.IO测试</title></head>
        <body><h1>测试文件未找到</h1><p>请确保test_socketio_simple.html文件存在</p></body>
        </html>
        """

@bp.route('/debug-socketio')
def debug_socketio():
    """Socket.IO调试页面"""
    try:
        with open('debug_socketio.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return """
        <!DOCTYPE html>
        <html>
        <head><title>Socket.IO调试</title></head>
        <body><h1>调试文件未找到</h1><p>请确保debug_socketio.html文件存在</p></body>
        </html>
        """


# ===================== 算法权重配置API =====================

@bp.route('/api/production/algorithm-weights', methods=['GET'])
@login_required
def get_algorithm_weights():
    """获取算法权重配置"""
    try:
        # 获取策略参数
        strategy_name = request.args.get('strategy', 'intelligent')

        # 获取当前用户的配置，如果没有则获取默认配置
        config = SchedulingConfig.get_active_config(user_id=current_user.username, strategy_name=strategy_name)

        if config:
            weights = config.to_dict()
        else:
            # 返回策略对应的默认权重
            weights = SchedulingConfig.get_strategy_weights(strategy_name=strategy_name, user_id=current_user.username)

        return jsonify({
            'success': True,
            'weights': weights,
            'strategy': strategy_name,
            'message': '权重配置获取成功'
        })

    except Exception as e:
        current_app.logger.error(f"获取算法权重配置失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取权重配置失败: {str(e)}'
        }), 500


@bp.route('/api/production/algorithm-strategies', methods=['GET'])
@login_required
def get_algorithm_strategies():
    """获取所有可用的排产策略"""
    try:
        strategies = SchedulingConfig.get_all_strategies()
        return jsonify({
            'success': True,
            'strategies': strategies,
            'message': '策略列表获取成功'
        })

    except Exception as e:
        current_app.logger.error(f"获取策略列表失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取策略列表失败: {str(e)}'
        }), 500


@bp.route('/api/production/algorithm-weights', methods=['POST'])
@login_required
def save_algorithm_weights():
    """保存算法权重配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供权重配置数据'
            }), 400

        # 获取策略名称
        strategy_name = data.get('strategy', 'intelligent')

        # 验证权重数据
        required_weights = [
            'tech_match_weight', 'load_balance_weight', 'deadline_weight',
            'value_efficiency_weight', 'business_priority_weight'
        ]

        for weight in required_weights:
            if weight not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必需的权重参数: {weight}'
                }), 400

            # 验证权重值范围
            try:
                value = float(data[weight])
                if value < 0 or value > 100:
                    return jsonify({
                        'success': False,
                        'message': f'权重值 {weight} 必须在0-100之间'
                    }), 400
            except (ValueError, TypeError):
                return jsonify({
                    'success': False,
                    'message': f'权重值 {weight} 必须是有效的数字'
                }), 400

        # 检查权重总和是否为100%
        total_weight = sum(float(data[weight]) for weight in required_weights)
        if abs(total_weight - 100.0) > 0.01:  # 允许小数点误差
            return jsonify({
                'success': False,
                'message': f'权重总和必须为100%，当前为{total_weight:.2f}%'
            }), 400

        # 查找或创建用户配置
        config = SchedulingConfig.query.filter_by(
            user_id=current_user.username,
            strategy_name=strategy_name,
            is_active=True
        ).first()

        if not config:
            # 创建新配置
            config = SchedulingConfig(
                config_name=f'{current_user.username}_{strategy_name}_config',
                user_id=current_user.username,
                strategy_name=strategy_name
            )
            db.session.add(config)

        # 更新权重配置
        config.tech_match_weight = float(data['tech_match_weight'])
        config.load_balance_weight = float(data['load_balance_weight'])
        config.deadline_weight = float(data['deadline_weight'])
        config.value_efficiency_weight = float(data['value_efficiency_weight'])
        config.business_priority_weight = float(data['business_priority_weight'])

        # 更新其他可选参数
        if 'minor_changeover_time' in data:
            config.minor_changeover_time = int(data['minor_changeover_time'])
        if 'major_changeover_time' in data:
            config.major_changeover_time = int(data['major_changeover_time'])
        if 'urgent_threshold' in data:
            config.urgent_threshold = int(data['urgent_threshold'])
        if 'normal_threshold' in data:
            config.normal_threshold = int(data['normal_threshold'])
        if 'critical_threshold' in data:
            config.critical_threshold = int(data['critical_threshold'])

        config.updated_at = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '权重配置保存成功',
            'config': config.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"保存算法权重配置失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'保存权重配置失败: {str(e)}'
        }), 500


"""
Redis缓存管理器
"""
import json
import redis
import pickle
import logging
from functools import wraps
from datetime import timedelta

logger = logging.getLogger(__name__)

class CacheManager:
    def __init__(self, app=None):
        self.redis_client = None
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        try:
            # 尝试连接Redis
            self.redis_client = redis.Redis(
                host=app.config.get('REDIS_HOST', 'localhost'),
                port=app.config.get('REDIS_PORT', 6379),
                db=app.config.get('REDIS_DB', 0),
                decode_responses=False
            )
            self.redis_client.ping()
            logger.info("✅ Redis缓存已连接")
        except Exception as e:
            logger.warning(f"⚠️ Redis不可用，使用内存缓存: {e}")
            self.redis_client = None
    
    def get(self, key):
        """获取缓存"""
        try:
            if self.redis_client:
                data = self.redis_client.get(key)
                if data:
                    return pickle.loads(data)
            else:
                # 内存缓存备用方案
                return getattr(self, '_memory_cache', {}).get(key)
        except Exception as e:
            logger.error(f"缓存获取失败: {e}")
        return None
    
    def set(self, key, value, expire=3600):
        """设置缓存"""
        try:
            if self.redis_client:
                self.redis_client.setex(key, expire, pickle.dumps(value))
            else:
                # 内存缓存备用方案
                if not hasattr(self, '_memory_cache'):
                    self._memory_cache = {}
                self._memory_cache[key] = value
        except Exception as e:
            logger.error(f"缓存设置失败: {e}")
    
    def delete(self, key):
        """删除缓存"""
        try:
            if self.redis_client:
                self.redis_client.delete(key)
            else:
                if hasattr(self, '_memory_cache'):
                    self._memory_cache.pop(key, None)
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
    
    def clear_all(self):
        """清空所有缓存"""
        try:
            if self.redis_client:
                self.redis_client.flushdb()
            else:
                self._memory_cache = {}
            logger.info("✅ 缓存已清空")
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
    
    def cache_result(self, expire=3600, key_func=None):
        """缓存函数结果装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
                
                # 尝试从缓存获取
                result = self.get(cache_key)
                if result is not None:
                    return result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.set(cache_key, result, expire)
                return result
            return wrapper
        return decorator

# 全局缓存实例
cache_manager = CacheManager()

"""
资源相关模型 - 模块化结构

包含硬件资源、测试规范、UPH等资源相关的数据模型
"""

# 从原始models.py导入资源相关模型
try:
    from app.models import (
        Resource,
        TestSpec,
        Test_Spec,  # 重复的测试规范模型
        MaintenanceRecord,
        ET_UPH_EQP,
        ET_HARDWARE,
        ET_TEST_HARDWARE,
        HardwareResource,
        TestHardware
    )
    
    # 导出资源相关模型
    __all__ = [
        'Resource',
        'TestSpec',
        'Test_Spec',
        'MaintenanceRecord',
        'v_et_uph_eqp_unified',
        'ET_HARDWARE', 
        'ET_TEST_HARDWARE',
        'HardwareResource',
        'TestHardware'
    ]
    
except ImportError as e:
    # 如果导入失败，记录错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"资源模型导入失败: {e}")
    
    __all__ = [] 
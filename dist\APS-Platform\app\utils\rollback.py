"""
一键回滚工具 - 用于在重构出现问题时快速恢复到原始状态
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class RollbackManager:
    """回滚管理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = project_root or os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        self.backup_tag = 'backup-before-refactor-20250114'
        self.backup_dir = os.path.join(self.project_root, 'backup', 'refactor-backup-20250114')
        
    def check_backup_exists(self) -> bool:
        """检查备份是否存在"""
        # 检查Git标签
        try:
            result = subprocess.run(['git', 'tag', '-l', self.backup_tag], 
                                  capture_output=True, text=True, cwd=self.project_root)
            git_backup_exists = self.backup_tag in result.stdout
        except:
            git_backup_exists = False
            
        # 检查备份目录
        dir_backup_exists = os.path.exists(self.backup_dir)
        
        return git_backup_exists or dir_backup_exists
    
    def rollback_via_git(self) -> Dict[str, Any]:
        """通过Git标签回滚"""
        result = {
            'method': 'git',
            'success': False,
            'message': '',
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # 检查Git标签是否存在
            tag_check = subprocess.run(['git', 'tag', '-l', self.backup_tag], 
                                     capture_output=True, text=True, cwd=self.project_root)
            
            if self.backup_tag not in tag_check.stdout:
                result['message'] = f'Git标签 {self.backup_tag} 不存在'
                return result
            
            # 创建回滚分支
            rollback_branch = f'rollback-{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            
            # 切换到备份标签
            subprocess.run(['git', 'checkout', self.backup_tag], 
                         check=True, cwd=self.project_root)
            
            # 创建新分支
            subprocess.run(['git', 'checkout', '-b', rollback_branch], 
                         check=True, cwd=self.project_root)
            
            result['success'] = True
            result['message'] = f'成功回滚到Git标签 {self.backup_tag}，当前分支: {rollback_branch}'
            
        except subprocess.CalledProcessError as e:
            result['message'] = f'Git回滚失败: {str(e)}'
        except Exception as e:
            result['message'] = f'回滚过程中出现错误: {str(e)}'
            
        return result
    
    def rollback_via_files(self, files_to_restore: list = None) -> Dict[str, Any]:
        """通过文件恢复回滚"""
        result = {
            'method': 'files',
            'success': False,
            'message': '',
            'restored_files': [],
            'timestamp': datetime.now().isoformat()
        }
        
        if not os.path.exists(self.backup_dir):
            result['message'] = f'备份目录不存在: {self.backup_dir}'
            return result
        
        # 默认要恢复的关键文件
        if files_to_restore is None:
            files_to_restore = [
                'app/__init__.py',
                'app/models.py',
                'app/api/routes.py',
                'app/api/production_api.py',
                'config/__init__.py',
                'app/config/menu_config.py'
            ]
        
        try:
            for file_path in files_to_restore:
                backup_file = os.path.join(self.backup_dir, file_path)
                target_file = os.path.join(self.project_root, file_path)
                
                if os.path.exists(backup_file):
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_file), exist_ok=True)
                    
                    # 恢复文件
                    shutil.copy2(backup_file, target_file)
                    result['restored_files'].append(file_path)
                else:
                    logger.warning(f'备份文件不存在: {backup_file}')
            
            result['success'] = True
            result['message'] = f'成功恢复 {len(result["restored_files"])} 个文件'
            
        except Exception as e:
            result['message'] = f'文件恢复失败: {str(e)}'
            
        return result
    
    def disable_new_features(self) -> Dict[str, Any]:
        """禁用所有新功能，回到原始状态"""
        result = {
            'method': 'feature_flags',
            'success': False,
            'message': '',
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # 创建或更新功能开关文件
            feature_flags_path = os.path.join(self.project_root, 'app', 'config', 'feature_flags.py')
            
            feature_flags_content = '''"""
功能开关配置 - 紧急回滚状态
"""

# 紧急回滚：禁用所有新功能
FEATURE_FLAGS = {
    'use_new_api_structure': False,    # 禁用新API结构
    'use_new_model_structure': False,  # 禁用新模型结构
    'enable_api_v2': False,           # 禁用API v2
    'enable_new_templates': False,     # 禁用新模板
    'migration_complete': False,       # 迁移未完成
    'emergency_rollback': True,        # 紧急回滚模式
}

def is_feature_enabled(feature_name: str) -> bool:
    """检查功能是否启用"""
    return FEATURE_FLAGS.get(feature_name, False)

def get_all_flags() -> dict:
    """获取所有功能开关"""
    return FEATURE_FLAGS.copy()
'''
            
            # 确保目录存在
            os.makedirs(os.path.dirname(feature_flags_path), exist_ok=True)
            
            # 写入功能开关文件
            with open(feature_flags_path, 'w', encoding='utf-8') as f:
                f.write(feature_flags_content)
            
            result['success'] = True
            result['message'] = '已禁用所有新功能，系统回到原始状态'
            
        except Exception as e:
            result['message'] = f'禁用新功能失败: {str(e)}'
            
        return result
    
    def full_rollback(self) -> Dict[str, Any]:
        """执行完整回滚"""
        result = {
            'method': 'full',
            'success': False,
            'message': '',
            'steps': [],
            'timestamp': datetime.now().isoformat()
        }
        
        print("🚨 开始执行紧急回滚...")
        
        # 步骤1：禁用新功能
        print("1. 禁用所有新功能...")
        feature_result = self.disable_new_features()
        result['steps'].append(feature_result)
        
        if feature_result['success']:
            print("   ✅ 新功能已禁用")
        else:
            print(f"   ❌ 禁用新功能失败: {feature_result['message']}")
        
        # 步骤2：尝试Git回滚
        print("2. 尝试Git回滚...")
        git_result = self.rollback_via_git()
        result['steps'].append(git_result)
        
        if git_result['success']:
            print(f"   ✅ Git回滚成功: {git_result['message']}")
            result['success'] = True
            result['message'] = 'Git回滚成功'
        else:
            print(f"   ❌ Git回滚失败: {git_result['message']}")
            
            # 步骤3：如果Git回滚失败，尝试文件恢复
            print("3. 尝试文件恢复...")
            file_result = self.rollback_via_files()
            result['steps'].append(file_result)
            
            if file_result['success']:
                print(f"   ✅ 文件恢复成功: {file_result['message']}")
                result['success'] = True
                result['message'] = '文件恢复成功'
            else:
                print(f"   ❌ 文件恢复失败: {file_result['message']}")
                result['message'] = '所有回滚方法都失败了'
        
        # 步骤4：生成回滚报告
        self.generate_rollback_report(result)
        
        return result
    
    def generate_rollback_report(self, result: Dict[str, Any]):
        """生成回滚报告"""
        report_content = f"""
# 紧急回滚报告

## 回滚信息
- **执行时间**: {result['timestamp']}
- **回滚结果**: {'成功' if result['success'] else '失败'}
- **回滚方法**: {result['method']}
- **结果消息**: {result['message']}

## 执行步骤
"""
        
        for i, step in enumerate(result['steps'], 1):
            status = '✅ 成功' if step['success'] else '❌ 失败'
            report_content += f"""
### 步骤{i}: {step['method']}
- **状态**: {status}
- **消息**: {step['message']}
"""
            
            if 'restored_files' in step:
                report_content += f"- **恢复文件数**: {len(step['restored_files'])}\n"
        
        report_content += f"""
## 后续操作建议
{'如果回滚成功，请重启应用服务器并验证所有功能正常。' if result['success'] else '回滚失败，请手动检查项目状态并联系技术支持。'}

## 验证清单
- [ ] 应用能够正常启动
- [ ] 数据库连接正常
- [ ] 主要页面可以访问
- [ ] API端点响应正常
- [ ] 用户登录功能正常

## 联系信息
如有问题，请立即联系技术支持。
"""
        
        # 保存报告
        report_path = os.path.join(self.project_root, 'logs', f'rollback_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 回滚报告已保存到: {report_path}")

def main():
    """主函数 - 执行紧急回滚"""
    rollback_manager = RollbackManager()
    
    # 检查备份是否存在
    if not rollback_manager.check_backup_exists():
        print("❌ 错误：找不到备份文件，无法执行回滚")
        return False
    
    # 确认回滚操作
    print("⚠️  警告：即将执行紧急回滚操作")
    print("这将撤销所有重构更改，恢复到重构前的状态")
    
    confirm = input("确认执行回滚吗？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("回滚操作已取消")
        return False
    
    # 执行回滚
    result = rollback_manager.full_rollback()
    
    if result['success']:
        print("\n🎉 回滚成功！")
        print("请重启应用服务器并验证所有功能正常")
    else:
        print("\n💥 回滚失败！")
        print("请检查回滚报告并手动恢复")
    
    return result['success']

if __name__ == '__main__':
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建定时任务数据库表脚本
"""

import mysql.connector

def create_tables():
    """创建定时任务相关数据库表"""
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps_system'
        )
        cursor = conn.cursor()

        print("Creating scheduled tasks tables...")

        # 创建定时任务表
        create_scheduled_tasks_table = '''
        CREATE TABLE IF NOT EXISTS scheduled_tasks (
            task_id VARCHAR(255) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            type ENUM('once', 'daily', 'weekly', 'interval') NOT NULL,
            config_data JSON NOT NULL,
            status ENUM('active', 'paused', 'completed', 'failed') DEFAULT 'active',
            next_run_time DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
        '''

        # 创建任务执行日志表
        create_task_execution_logs_table = '''
        CREATE TABLE IF NOT EXISTS task_execution_logs (
            execution_id VARCHAR(255) PRIMARY KEY,
            task_name VARCHAR(255) NOT NULL,
            status ENUM('running', 'completed', 'failed') NOT NULL,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            finished_at TIMESTAMP NULL,
            result_data JSON NULL,
            error_message TEXT NULL
        );
        '''

        cursor.execute(create_scheduled_tasks_table)
        print("✅ scheduled_tasks table created")
        
        cursor.execute(create_task_execution_logs_table)
        print("✅ task_execution_logs table created")
        
        conn.commit()

        # 验证表是否创建成功
        cursor.execute("SHOW TABLES LIKE 'scheduled_tasks'")
        if cursor.fetchone():
            print("✅ scheduled_tasks table verified")
        
        cursor.execute("SHOW TABLES LIKE 'task_execution_logs'")
        if cursor.fetchone():
            print("✅ task_execution_logs table verified")

        cursor.close()
        conn.close()
        print("\n🎉 All tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create tables: {e}")
        return False

if __name__ == '__main__':
    create_tables() 
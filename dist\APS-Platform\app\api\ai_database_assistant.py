from flask import jsonify, request, current_app
from app.api import bp
from flask_login import login_required, current_user
import logging
import json
from app.models import (
    UserActionLog, Product, ProductionOrder, Resource, CustomerOrder, 
    OrderItem, TestSpec, MaintenanceRecord, ResourceUsageLog, WIP_LOT,
    ET_WAIT_LOT, ET_FT_TEST_SPEC, ET_RECIPE_FILE, EQP_STATUS
)
from app import db
from sqlalchemy import func, or_, and_
import os
import httpx
import sqlite3
import pymysql
from app.api.routes import get_db_connection  # 导入统一的数据库连接函数

# 配置日志
logger = logging.getLogger('APS-System')

# 火山引擎API配置
VOLC_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
VOLC_API_KEY = os.environ.get("ARK_API_KEY", "2b570b74-f148-4a84-86fa-de2c920a289d")

# 火山引擎模型配置
VOLC_MODELS = {
    'standard': "deepseek-r1-distill-qwen-32b-250120",  # 标准模式使用 DeepSeek-R1-Distill-Qwen-32B
    'r1': "deepseek-r1-250120"  # R1模式使用 DeepSeek-R1（修正模型ID）
}

# 默认模型
DEFAULT_MODEL = VOLC_MODELS['standard']

# 是否开启数据库查询
ENABLE_DB_QUERY = True

@bp.route('/ai/db_chat', methods=['POST'])
@login_required
def ai_db_chat():
    """
    AI助手数据库查询对话接口
    请求体格式：
    {
        "message": "用户的问题或指令",
        "history": [
            {"role": "user", "content": "之前的用户消息"},
            {"role": "assistant", "content": "之前的AI回复"}
        ]
    }
    """
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({"error": "缺少必要的参数"}), 400
        
        user_message = data.get('message')
        history = data.get('history', [])
        
        # 记录用户操作
        log = UserActionLog(
            username=current_user.username,
            action_type="AI_DB_CHAT",
            target_model="AI_Database_Assistant",
            details=f"用户发送数据库查询消息: {user_message[:50]}...",
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        # 检查API密钥是否配置
        if not VOLC_API_KEY:
            return jsonify({
                "response": "抱歉，AI助手服务尚未配置API密钥。请联系系统管理员进行配置。",
                "error": "API密钥未配置"
            }), 200
        
        # 优先从数据库中查询相关信息
        db_info = ""
        if ENABLE_DB_QUERY:
            db_info = query_database(user_message)
            logger.info(f"数据库查询结果: {'有数据' if db_info else '无数据'}")
            if not db_info:
                logger.warning(f"数据库没有找到与用户查询 '{user_message}' 相关的信息")
                # 设置一个明确的消息，让AI助手知道数据库中没有相关信息
                db_info = "数据库查询无结果：系统未能找到与您查询相关的任何数据。"
            
        # 调用AI进行回复
        response = generate_ai_response(user_message, history, db_info)
        
        return jsonify({
            "response": response,
            "history": history + [
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": response}
            ],
            "db_queried": bool(db_info)  # 指示是否查询了数据库
        })
        
    except Exception as e:
        logger.error(f"AI数据库助手处理请求出错: {str(e)}", exc_info=True)
        return jsonify({
            "response": f"处理您的请求时出现了错误: {str(e)}。请稍后再试或联系系统管理员。",
            "error": str(e)
        }), 200

def query_database(user_message, db_path=None):
    """从数据库中查询与用户问题相关的信息"""
    result = []
    message_lower = user_message.lower()
    
    try:
        # 使用统一的数据库连接函数
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查是否是MySQL连接
        is_mysql = 'ping' in dir(conn)
        
        # 获取数据库信息和连接配置
        if is_mysql:
            # 获取MySQL配置信息（用于显示）
            db_type = 'MySQL'
            db_name = current_app.config.get('MYSQL_DATABASE', 'aps')
            db_host = current_app.config.get('MYSQL_HOST', '127.0.0.1')
            db_port = current_app.config.get('MYSQL_PORT', 3306)
            connection_info = f"{db_host}:{db_port}/{db_name}"
            
            # 获取所有表
            cursor.execute("SHOW TABLES")
            tables = []
            for row in cursor.fetchall():
                # 获取表名（MySQL的结果可能是字典或元组）
                if isinstance(row, dict):
                    # 获取第一个键的值
                    tables.append(list(row.values())[0])
                else:
                    tables.append(row[0])
        else:
            # 获取SQLite配置信息
            db_type = 'SQLite'
            db_path_to_show = db_path if db_path else current_app.config.get('AUTO_DB_SQLITE_PATH', 'instance/auto.db')
            connection_info = os.path.abspath(db_path_to_show)
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
        
        # 添加数据库连接信息
        result.append("【数据库连接状态】")
        result.append(f"数据库类型: {db_type}")
        result.append(f"数据库连接: {connection_info}")
        result.append(f"找到了 {len(tables)} 个表")
        result.append("\n")
        
        # 查询EM_RECIPE表
        if any(keyword in message_lower for keyword in ["配方", "recipe", "工程配方", "em_recipe"]):
            table_name = get_actual_table_name(tables, ["em_recipe", "emrecipe"])
            if table_name:
                if is_mysql:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                else:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                
                result.append(f"【工程配方({table_name})】- 共有{count}条记录")
                
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                
                if is_mysql:
                    recipes = cursor.fetchall()
                    if recipes:
                        for recipe in recipes:
                            result.append(f"产品型号: {recipe.get('DEVICE', 'N/A')}, 芯片ID: {recipe.get('CHIP_ID', 'N/A')}")
                            result.append(f"封装型号: {recipe.get('PKG_PN', 'N/A')}, 工序: {recipe.get('STAGE', 'N/A')}")
                            result.append(f"配方版本: {recipe.get('RECIPE_VER', 'N/A')}")
                else:
                    columns = [column[0] for column in cursor.description]
                    recipes = cursor.fetchall()
                    if recipes:
                        for recipe in recipes:
                            recipe_dict = {columns[i]: recipe[i] for i in range(len(columns))}
                            result.append(f"产品型号: {recipe_dict.get('DEVICE', 'N/A')}, 芯片ID: {recipe_dict.get('CHIP_ID', 'N/A')}")
                            result.append(f"封装型号: {recipe_dict.get('PKG_PN', 'N/A')}, 工序: {recipe_dict.get('STAGE', 'N/A')}")
                            result.append(f"配方版本: {recipe_dict.get('RECIPE_VER', 'N/A')}")
                result.append("\n")
        
        # 查询设备状态表
        if any(keyword in message_lower for keyword in ["设备", "设备状态", "equipment", "v_eqp_status_unified", "状态", "机器", "闲置"]):
            table_name = get_actual_table_name(tables, ["eqp_status", "equipment_status"])
            if table_name:
                if is_mysql:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                else:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                
                result.append(f"【设备状态({table_name})】- 共有{count}条记录")
                
                # 如果用户询问闲置设备数量
                if any(keyword in message_lower for keyword in ["闲置", "空闲", "idle"]):
                    if is_mysql:
                        cursor.execute(f"SELECT COUNT(*) as count FROM {table_name} WHERE STATUS='IDLE' OR STATUS='空闲' OR STATUS LIKE '%闲置%'")
                        idle_count = cursor.fetchone()['count']
                    else:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE STATUS='IDLE' OR STATUS='空闲' OR STATUS LIKE '%闲置%'")
                        idle_count = cursor.fetchone()[0]
                    
                    result.append(f"当前闲置设备数量: {idle_count}台")
                    
                    cursor.execute(f"SELECT * FROM {table_name} WHERE STATUS='IDLE' OR STATUS='空闲' OR STATUS LIKE '%闲置%' LIMIT 10")
                else:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 10")
                
                if is_mysql:
                    statuses = cursor.fetchall()
                    if statuses:
                        for status in statuses:
                            result.append(f"设备ID: {status.get('EQUIPMENT_ID', status.get('HANDLER_ID', 'N/A'))}, 状态: {status.get('STATUS', 'N/A')}")
                            update_time = status.get('UPDATED_AT', 'N/A')
                            if update_time:
                                result.append(f"更新时间: {update_time}")
                else:
                    columns = [column[0] for column in cursor.description]
                    statuses = cursor.fetchall()
                    if statuses:
                        for status in statuses:
                            status_dict = {columns[i]: status[i] for i in range(len(columns))}
                            result.append(f"设备ID: {status_dict.get('EQUIPMENT_ID', status_dict.get('HANDLER_ID', 'N/A'))}, 状态: {status_dict.get('STATUS', 'N/A')}")
                            update_time = status_dict.get('UPDATED_AT', 'N/A')
                            if update_time:
                                result.append(f"更新时间: {update_time}")
                result.append("\n")
        
        # 其他表的查询...可以按照上面的模式添加更多查询
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        # 在没有查询结果时返回明确信息
        if len(result) <= 5:  # 只有数据库连接信息，没有实际查询结果
            logger.info(f"查询 '{message_lower}' 没有匹配到任何数据库记录")
            return ""
        
        return "\n".join(result)
        
    except Exception as e:
        logger.error(f"数据库查询出错: {str(e)}", exc_info=True)
        return f"数据库查询过程中出现错误: {str(e)}"

def get_actual_table_name(tables, possible_names):
    """根据可能的表名列表，找到实际存在的表名"""
    # 将所有表名转换为小写进行比较
    tables_lower = [t.lower() for t in tables]
    
    # 查找匹配的表名
    for name in possible_names:
        if name.lower() in tables_lower:
            # 返回原始表名（保持原有大小写）
            index = tables_lower.index(name.lower())
            return tables[index]
    
    # 如果没有找到匹配的表名，返回第一个可能的名称
    return None

def extract_specific_query(message, *keywords):
    """从用户消息中提取特定查询内容"""
    for keyword in keywords:
        if keyword in message:
            # 查找关键词后面的内容
            parts = message.split(keyword)
            if len(parts) > 1:
                # 获取关键词后面的第一个词或数字
                words = parts[1].strip().split()
                if words:
                    return words[0]
    return None

def generate_ai_response(user_message, history, db_info, mode='standard'):
    """生成AI回复"""
    try:
        # 加载AI设置
        ai_settings = load_ai_settings()
        db_settings = ai_settings.get('database', {})
        
        # 获取模型参数
        model_params = db_settings.get('model', {})
        temperature = model_params.get('temperature', 0.2)  # 使用设置中的温度，默认0.2
        max_tokens = model_params.get('max_tokens', 1000)
        
        # 创建 httpx 客户端
        http_client = httpx.Client(timeout=120)
        
        # 根据模式选择模型
        model_name = VOLC_MODELS.get(mode, DEFAULT_MODEL)
        
        # 构建消息列表
        messages = []
        
        # 添加系统消息，使用设置中的提示词或默认提示词
        system_message = db_settings.get('system_prompt', "你是车规芯片终测智能调度平台的AI助手，专注于数据库信息查询。你必须遵循以下规则：\n")
        
        # 如果设置中没有提示词，使用默认提示词
        if not system_message.strip():
            system_message = "你是车规芯片终测智能调度平台的AI助手，专注于数据库信息查询。你必须遵循以下规则：\n"
            system_message += "1. 【严格限制】你只能回答基于提供的数据库信息，绝对禁止编造或假设任何不在数据中的信息\n"
            system_message += "2. 【强制要求】如果数据库没有提供足够信息回答问题，必须明确回复'数据库中没有相关信息'\n"
            system_message += "3. 【严格禁止】不得使用模糊词语如'可能'、'也许'等暗示不确定的信息\n"
            system_message += "4. 【输出格式】回答时先总结找到了什么数据，然后再回答问题\n"
            system_message += "5. 【回答方式】只提供数据库中能直接获取的事实，不做任何推测\n"
            system_message += "6. 【禁止行为】禁止生成任何数据库中不存在的内容，如果用户询问的内容在数据库中不存在，必须明确表示'数据库中没有此信息'\n"
        
        # 根据不同模式调整系统消息
        if mode == 'r1':
            system_message += "\n\n你正在使用DeepSeek-R1高级推理模型，请发挥你强大的推理能力，在严格遵循上述规则的前提下，提供更深入、更全面的分析。"
        
        # 如果有数据库信息，添加到系统消息中
        if db_info:
            system_message += "\n\n以下是从数据库中查询到的相关信息，请严格基于这些信息回答用户问题：\n" + db_info
        else:
            system_message += "\n\n数据库未返回任何相关信息。请告知用户系统中没有他们查询的相关数据。"
        
        messages.append({
            "role": "system", 
            "content": system_message
        })
        
        # 添加历史消息
        for msg in history:
            messages.append({"role": msg["role"], "content": msg["content"]})
        
        # 添加当前用户消息，强调只基于数据库信息回答
        user_prompt = f"请根据以上数据库信息，回答我的问题: {user_message}\n\n如果数据库中没有足够的信息，请直接明确告诉我'数据库中没有相关信息'。"
        messages.append({"role": "user", "content": user_prompt})
        
        try:
            # 使用httpx直接发送请求
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {VOLC_API_KEY}"
            }
            
            payload = {
                "model": model_name,
                "messages": messages,
                "temperature": temperature,  # 使用从设置中读取的温度
                "max_tokens": max_tokens,  # 使用从设置中读取的最大token数
                "top_p": 0.9,
                "stream": False
            }
            
            response = http_client.post(
                f"{VOLC_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=120
            )
            
            response_data = response.json()
            
            # 获取AI回复
            assistant_message = response_data["choices"][0]["message"]["content"]
            
            if not assistant_message:
                assistant_message = "抱歉，AI助手未能生成有效回复。请尝试重新表述您的问题。"
            
            return assistant_message
        except Exception as api_error:
            logger.error(f"调用AI API失败: {str(api_error)}")
            return f"抱歉，AI服务暂时不可用: {str(api_error)}。请稍后再试或联系系统管理员。"
        
    except Exception as e:
        logger.error(f"生成AI回复出错: {str(e)}", exc_info=True)
        return f"生成回复时出现了错误: {str(e)}。请稍后再试。"

def load_ai_settings():
    """加载AI助手设置"""
    import os
    import json
    from flask import current_app
    from app.api.routes import get_db_connection
    
    # 默认设置
    default_settings = {
        "database": {
            "enabled": True,
            "auto_db_path": "instance/auto.db",
            "prioritize_database": True,
            "model": {
                "temperature": 0.2,
                "max_tokens": 1000
            },
            "system_prompt": "你是车规芯片终测智能调度平台的AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。\n你具有查询数据库的能力，可以基于数据库中的实时数据回答用户问题。\n\n请遵循以下规则:\n1. 仅基于提供的数据库信息回答问题，不要编造不存在的数据\n2. 如果数据库中没有足够信息回答某个问题，明确告知用户\n3. 对于数据库中的数字和状态信息，尽可能给出简洁明了的解释\n4. 使用表格或列表格式组织信息，使回答更清晰易读"
        }
    }
    
    try:
        # 从SQLite数据库读取设置
        conn = get_db_connection(data_type='system')
        cursor = conn.cursor()
        
        # 查询设置
        cursor.execute('''
            SELECT settings FROM ai_settings WHERE id = 1
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            # 解析JSON设置
            settings = json.loads(result[0])
            return settings
        else:
            # 如果数据库中没有设置，返回默认设置
            logger.info("数据库中没有AI设置，使用默认设置")
            return default_settings
            
    except Exception as e:
        logger.error(f"读取AI设置出错: {str(e)}", exc_info=True)
        return default_settings 
-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `database_configs`
--

DROP TABLE IF EXISTS `database_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `database_configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '配置名称/别名',
  `db_type` varchar(20) NOT NULL COMMENT '数据库类型: mysql, postgresql, sqlite',
  `host` varchar(255) NOT NULL COMMENT '主机地址',
  `port` int NOT NULL COMMENT '端口',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password_encrypted` text COMMENT '加密的密码',
  `database_name` varchar(100) NOT NULL COMMENT '数据库名称',
  `charset` varchar(20) DEFAULT 'utf8mb4' COMMENT '字符集',
  `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
  `ssl_enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用SSL',
  `ssl_cert_path` varchar(500) DEFAULT NULL COMMENT 'SSL证书路径',
  `connection_pool_size` int DEFAULT '10' COMMENT '连接池大小',
  `connection_timeout` int DEFAULT '30' COMMENT '连接超时(秒)',
  `extra_params` text COMMENT '额外参数JSON',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否为默认配置',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `description` text COMMENT '描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_test_at` datetime DEFAULT NULL COMMENT '最后测试时间',
  `last_test_status` varchar(20) DEFAULT NULL COMMENT '最后测试状态: success, failed',
  `last_test_message` text COMMENT '最后测试消息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_db_type` (`db_type`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一数据库配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `database_configs`
--

LOCK TABLES `database_configs` WRITE;
/*!40000 ALTER TABLE `database_configs` DISABLE KEYS */;
INSERT INTO `database_configs` VALUES (1,'APS-MySQL-主库','mysql','127.0.0.1',3306,'root','WWWwww123!','aps','utf8mb4','Asia/Shanghai',0,NULL,10,30,'{}',1,1,'主要业务数据库，包含订单、生产计划、设备状态等核心数据','2025-06-18 18:13:58','2025-06-29 12:03:47','2025-06-19 02:39:33','failed','(1045, \"Access denied for user \'root\'@\'localhost\' (using password: NO)\")'),(2,'APS-MySQL-系统库','mysql','127.0.0.1',3306,'root','WWWwww123!','aps_system','utf8mb4','Asia/Shanghai',0,NULL,5,30,'{}',0,1,'系统配置数据库，包含用户权限、系统设置、菜单配置等','2025-06-18 18:13:58','2025-06-29 12:03:18','2025-06-19 02:39:33','failed','(1045, \"Access denied for user \'root\'@\'localhost\' (using password: NO)\")');
/*!40000 ALTER TABLE `database_configs` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-29 21:13:39

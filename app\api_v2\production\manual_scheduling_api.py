#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动排产API接口
对接智能排产算法提供前端调用
"""

import logging
import time
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

logger = logging.getLogger(__name__)

def _convert_priority_to_int(priority_value) -> int:
    """
    将优先级值转换为整数
    
    Args:
        priority_value: 优先级值（可能是字符串、数字或None）
        
    Returns:
        int: 转换后的整数优先级（默认为1）
    """
    if priority_value is None:
        return 1
        
    if isinstance(priority_value, (int, float)):
        return int(priority_value)
        
    if isinstance(priority_value, str):
        priority_str = priority_value.strip()
        if not priority_str or priority_str.lower() in ['n', 'none', '']:
            return 1
        try:
            return int(float(priority_str))
        except (ValueError, TypeError):
            return 1
            
    return 1

# 创建蓝图
manual_scheduling_api = Blueprint('manual_scheduling_api', __name__)

@manual_scheduling_api.route('/api/v2/production/execute-manual-scheduling', methods=['POST'])
@login_required
def execute_manual_scheduling():
    """
    执行智能排产算法
    
    数据输入源:
    - ET_WAIT_LOT: 待排产批次
    - EQP_STATUS: 设备状态
    - ET_UPH_EQP: 产能数据
    - ET_FT_TEST_SPEC: 测试规范
    - et_recipe_file: 设备配方文件
    
    请求体:
    {
        "algorithm": "intelligent|deadline|product|value",
        "optimization_target": "balanced|makespan|efficiency",
        "auto_mode": false,
        "time_limit": 30,
        "population_size": 100
    }
    
    返回:
    {
        "success": true,
        "message": "排产完成",
        "schedule": [...],
        "metrics": {...},
        "execution_time": 2.5
    }
    
    输出目标: lotprioritydone表（已排产批次）
    """
    # 关键修复：将服务导入和初始化移入函数内部，打破循环导入
    from app.services import RealSchedulingService, DataSourceManager
    from sqlalchemy import text
    from app import db
    
    try:
        start_time = time.time()
        data = request.get_json()
        
        # 提取参数
        algorithm = data.get('algorithm', 'intelligent')
        optimization_target = data.get('optimization_target', 'balanced')
        auto_mode = data.get('auto_mode', False)
        
        logger.info(f"🚀 开始智能排产 - 策略: {algorithm}, 目标: {optimization_target}")
        
        # 获取当前用户ID
        user_id = current_user.username if current_user.is_authenticated else 'system'
        
        # 初始化服务
        rs = RealSchedulingService()
        manager = DataSourceManager()
        
        # 1. 获取待排产批次
        logger.info("📋 获取待排产批次数据...")
        wait_lots_result = manager.get_table_data('ET_WAIT_LOT', per_page=None)
        if not wait_lots_result.get('success'):
            return jsonify({
                'success': False,
                'message': '无法获取待排产数据',
                'schedule': []
            }), 400
            
        wait_lots = wait_lots_result.get('data', [])
        logger.info(f"📊 待排产批次: {len(wait_lots)} 个")
        
        if not wait_lots:
            return jsonify({
                'success': False,
                'message': '没有找到待排产批次',
                'schedule': []
            }), 400
        
        # 2. 调用真正的排产算法
        logger.info(f"🧠 执行排产算法 - 策略: {algorithm}")
        
        try:
            # 🔧 修复：execute_real_scheduling方法不接受user_id参数
            scheduled_lots = rs.execute_real_scheduling(algorithm)
            logger.info(f"✅ 排产算法执行完成，生成 {len(scheduled_lots)} 条记录")
        except Exception as e:
            logger.error(f"❌ 排产算法执行失败: {e}", exc_info=True) # 添加exc_info获取完整追溯
            return jsonify({
                'success': False,
                'message': f'排产算法核心服务失败: {str(e)}',
                'schedule': []
            }), 500
        
        # 3. 保存到数据库
        if scheduled_lots:
            logger.info(f"💾 保存 {len(scheduled_lots)} 条排产记录到数据库...")
            try:
                # 清空现有记录
                db.session.execute(text("DELETE FROM lotprioritydone"))
                
                # 插入新记录 - 包含所有字段，特别是排产计算结果字段
                for lot in scheduled_lots:
                    insert_sql = text("""
                        INSERT INTO lotprioritydone (
                            HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, 
                            DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, WIP_STATE, 
                            PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER, 
                            RELEASE_TIME, FAC_ID, CREATE_TIME, PRIORITY,
                            match_type, comprehensive_score, processing_time, 
                            changeover_time, algorithm_version, priority_score, 
                            estimated_hours, equipment_status
                        ) VALUES (
                            :HANDLER_ID, :LOT_ID, :LOT_TYPE, :GOOD_QTY, :PROD_ID,
                            :DEVICE, :CHIP_ID, :PKG_PN, :PO_ID, :STAGE, :WIP_STATE,
                            :PROC_STATE, :HOLD_STATE, :FLOW_ID, :FLOW_VER,
                            :RELEASE_TIME, :FAC_ID, :CREATE_TIME, :PRIORITY,
                            :match_type, :comprehensive_score, :processing_time,
                            :changeover_time, :algorithm_version, :priority_score,
                            :estimated_hours, :equipment_status
                        )
                    """)
                    
                    # 🔧 修复：确保数据字段安全映射，避免KeyError
                    insert_data = {
                        'HANDLER_ID': lot.get('HANDLER_ID', ''),
                        'LOT_ID': lot.get('LOT_ID', ''),
                        'LOT_TYPE': lot.get('LOT_TYPE', 'lot_wip'),
                        'GOOD_QTY': lot.get('GOOD_QTY', 0),
                        'PROD_ID': lot.get('PROD_ID', ''),
                        'DEVICE': lot.get('DEVICE', ''),
                        'CHIP_ID': lot.get('CHIP_ID', ''),
                        'PKG_PN': lot.get('PKG_PN', ''),
                        'PO_ID': lot.get('PO_ID', ''),
                        'STAGE': lot.get('STAGE', ''),
                        'WIP_STATE': lot.get('WIP_STATE', 'QUEUE'),
                        'PROC_STATE': lot.get('PROC_STATE', 'WAIT'),
                        'HOLD_STATE': lot.get('HOLD_STATE', 'N'),
                        'FLOW_ID': lot.get('FLOW_ID', ''),
                        'FLOW_VER': lot.get('FLOW_VER', ''),
                        'RELEASE_TIME': lot.get('RELEASE_TIME'),
                        'FAC_ID': lot.get('FAC_ID', 'FAC1'),
                        'CREATE_TIME': lot.get('CREATE_TIME'),
                        'PRIORITY': _convert_priority_to_int(lot.get('PRIORITY', 1)),  # 🔧 修复：转换优先级为整数
                        # 🔧 修复：排产计算结果字段安全映射
                        'match_type': lot.get('match_type', ''),
                        'comprehensive_score': lot.get('comprehensive_score', 0),
                        'processing_time': lot.get('processing_time', 0),
                        'changeover_time': lot.get('changeover_time', 0),
                        'algorithm_version': f'v2.1-{algorithm}',  # 🔧 强制覆盖算法版本
                        'priority_score': lot.get('priority_score', lot.get('comprehensive_score', 0)),
                        'estimated_hours': lot.get('estimated_hours', lot.get('processing_time', 0)),
                        'equipment_status': lot.get('equipment_status', 'AVAILABLE')
                    }
                    
                    db.session.execute(insert_sql, insert_data)
                
                db.session.commit()
                logger.info("✅ 排产记录保存成功")
                
            except Exception as e:
                logger.error(f"❌ 保存排产记录失败: {e}")
                db.session.rollback()
                return jsonify({
                    'success': False,
                    'message': f'排产计算成功但保存失败: {str(e)}',
                    'schedule': scheduled_lots
                }), 500
        
        # 4. 计算执行时间和统计信息
        execution_time = time.time() - start_time
        
        # 策略名称映射
        strategy_names = {
            'intelligent': '智能综合策略',
            'deadline': '交期优先策略',
            'product': '产品优先策略',
            'value': '产值优先策略'
        }
        
        metrics = {
            'algorithm': algorithm,
            'strategy_name': strategy_names.get(algorithm, algorithm),
            'optimization_target': optimization_target,
            'total_batches': len(wait_lots),
            'scheduled_batches': len(scheduled_lots),
            'failed_batches': len(wait_lots) - len(scheduled_lots),  # 🔧 修复：计算失败批次数
            'success_rate': f"{len(scheduled_lots)/len(wait_lots)*100:.1f}%" if wait_lots else "0%",
            'user_id': user_id
        }
        
        logger.info(f"🎉 智能排产完成 - 成功: {len(scheduled_lots)}/{len(wait_lots)}, 耗时: {execution_time:.2f}s")
        
        # 5. 返回结果
        return jsonify({
            'success': True,
            'message': f'智能排产完成，成功排产 {len(scheduled_lots)} 个批次',
            'schedule': scheduled_lots,
            'metrics': metrics,
            'execution_time': execution_time,
            'failed_lots': []  # 🔧 修复：failed_lots已移除
        })
        
    except Exception as e:
        logger.error(f"❌ 智能排产API异常: {e}", exc_info=True) # 添加exc_info
        return jsonify({
            'success': False,
            'message': f'排产执行失败: {str(e)}',
            'schedule': []
        }), 500

@manual_scheduling_api.route('/api/production/save-priority-done', methods=['POST'])
@login_required
def save_priority_done():
    """
    保存排产结果到已排产表
    (实际上在execute_manual_scheduling中已经自动保存了)
    """
    try:
        data = request.get_json()
        records = data.get('records', [])
        
        logger.info(f"📝 收到保存排产结果请求 - {len(records)} 条记录")
        
        # 这里只是模拟保存成功，实际保存在排产过程中已完成
        return jsonify({
            'success': True,
            'message': f'已保存 {len(records)} 条排产记录到 lotprioritydone 表',
            'saved_count': len(records)
        })
        
    except Exception as e:
        logger.error(f"❌ 保存排产结果异常: {e}")
        return jsonify({
            'success': False,
            'message': f'保存失败: {str(e)}'
        }), 500

@manual_scheduling_api.route('/api/production/schedule-status', methods=['GET'])
@login_required
def get_schedule_status():
    """获取排产状态"""
    try:
        # 检查是否有排产记录
        from sqlalchemy import text
        from app import db
        
        result = db.session.execute(text("SELECT COUNT(*) FROM lotprioritydone"))
        count = result.scalar()
        
        return jsonify({
            'success': True,
            'has_schedule': count > 0,
            'total_records': count,
            'last_updated': '2025-06-25 10:30:00'  # 可以从数据库获取实际时间
        })
        
    except Exception as e:
        logger.error(f"❌ 获取排产状态异常: {e}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500

@manual_scheduling_api.route('/api/production/clear-schedule', methods=['POST'])
@login_required
def clear_schedule():
    """清空排产结果"""
    try:
        from sqlalchemy import text
        from app import db
        
        # 清空已排产表
        db.session.execute(text("DELETE FROM lotprioritydone"))
        db.session.commit()
        
        logger.info("🗑️ 已清空排产结果")
        
        return jsonify({
            'success': True,
            'message': '已清空所有排产记录'
        })
        
    except Exception as e:
        logger.error(f"❌ 清空排产结果异常: {e}")
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        }), 500 
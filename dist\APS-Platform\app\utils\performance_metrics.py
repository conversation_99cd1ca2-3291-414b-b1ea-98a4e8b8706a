"""
性能指标收集器
"""
import time
import psutil
import threading
from collections import deque, defaultdict
from datetime import datetime, timedelta

class PerformanceMetrics:
    def __init__(self):
        self.metrics = defaultdict(deque)
        self.max_history = 1000  # 保留最近1000个数据点
        self.lock = threading.Lock()
        self.start_time = time.time()
        self.active_users = set()  # 活跃用户集合
        self.concurrent_requests = 0  # 当前并发请求数
    
    def record_request(self, endpoint, duration, status_code):
        """记录请求指标"""
        with self.lock:
            timestamp = time.time()
            self.metrics['requests'].append({
                'timestamp': timestamp,
                'endpoint': endpoint,
                'duration': duration,
                'status_code': status_code
            })
            
            # 限制历史数据量
            if len(self.metrics['requests']) > self.max_history:
                self.metrics['requests'].popleft()
    
    def record_database_query(self, query_type, duration, table=None):
        """记录数据库查询指标"""
        with self.lock:
            self.metrics['db_queries'].append({
                'timestamp': time.time(),
                'type': query_type,
                'duration': duration,
                'table': table
            })
            
            if len(self.metrics['db_queries']) > self.max_history:
                self.metrics['db_queries'].popleft()
    
    def record_user_activity(self, user_id):
        """记录用户活动"""
        with self.lock:
            self.active_users.add(user_id)
            # 清理5分钟前的用户记录
            current_time = time.time()
            if not hasattr(self, '_last_user_cleanup') or current_time - self._last_user_cleanup > 300:
                self._cleanup_inactive_users()
                self._last_user_cleanup = current_time
    
    def start_request(self):
        """开始请求计数"""
        with self.lock:
            self.concurrent_requests += 1
    
    def end_request(self):
        """结束请求计数"""
        with self.lock:
            self.concurrent_requests = max(0, self.concurrent_requests - 1)
    
    def _cleanup_inactive_users(self):
        """清理非活跃用户（内部方法）"""
        # 简单实现：每次清理时重置用户集合
        # 在实际应用中，可以基于最后活动时间进行更精确的清理
        if len(self.active_users) > 500:  # 如果用户数过多，进行清理
            self.active_users.clear()
    
    def get_current_stats(self):
        """获取当前统计信息"""
        with self.lock:
            now = time.time()
            one_minute_ago = now - 60
            
            # 计算最近1分钟的请求统计
            recent_requests = [
                r for r in self.metrics['requests'] 
                if r['timestamp'] > one_minute_ago
            ]
            
            request_count = len(recent_requests)
            avg_response_time = (
                sum(r['duration'] for r in recent_requests) / request_count
                if request_count > 0 else 0
            )
            
            error_count = sum(
                1 for r in recent_requests 
                if r['status_code'] >= 400
            )
            error_rate = (error_count / request_count * 100) if request_count > 0 else 0
            
            # 系统资源统计
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'uptime': now - self.start_time,
                'requests_per_minute': request_count,
                'avg_response_time': round(avg_response_time * 1000, 2),  # 转换为毫秒
                'error_rate': round(error_rate, 2),
                'concurrent_users': len(self.active_users),  # 并发用户数
                'concurrent_requests': self.concurrent_requests,  # 并发请求数
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_mb': round(memory.used / 1024 / 1024, 2),
                'memory_total_mb': round(memory.total / 1024 / 1024, 2)
            }
    
    def get_historical_data(self, hours=1):
        """获取历史数据"""
        with self.lock:
            cutoff_time = time.time() - (hours * 3600)
            
            historical_requests = [
                r for r in self.metrics['requests']
                if r['timestamp'] > cutoff_time
            ]
            
            # 按5分钟间隔聚合数据
            intervals = defaultdict(list)
            for request in historical_requests:
                interval = int(request['timestamp'] // 300) * 300  # 5分钟间隔
                intervals[interval].append(request)
            
            result = []
            for interval_start in sorted(intervals.keys()):
                interval_requests = intervals[interval_start]
                result.append({
                    'timestamp': interval_start,
                    'request_count': len(interval_requests),
                    'avg_response_time': sum(r['duration'] for r in interval_requests) / len(interval_requests),
                    'error_count': sum(1 for r in interval_requests if r['status_code'] >= 400)
                })
            
            return result

# 全局性能指标实例
performance_metrics = PerformanceMetrics()

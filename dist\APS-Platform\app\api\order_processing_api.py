#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单处理API
支持邮箱附件抓取、解析和处理的完整功能

Author: AI Assistant  
Date: 2025-01-25
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
import logging
import threading
import time
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from app import db
from app.models import EmailConfig, EmailAttachment, OrderData
from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
from app.services.enhanced_excel_parser import EnhancedExcelParser

logger = logging.getLogger(__name__)

# 创建蓝图
order_processing_bp = Blueprint('order_processing', __name__)

# 全局任务状态管理
processing_tasks = {}
task_lock = threading.Lock()

class ProcessingTask:
    def __init__(self, task_id: str, user_id: int, mode: str):
        self.task_id = task_id
        self.user_id = user_id
        self.mode = mode
        self.status = 'pending'  # pending, running, paused, completed, failed
        self.progress = {'overall': 0, 'step': 0, 'message': '准备中...'}
        self.start_time = datetime.now()
        self.end_time = None
        self.results = {}
        self.error_message = None
        self.stop_requested = False
        self.pause_requested = False

    def to_dict(self):
        return {
            'task_id': self.task_id,
            'user_id': self.user_id,
            'mode': self.mode,
            'status': self.status,
            'progress': self.progress,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'results': self.results,
            'error_message': self.error_message
        }

@order_processing_bp.route('/api/order_data/start', methods=['POST'])
@login_required
def start_order_processing():
    """启动订单处理任务"""
    try:
        data = request.get_json() or {}
        
        # 获取参数
        mode = data.get('mode', 'auto')  # auto, step
        email_config_ids = data.get('email_configs', [])
        parse_settings = data.get('parse_settings', {})
        
        logger.info(f"用户 {current_user.username} 启动订单处理任务")
        logger.info(f"参数: mode={mode}, email_configs={email_config_ids}")
        
        # 验证邮箱配置
        if not email_config_ids:
            configs = EmailConfig.query.filter_by(enabled=True).all()
            if not configs:
                return jsonify({
                    'success': False,
                    'error': '没有启用的邮箱配置'
                }), 400
            email_config_ids = [c.id for c in configs]
        else:
            configs = EmailConfig.query.filter(
                EmailConfig.id.in_(email_config_ids),
                EmailConfig.enabled == True
            ).all()
            if not configs:
                return jsonify({
                    'success': False,
                    'error': '没有找到有效的邮箱配置'
                }), 400
        
        # 生成任务ID
        task_id = f"order_proc_{int(time.time())}_{current_user.id}"
        
        # 创建任务对象
        task = ProcessingTask(task_id, current_user.id, mode)
        
        with task_lock:
            processing_tasks[task_id] = task
        
        # 在后台线程中启动处理
        def run_processing():
            try:
                # 获取当前应用实例和数据库连接
                from flask import current_app
                from app import create_app
                
                # 重新创建应用上下文
                app, _ = create_app()
                with app.app_context():
                    _execute_processing_task(task, configs, parse_settings)
            except Exception as e:
                logger.error(f"订单处理任务执行出错: {e}")
                task.status = 'failed'
                task.error_message = str(e)
                task.end_time = datetime.now()
        
        processing_thread = threading.Thread(target=run_processing, daemon=True)
        processing_thread.start()
        
        return jsonify({
            'success': True,
            'message': '订单处理任务已启动',
            'task_id': task_id
        })
        
    except Exception as e:
        logger.error(f"启动订单处理任务失败: {e}")
        return jsonify({
            'success': False,
            'error': f'启动失败: {str(e)}'
        }), 500

@order_processing_bp.route('/api/order_data/<task_id>/pause', methods=['POST'])
@login_required
def pause_processing(task_id):
    """暂停处理任务"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404
            
            if task.user_id != current_user.id:
                return jsonify({
                    'success': False,
                    'error': '无权限操作该任务'
                }), 403
            
            if task.status != 'running':
                return jsonify({
                    'success': False,
                    'error': '任务未在运行中'
                }), 400
            
            task.pause_requested = True
            task.status = 'paused'
            
        logger.info(f"用户 {current_user.username} 暂停任务 {task_id}")
        
        return jsonify({
            'success': True,
            'message': '任务已暂停'
        })
        
    except Exception as e:
        logger.error(f"暂停任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@order_processing_bp.route('/api/order_data/<task_id>/stop', methods=['POST'])
@login_required
def stop_processing(task_id):
    """停止处理任务"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404
            
            if task.user_id != current_user.id:
                return jsonify({
                    'success': False,
                    'error': '无权限操作该任务'
                }), 403
            
            task.stop_requested = True
            if task.status == 'running':
                task.status = 'completed'
                task.end_time = datetime.now()
            
        logger.info(f"用户 {current_user.username} 停止任务 {task_id}")
        
        return jsonify({
            'success': True,
            'message': '任务已停止'
        })
        
    except Exception as e:
        logger.error(f"停止任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@order_processing_bp.route('/api/order_data/<task_id>/status', methods=['GET'])
@login_required
def get_task_status(task_id):
    """获取任务状态"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': task.to_dict()
            })
            
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@order_processing_bp.route('/api/order_data/stats', methods=['GET'])
@login_required
def get_processing_stats():
    """获取处理统计信息"""
    try:
        # 获取基本统计
        try:
            total_orders = OrderData.query.count()
            recent_orders = OrderData.query.filter(
                OrderData.created_at >= datetime.now() - timedelta(days=7)
            ).count()
        except Exception as e:
            logger.warning(f"获取订单统计失败: {e}")
            total_orders = 0
            recent_orders = 0
        
        # 获取附件统计
        try:
            total_attachments = EmailAttachment.query.count()
            processed_attachments = EmailAttachment.query.filter_by(processed=True).count()
        except Exception as e:
            logger.warning(f"获取附件统计失败: {e}")
            total_attachments = 0
            processed_attachments = 0
        
        # 获取任务统计
        with task_lock:
            task_stats = {
                'total': len(processing_tasks),
                'running': len([t for t in processing_tasks.values() if t.status == 'running']),
                'completed': len([t for t in processing_tasks.values() if t.status == 'completed']),
                'failed': len([t for t in processing_tasks.values() if t.status == 'failed'])
            }
        
        return jsonify({
            'success': True,
            'data': {
                'orders': {
                    'total': total_orders,
                    'recent': recent_orders
                },
                'attachments': {
                    'total': total_attachments,
                    'processed': processed_attachments,
                    'pending': total_attachments - processed_attachments
                },
                'tasks': task_stats,
                'timestamp': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _execute_processing_task(task: ProcessingTask, configs: List[EmailConfig], parse_settings: dict):
    """执行处理任务的核心逻辑 - 简单直接的处理方式"""
    try:
        task.status = 'running'
        task.progress = {'overall': 0, 'step': 0, 'message': '开始处理...'}
        
        logger.info(f"开始执行订单处理任务: {task.task_id}")
        
        # 发送任务开始消息
        try:
            from app import socketio
            socketio.emit('task_progress', {
                'task_id': task.task_id,
                'progress': 0,
                'step_progress': 0,
                'message': '开始处理...',
                'status': 'running'
            })
            socketio.emit('log', {
                'level': 'info',
                'message': f'开始执行订单处理任务: {task.task_id}'
            })
        except Exception as e:
            logger.error(f"Socket.IO发送失败: {e}")
        
        total_configs = len(configs)
        processed_configs = 0
        total_attachments = 0
        processed_files = 0
        
        for i, config in enumerate(configs):
            if task.stop_requested:
                logger.info("收到停止请求，终止处理")
                break
                
            # 更新进度
            config_progress = int((i / total_configs) * 80)  # 80%用于邮件处理
            task.progress = {
                'overall': config_progress,
                'step': 0,
                'message': f'处理邮箱配置: {config.name}'
            }
            
            logger.info(f"📧 开始处理邮箱配置: {config.name} ({config.email})")
            
            try:
                # 使用高性能邮件处理器
                task.progress['message'] = f'🚀 高性能邮件处理: {config.email}'
                logger.info("🚀 使用高性能邮件处理器...")
                
                processor = HighPerformanceEmailProcessor(config)
                
                # 设置进度回调
                def progress_callback(data):
                    # 更新任务进度
                    current_config_progress = int((i / total_configs) * 80)
                    email_progress = int(data.get('progress', 0) * 0.8)  # 邮件处理占80%
                    overall_progress = current_config_progress + email_progress
                    
                    task.progress = {
                        'overall': min(overall_progress, 80),
                        'step': data.get('progress', 0),
                        'message': f"🚀 {data['message']}"
                    }
                    
                    # 发送Socket.IO消息到前端
                    try:
                        from app import socketio
                        progress_data = {
                            'task_id': task.task_id,
                            'progress': task.progress['overall'],
                            'step_progress': task.progress['step'],
                            'message': task.progress['message'],
                            'status': task.status
                        }
                        logger.info(f"📡 发送进度更新: {progress_data}")
                        socketio.emit('task_progress', progress_data)
                        
                        # 同时发送日志消息
                        socketio.emit('log', {
                            'level': 'info',
                            'message': task.progress['message']
                        })
                    except Exception as e:
                        logger.error(f"Socket.IO发送失败: {e}")
                
                processor.set_progress_callback(progress_callback)
                
                # 快速处理邮件（使用配置的天数）
                fetch_days = config.fetch_days or 3
                logger.info(f"📅 抓取最近 {fetch_days} 天的邮件")
                start_time = time.time()
                fetch_result = processor.process_fast(days=fetch_days)
                process_time = time.time() - start_time
                
                if fetch_result.get('success'):
                    downloaded = fetch_result.get('processed', 0)
                    skipped = fetch_result.get('skipped', 0)
                    total_emails = fetch_result.get('total', 0)
                    
                    logger.info(f"📊 高性能处理结果:")
                    logger.info(f"   - 处理邮件: {total_emails} 封")
                    logger.info(f"   - 下载附件: {downloaded} 个")
                    logger.info(f"   - 跳过文件: {skipped} 个")
                    logger.info(f"   - 处理时间: {process_time:.2f}秒")
                    logger.info(f"   - 处理速度: {total_emails/process_time:.1f} 邮件/秒")
                    
                    total_attachments += downloaded
                    
                else:
                    logger.error(f"❌ 高性能处理失败: {fetch_result.get('error', '未知错误')}")
                    # 不再使用fallback处理器，直接记录错误
                
                logger.info("✅ 邮件处理完成")
                
                processed_configs += 1
                
            except Exception as e:
                logger.error(f"处理邮箱配置出错: {e}")
                continue
        
        # Excel解析阶段
        task.progress = {'overall': 80, 'step': 0, 'message': '开始解析Excel文件...'}
        logger.info("📊 开始Excel文件解析阶段...")
        
        try:
            # 获取未处理的附件
            unprocessed_attachments = EmailAttachment.query.filter_by(processed=False).all()
            logger.info(f"找到 {len(unprocessed_attachments)} 个未处理的附件")
            
            if unprocessed_attachments:
                excel_parser = EnhancedExcelParser(downloads_dir="downloads", search_subdirs=True)
                
                for j, attachment in enumerate(unprocessed_attachments[:10]):  # 限制处理10个文件
                    if task.stop_requested:
                        break
                        
                    progress = 80 + int((j / min(len(unprocessed_attachments), 10)) * 15)
                    task.progress = {
                        'overall': progress,
                        'step': 0,
                        'message': f'解析文件: {attachment.filename}'
                    }
                    
                    logger.info(f"📄 解析文件: {attachment.filename}")
                    
                    try:
                        if attachment.file_path and os.path.exists(attachment.file_path):
                            # 解析Excel文件
                            result = excel_parser.parse_file(attachment.file_path)
                            
                            if result and result.get('success'):
                                processed_files += 1
                                attachment.processed = True
                                attachment.process_date = datetime.now()
                                attachment.process_result = 'success'
                                db.session.commit()
                                logger.info(f"✅ 文件解析成功: {attachment.filename}")
                            else:
                                logger.warning(f"⚠️ 文件解析失败: {attachment.filename}")
                                
                    except Exception as e:
                        logger.error(f"解析文件出错 {attachment.filename}: {e}")
        
        except Exception as e:
            logger.error(f"Excel解析阶段出错: {e}")
        
        # 任务完成
        task.status = 'completed'
        task.end_time = datetime.now()
        task.progress = {
            'overall': 100,
            'step': 100,
            'message': '处理完成'
        }
        
        task.results = {
            'processed_configs': processed_configs,
            'total_attachments': total_attachments,
            'processed_files': processed_files,
            'duration': (task.end_time - task.start_time).total_seconds()
        }
        
        logger.info(f"🎉 订单处理任务完成: {task.results}")
        
        # 发送任务完成消息
        try:
            from app import socketio
            socketio.emit('task_progress', {
                'task_id': task.task_id,
                'progress': 100,
                'step_progress': 100,
                'message': '处理完成',
                'status': 'completed'
            })
            socketio.emit('task_complete', {
                'task_id': task.task_id,
                'results': task.results,
                'message': '订单处理任务完成'
            })
            socketio.emit('log', {
                'level': 'success',
                'message': f'🎉 订单处理任务完成: {task.results}'
            })
        except Exception as e:
            logger.error(f"Socket.IO发送失败: {e}")
        
    except Exception as e:
        logger.error(f"执行处理任务出错: {e}")
        task.status = 'failed'
        task.error_message = str(e)
        task.end_time = datetime.now() 
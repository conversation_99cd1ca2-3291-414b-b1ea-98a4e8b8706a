"""
统一数据库配置管理模型
支持MySQL、PostgreSQL等多种数据库类型的统一管理
"""

from app import db
from datetime import datetime
import json
from cryptography.fernet import Fernet
import os
import base64

class DatabaseConfig(db.Model):
    """统一数据库配置表"""
    __tablename__ = 'database_configs'
    __bind_key__ = 'system'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='配置名称/别名')
    db_type = db.Column(db.String(20), nullable=False, comment='数据库类型: mysql, postgresql, sqlite')
    host = db.Column(db.String(255), nullable=False, comment='主机地址')
    port = db.Column(db.Integer, nullable=False, comment='端口')
    username = db.Column(db.String(100), nullable=False, comment='用户名')
    password_encrypted = db.Column(db.Text, comment='加密的密码')
    database_name = db.Column(db.String(100), nullable=False, comment='数据库名称')
    charset = db.Column(db.String(20), default='utf8mb4', comment='字符集')
    timezone = db.Column(db.String(50), default='Asia/Shanghai', comment='时区')
    ssl_enabled = db.Column(db.Boolean, default=False, comment='是否启用SSL')
    ssl_cert_path = db.Column(db.String(500), comment='SSL证书路径')
    connection_pool_size = db.Column(db.Integer, default=10, comment='连接池大小')
    connection_timeout = db.Column(db.Integer, default=30, comment='连接超时(秒)')
    extra_params = db.Column(db.Text, comment='额外参数JSON')
    is_default = db.Column(db.Boolean, default=False, comment='是否为默认配置')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    description = db.Column(db.Text, comment='描述')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    last_test_at = db.Column(db.DateTime, comment='最后测试时间')
    last_test_status = db.Column(db.String(20), comment='最后测试状态: success, failed')
    last_test_message = db.Column(db.Text, comment='最后测试消息')
    
    def __init__(self, **kwargs):
        # 处理密码加密
        if 'password' in kwargs:
            self.set_password(kwargs.pop('password'))
        super().__init__(**kwargs)
    
    @property
    def encryption_key(self):
        """获取加密密钥"""
        key = os.environ.get('DB_ENCRYPTION_KEY')
        if not key:
            # 生成默认密钥（生产环境应该使用环境变量）
            key = base64.urlsafe_b64encode(b'APS-Database-Config-Key-2025')[:32]
            key = base64.urlsafe_b64encode(key)
        return key.encode() if isinstance(key, str) else key
    
    def set_password(self, password):
        """加密并设置密码"""
        if password:
            fernet = Fernet(self.encryption_key)
            encrypted_password = fernet.encrypt(password.encode())
            self.password_encrypted = base64.urlsafe_b64encode(encrypted_password).decode()
    
    def get_password(self):
        """解密并获取密码"""
        if not self.password_encrypted:
            return None
        try:
            fernet = Fernet(self.encryption_key)
            encrypted_password = base64.urlsafe_b64decode(self.password_encrypted.encode())
            return fernet.decrypt(encrypted_password).decode()
        except Exception:
            return None
    
    @property
    def password(self):
        """密码属性（只读）"""
        return self.get_password()
    
    def get_connection_string(self):
        """生成数据库连接字符串"""
        password = self.get_password()
        
        if self.db_type == 'mysql':
            return f"mysql+pymysql://{self.username}:{password}@{self.host}:{self.port}/{self.database_name}?charset={self.charset}"
        elif self.db_type == 'postgresql':
            return f"postgresql://{self.username}:{password}@{self.host}:{self.port}/{self.database_name}"
        elif self.db_type == 'sqlite':
            return f"sqlite:///{self.database_name}"
        else:
            raise ValueError(f"不支持的数据库类型: {self.db_type}")
    
    def get_connection_params(self):
        """获取连接参数字典"""
        password = self.get_password()
        base_params = {
            'host': self.host,
            'port': self.port,
            'user': self.username,
            'password': password,
            'database': self.database_name,
            'charset': self.charset,
        }
        
        # 添加额外参数
        if self.extra_params:
            try:
                extra = json.loads(self.extra_params)
                base_params.update(extra)
            except:
                pass
        
        return base_params
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            if self.db_type == 'mysql':
                import pymysql
                conn = pymysql.connect(**self.get_connection_params())
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                conn.close()
                
                self.last_test_at = datetime.utcnow()
                self.last_test_status = 'success'
                self.last_test_message = '连接成功'
                return True, '连接成功'
                
            elif self.db_type == 'postgresql':
                import psycopg2
                conn = psycopg2.connect(**self.get_connection_params())
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                conn.close()
                
                self.last_test_at = datetime.utcnow()
                self.last_test_status = 'success'
                self.last_test_message = '连接成功'
                return True, '连接成功'
                
            else:
                raise ValueError(f"不支持的数据库类型: {self.db_type}")
                
        except Exception as e:
            self.last_test_at = datetime.utcnow()
            self.last_test_status = 'failed'
            self.last_test_message = str(e)
            return False, str(e)
    
    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'name': self.name,
            'db_type': self.db_type,
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'database_name': self.database_name,
            'charset': self.charset,
            'timezone': self.timezone,
            'ssl_enabled': self.ssl_enabled,
            'ssl_cert_path': self.ssl_cert_path,
            'connection_pool_size': self.connection_pool_size,
            'connection_timeout': self.connection_timeout,
            'extra_params': json.loads(self.extra_params) if self.extra_params else {},
            'is_default': self.is_default,
            'is_active': self.is_active,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_test_at': self.last_test_at.isoformat() if self.last_test_at else None,
            'last_test_status': self.last_test_status,
            'last_test_message': self.last_test_message
        }
        
        if include_sensitive:
            data['password'] = self.get_password()
        
        return data
    
    @classmethod
    def get_default_config(cls, db_type='mysql'):
        """获取默认配置"""
        return cls.query.filter_by(db_type=db_type, is_default=True, is_active=True).first()
    
    @classmethod
    def get_active_configs(cls):
        """获取所有活跃配置"""
        return cls.query.filter_by(is_active=True).order_by(cls.is_default.desc(), cls.name).all()
    
    def __repr__(self):
        return f'<DatabaseConfig {self.name}({self.db_type})>'


class DatabaseMapping(db.Model):
    """数据库映射表 - 定义哪些表/模块使用哪个数据库配置"""
    __tablename__ = 'database_mappings'
    __bind_key__ = 'system'
    
    id = db.Column(db.Integer, primary_key=True)
    mapping_type = db.Column(db.String(20), nullable=False, comment='映射类型: table, module, schema')
    target_name = db.Column(db.String(100), nullable=False, comment='目标名称（表名/模块名/架构名）')
    database_config_id = db.Column(db.Integer, db.ForeignKey('database_configs.id'), nullable=False)
    priority = db.Column(db.Integer, default=100, comment='优先级，数值越小优先级越高')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    description = db.Column(db.Text, comment='描述')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联
    database_config = db.relationship('DatabaseConfig', backref='mappings')
    
    def to_dict(self):
        return {
            'id': self.id,
            'mapping_type': self.mapping_type,
            'target_name': self.target_name,
            'database_config_id': self.database_config_id,
            'database_config_name': self.database_config.name if self.database_config else None,
            'priority': self.priority,
            'is_active': self.is_active,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_config_for_table(cls, table_name):
        """根据表名获取数据库配置"""
        mapping = cls.query.filter_by(
            mapping_type='table',
            target_name=table_name,
            is_active=True
        ).order_by(cls.priority).first()
        
        if mapping:
            return mapping.database_config
        
        # 如果没有特定映射，返回默认配置
        return DatabaseConfig.get_default_config()
    
    def __repr__(self):
        return f'<DatabaseMapping {self.target_name} -> {self.database_config.name if self.database_config else "None"}>' 
{% extends "resources/base_resource.html" %}

{% set page_title = "已排产批次" %}
{% set table_title = "已排产批次" %}
{% set page_description = "管理已排产批次信息，包含批次号、产品名称、工序、数量、排产时间、完成率等。支持筛选、编辑、删除等操作。" %}
{% set table_name = "lotprioritydone" %}
{% set api_endpoint = "/api/v2/resources" %}

{% block extra_css %}
{{ super() }}
<style>
/* 已排产批次专用样式 */
.status-scheduled { background-color: #d1ecf1; color: #0c5460; }
.status-running { background-color: #d4edda; color: #155724; }
.status-completed { background-color: #d1e7dd; color: #0f5132; }
.status-cancelled { background-color: #f8d7da; color: #721c24; }

.priority-high { font-weight: bold; color: #dc3545; }
.priority-medium { font-weight: bold; color: #fd7e14; }
.priority-low { color: #6c757d; }

/* 批次号列样式 */
.lot-id-column {
    font-family: 'Monaco', 'Consolas', monospace;
    font-weight: bold;
}

/* 数量列右对齐 */
.quantity-column {
    text-align: right;
}

/* 完成率进度条 */
.completion-rate {
    width: 80px;
}

.completion-rate .progress {
    height: 18px;
}

.completion-high { background-color: #28a745; }
.completion-medium { background-color: #ffc107; }
.completion-low { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">{{ page_title }}</h5>
                        <div>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-info me-2" onclick="exportDoneLots()">
                                <i class="fas fa-file-excel me-1"></i>导出Excel
                            </button>
                        </div>
                    </div>
                    
                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">{{ table_title }}数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="batchMoveToWaiting()">
                                    <i class="fas fa-arrow-left me-1"></i>批量移至待排产
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<!-- 引入XLSX库 -->
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<script>
// 页面配置
const API_ENDPOINT = '{{ api_endpoint }}';
const TABLE_NAME = '{{ table_name }}';

// 移至待排产功能
function moveToWaiting() {
    if (confirm('确定要将选中的批次移至待排产吗？')) {
        // 获取选中的记录
        const selectedRows = document.querySelectorAll('#dataTable tbody input[type="checkbox"]:checked');
        if (selectedRows.length === 0) {
            alert('请选择要移动的批次');
            return;
        }
        
        const selectedIds = Array.from(selectedRows).map(row => 
            parseInt(row.closest('tr').dataset.id)
        );
        
        moveLotsToWaiting(selectedIds);
    }
}

function batchMoveToWaiting() {
    const selectedIds = getSelectedRowIds();
    if (selectedIds.length === 0) {
        alert('请选择要移动的批次');
        return;
    }
    
    if (confirm(`确定要将选中的 ${selectedIds.length} 个批次移至待排产吗？`)) {
        moveLotsToWaiting(selectedIds);
    }
}

function moveLotsToWaiting(ids) {
    // 显示加载状态
    showLoading();
    
    // 调用API将批次移至待排产
    fetch('/api/v2/production/done-lots/move-to-waiting', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ids: ids }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        hideLoading();
        
        if (result.success) {
            showNotification('success', `成功移动 ${result.moved_count} 个批次至待排产`);
            loadData(); // 刷新数据
            clearSelection();
        } else {
            showNotification('error', result.error || '移动失败');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('移动批次失败:', error);
        showNotification('error', '移动失败: ' + error.message);
    });
}

// 自定义数据格式化函数
function formatCellValue(value, column) {
    if (column === 'status') {
        const statusClass = value ? `status-${value.toLowerCase()}` : '';
        return `<span class="badge ${statusClass}">${value || ''}</span>`;
    }
    
    if (column === 'priority') {
        let priorityClass = 'priority-low';
        if (value >= 8) priorityClass = 'priority-high';
        else if (value >= 5) priorityClass = 'priority-medium';
        
        return `<span class="${priorityClass}">${value || ''}</span>`;
    }
    
    if (column === 'lot_id') {
        return `<span class="lot-id-column">${value || ''}</span>`;
    }
    
    if (column === 'quantity') {
        return `<span class="quantity-column">${value || ''}</span>`;
    }
    
    if (column === 'completion_rate') {
        const rate = parseFloat(value) || 0;
        let progressClass = 'completion-low';
        if (rate >= 80) progressClass = 'completion-high';
        else if (rate >= 50) progressClass = 'completion-medium';
        
        return `
            <div class="completion-rate">
                <div class="progress">
                    <div class="progress-bar ${progressClass}" style="width: ${rate}%">
                        ${rate.toFixed(1)}%
                    </div>
                </div>
            </div>
        `;
    }
    
    // 默认格式化
    return value || '';
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 🔧 检查URL参数，处理刷新和新记录通知
    checkForRefreshAndNewRecords();
    
    loadColumns();
    loadData();
});

// 🔧 新增：检查URL参数并处理刷新逻辑
function checkForRefreshAndNewRecords() {
    const urlParams = new URLSearchParams(window.location.search);
    const refreshParam = urlParams.get('refresh');
    const newRecordsParam = urlParams.get('new_records');
    
    if (refreshParam && newRecordsParam) {
        console.log('🔄 检测到新排产数据，强制刷新页面数据');
        
        // 显示新数据通知
        showNewRecordsNotification(parseInt(newRecordsParam));
        
        // 清理URL参数，避免重复处理
        const cleanUrl = window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);
        
        // 强制清除可能的缓存，确保获取最新数据
        setTimeout(() => {
            forceRefreshData();
        }, 500);
    }
}

// 🔧 新增：显示新记录通知
function showNewRecordsNotification(recordCount) {
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
    notification.style.cssText = `
        top: 20px; right: 20px; z-index: 9999; min-width: 350px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left: 4px solid #28a745;
    `;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle me-2 text-success"></i>
            <div>
                <strong>新排产数据已加载</strong>
                <br><small>新增 ${recordCount} 条排产记录</small>
            </div>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (document.contains(notification)) {
            notification.remove();
        }
    }, 5000);
}

// 🔧 新增：导出当前已排产批次
function exportDoneLots() {
    console.log('🔄 开始导出当前排产结果...');
    
    // 获取当前页面显示的数据
    const tableRows = document.querySelectorAll('#dataTable tbody tr');
    if (tableRows.length === 0) {
        alert('没有可导出的排产结果');
        return;
    }
    
    // 获取表头信息
    const headers = [];
    const headerCells = document.querySelectorAll('#dataTable thead th');
    headerCells.forEach(cell => {
        const text = cell.textContent.trim();
        if (text && text !== '选择') { // 排除复选框列
            headers.push(text);
        }
    });
    
    // 获取当前页面的数据
    const exportData = [];
    
    tableRows.forEach((row, index) => {
        // 跳过"没有数据"的提示行
        if (row.cells.length <= 1) return;
        
        const rowData = {};
        let cellIndex = 0;
        
        // 遍历每个单元格（跳过复选框列）
        for (let i = 1; i < row.cells.length; i++) { // 从1开始跳过复选框
            const cell = row.cells[i];
            const headerName = headers[cellIndex];
            
            if (headerName) {
                // 获取纯文本值，移除HTML标签
                let cellValue = cell.textContent.trim();
                
                // 特殊处理完成率进度条
                if (headerName.includes('完成率') || headerName.includes('completion_rate')) {
                    const progressText = cell.querySelector('.progress-bar');
                    if (progressText) {
                        cellValue = progressText.textContent.trim();
                    }
                }
                
                rowData[headerName] = cellValue;
                cellIndex++;
            }
        }
        
        if (Object.keys(rowData).length > 0) {
            exportData.push(rowData);
        }
    });
    
    if (exportData.length === 0) {
        alert('没有有效的排产数据可导出');
        return;
    }
    
    console.log(`📊 准备导出 ${exportData.length} 条排产记录`);
    
    try {
        // 使用XLSX库创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽
        const colWidths = headers.map(header => {
            if (header.includes('LOT_ID') || header.includes('批次')) return { wch: 15 };
            if (header.includes('DEVICE') || header.includes('产品')) return { wch: 20 };
            if (header.includes('HANDLER_ID') || header.includes('设备')) return { wch: 12 };
            if (header.includes('GOOD_QTY') || header.includes('数量')) return { wch: 10 };
            if (header.includes('TIME') || header.includes('时间')) return { wch: 18 };
            return { wch: 12 };
        });
        ws['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(wb, ws, "排产结果");
        
        // 生成文件名
        const now = new Date();
        const timestamp = now.getFullYear() +
                         String(now.getMonth() + 1).padStart(2, '0') +
                         String(now.getDate()).padStart(2, '0') + '_' +
                         String(now.getHours()).padStart(2, '0') +
                         String(now.getMinutes()).padStart(2, '0') +
                         String(now.getSeconds()).padStart(2, '0');
        
        const filename = `排产结果_${timestamp}.xlsx`;
        
        // 下载文件
        XLSX.writeFile(wb, filename);
        
        console.log(`✅ 排产结果导出成功: ${filename}`);
        
        // 显示成功通知
        showNewRecordsNotification(exportData.length, '排产结果导出成功！');
        
    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败: ' + error.message);
    }
}

// 🔧 修改：显示通知函数支持自定义消息
function showNewRecordsNotification(recordCount, customMessage = null) {
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
    notification.style.cssText = `
        top: 20px; right: 20px; z-index: 9999; min-width: 350px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left: 4px solid #28a745;
    `;
    
    const message = customMessage || `新增 ${recordCount} 条排产记录`;
    const title = customMessage ? '操作成功' : '新排产数据已加载';
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle me-2 text-success"></i>
            <div>
                <strong>${title}</strong>
                <br><small>${message}</small>
            </div>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (document.contains(notification)) {
            notification.remove();
        }
    }, 5000);
}

// 🔧 新增：强制刷新数据（绕过缓存）
function forceRefreshData() {
    console.log('🔄 强制刷新已排产数据...');
    
    // 添加时间戳参数防止缓存
    const originalLoadData = window.loadData;
    
    // 临时重写loadData函数添加防缓存参数
    window.loadData = function(page = 1) {
        currentPage = page;
        showLoading(true);
        
        console.log(`正在强制刷新 ${TABLE_NAME} 数据，第 ${page} 页...`);
        
        // 构建参数，添加防缓存时间戳
        const params = {
            page: page,
            per_page: pageSize,
            _t: Date.now() // 防缓存参数
        };
        
        // 添加筛选条件
        if (advancedFilters.length > 0) {
            params.advanced_filters = JSON.stringify(advancedFilters);
        }
        
        // 🔧 修复：使用专用的Done Lots API
        const url = `/api/v2/production/done-lots?` + new URLSearchParams(params);
        
        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache', // 禁用缓存
                'Pragma': 'no-cache'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log(`强制刷新API响应状态: ${response.status}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log(`强制刷新API返回数据:`, data);
            
            if (!data.success) {
                throw new Error(data.error || '数据加载失败');
            }
            
            // 处理数据
            const columns = data.columns || [];
            const rows = data.data || [];
            
            if (data.pagination) {
                totalRecords = data.pagination.total || 0;
                totalPages = data.pagination.pages || 1;
            } else {
                totalRecords = data.total_records || data.total || 0;
                totalPages = data.total_pages || 1;
            }
            
            // 更新界面
            renderTable(columns, rows);
            renderPagination();
            updateStats();
            updateFieldOptions(columns);
            
            console.log(`✅ 强制刷新成功，加载 ${rows.length} 条数据，共 ${totalRecords} 条记录`);
            
            // 恢复原始loadData函数
            window.loadData = originalLoadData;
        })
        .catch(error => {
            console.error('强制刷新数据失败:', error);
            showError('刷新数据失败: ' + error.message);
            
            // 恢复原始loadData函数
            window.loadData = originalLoadData;
        })
        .finally(() => {
            showLoading(false);
        });
    };
    
    // 执行强制刷新
    loadData(1);
}
</script>
{% endblock %} 
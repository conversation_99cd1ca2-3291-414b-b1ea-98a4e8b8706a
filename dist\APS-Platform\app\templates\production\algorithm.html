{% extends "base.html" %}

{% block title %}手动排产与优先级管理
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
    .preview-area {
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    /* 添加表格列宽控制 */
    .table th {
        white-space: nowrap;
        min-width: 100px;
        position: relative;
        padding-right: 20px;
    }
    .table td {
        white-space: nowrap;
    }
    /* 选择行样式 */
    .selectable-row {
        cursor: pointer;
    }
    .selectable-row.selected {
        background-color: #fff1f0 !important; /* 浅红色背景 */
    }
    .selectable-row:hover {
        background-color: #f8f9fa;
    }
    /* 筛选输入框样式 */
    .filter-input {
        width: 100%;
        padding: 2px 5px;
        margin: 2px 0;
        font-size: 0.875rem;
        border: 1px solid #dee2e6;
        border-radius: 3px;
    }
    /* 排序图标样式 */
    .sort-icon {
        position: absolute;
        right: 5px;
        cursor: pointer;
        color: #999;
    }
    .sort-icon.active {
        color: #b72424; /* 红色主题色 */
    }
    /* 筛选行样式 */
    .filter-row th {
        padding: 4px;
        background-color: #f8f9fa;
    }
    /* 批量操作按钮样式 */
    .batch-actions {
        margin-bottom: 10px;
    }
    .batch-actions .btn {
        margin-right: 5px;
    }
    /* 键盘快捷键提示 */
    .shortcut-hint {
        font-size: 0.8rem;
        color: #6c757d;
        margin-left: 5px;
    }
    /* 修改表格字体大小和行高 */
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    /* 固定第一列（复选框列）*/
    .table-responsive {
        position: relative;
    }
    
    .table th:first-child,
    .table td:first-child {
        position: sticky;
        left: 0;
        z-index: 2;
        background-color: #fff;
        border-right: 1px solid #dee2e6;
    }
    
    /* 当表头固定时，确保第一列表头的背景色 */
    .table th:first-child {
        background-color: #f8f9fa;
        z-index: 3;
    }
    
    /* 悬停时保持背景色 */
    .table tr:hover td:first-child {
        background-color: #f8f9fa;
    }
    
    /* 选中行时的第一列样式 */
    .table tr.selected td:first-child {
        background-color: #fff1f0;
    }
    
    /* 调整单元格内边距 */
    .table td, 
    .table th {
        padding: 0.3rem 0.5rem;
        white-space: nowrap;
    }
    
    /* 调整复选框大小 */
    .form-check-input {
        width: 0.8rem;
        height: 0.8rem;
        margin-top: 0.2rem;
    }

    /* 修改主要按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }

    /* 修改分页激活状态颜色 */
    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 修改徽章颜色 */
    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    /* 修改链接颜色 */
    a {
        color: #b72424;
    }
    a:hover {
        color: #b72424;
    }


</style>
{% endblock %}

{% block content %}
<div class="card mb-4">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <span>算法权重配置</span>
      <div class="d-flex align-items-center">
        <label for="strategySelector" class="form-label me-2 mb-0">排产策略:</label>
        <select class="form-select form-select-sm" id="strategySelector" style="width: auto;">
          <option value="intelligent">🧠 智能综合策略</option>
          <option value="deadline">📅 交期优先策略</option>
          <option value="product">📦 产品优先策略</option>
          <option value="value">💰 产值优先策略</option>
        </select>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="alert alert-info" id="strategyDescription">
      <i class="fas fa-info-circle me-2"></i>
      <span id="strategyDescText">平衡各项指标的综合策略，适用于大多数排产场景</span>
    </div>
    <form id="weights-form">
      <div class="row g-3">
        <div class="col-md-6">
          <label for="tech_match_weight" class="form-label">技术匹配度权重(%)</label>
          <input type="number" class="form-control" id="tech_match_weight" name="tech_match_weight" min="0" max="100" step="1" value="0">
          <div class="form-text">当前值: <span id="techMatchValue">0</span>%</div>
        </div>
        <div class="col-md-6">
          <label for="load_balance_weight" class="form-label">负载均衡权重(%)</label>
          <input type="number" class="form-control" id="load_balance_weight" name="load_balance_weight" min="0" max="100" step="1" value="0">
          <div class="form-text">当前值: <span id="loadBalanceValue">0</span>%</div>
        </div>
        <div class="col-md-6">
          <label for="deadline_weight" class="form-label">交期紧迫度权重(%)</label>
          <input type="number" class="form-control" id="deadline_weight" name="deadline_weight" min="0" max="100" step="1" value="0">
          <div class="form-text">当前值: <span id="deadlineValue">0</span>%</div>
        </div>
        <div class="col-md-6">
          <label for="value_efficiency_weight" class="form-label">产值效率权重(%)</label>
          <input type="number" class="form-control" id="value_efficiency_weight" name="value_efficiency_weight" min="0" max="100" step="1" value="0">
          <div class="form-text">当前值: <span id="valueEfficiencyValue">0</span>%</div>
        </div>
        <div class="col-md-6">
          <label for="business_priority_weight" class="form-label">业务优先级权重(%)</label>
          <input type="number" class="form-control" id="business_priority_weight" name="business_priority_weight" min="0" max="100" step="1" value="0">
          <div class="form-text">当前值: <span id="businessPriorityValue">0</span>%</div>
        </div>
        <div class="col-12">
          <div class="alert alert-secondary">
            <strong>权重总和: <span id="totalWeight">0</span>%</strong>
            <small class="text-muted ms-2">(必须等于100%)</small>
          </div>
        </div>
      </div>
      <div class="mt-3">
        <button type="submit" class="btn btn-primary">保存权重</button>
        <button type="button" class="btn btn-secondary ms-2" id="resetBtn">重置为默认值</button>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 加载jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(function() {
  var currentStrategy = 'intelligent';

  // 策略描述映射
  var strategyDescriptions = {
    'intelligent': '平衡各项指标的综合策略，适用于大多数排产场景',
    'deadline': '优先考虑交期紧迫度，确保按时交付',
    'product': '优先考虑技术匹配度，提高产品质量',
    'value': '优先考虑产值效率，最大化经济效益'
  };

  // 更新权重总和显示
  function updateTotalWeight() {
    var total = 0;
    ['tech_match_weight', 'load_balance_weight', 'deadline_weight', 'value_efficiency_weight', 'business_priority_weight'].forEach(function(field) {
      var value = parseFloat($('#' + field).val()) || 0;
      total += value;
    });
    $('#totalWeight').text(total.toFixed(1));

    // 更新颜色提示
    var alertDiv = $('#totalWeight').closest('.alert');
    alertDiv.removeClass('alert-secondary alert-success alert-warning alert-danger');
    if (Math.abs(total - 100) < 0.01) {
      alertDiv.addClass('alert-success');
    } else if (Math.abs(total - 100) < 5) {
      alertDiv.addClass('alert-warning');
    } else {
      alertDiv.addClass('alert-danger');
    }
  }

  // 加载指定策略的权重配置
  function loadWeights(strategy) {
    console.log('🔄 开始加载策略权重:', strategy);

    $.getJSON('/api/production/algorithm-weights?strategy=' + strategy, function(resp) {
      console.log('📡 API响应:', resp);

      if (resp.success && resp.weights) {
        var w = resp.weights;

        // 更新输入框和显示值
        $('#tech_match_weight').val(w.tech_match_weight || 0);
        $('#load_balance_weight').val(w.load_balance_weight || 0);
        $('#deadline_weight').val(w.deadline_weight || 0);
        $('#value_efficiency_weight').val(w.value_efficiency_weight || 0);
        $('#business_priority_weight').val(w.business_priority_weight || 0);

        // 更新显示值
        $('#techMatchValue').text((w.tech_match_weight || 0).toFixed(1));
        $('#loadBalanceValue').text((w.load_balance_weight || 0).toFixed(1));
        $('#deadlineValue').text((w.deadline_weight || 0).toFixed(1));
        $('#valueEfficiencyValue').text((w.value_efficiency_weight || 0).toFixed(1));
        $('#businessPriorityValue').text((w.business_priority_weight || 0).toFixed(1));

        // 更新策略描述
        $('#strategyDescText').text(strategyDescriptions[strategy] || '');

        // 更新权重总和
        updateTotalWeight();

        console.log('✅ 已加载策略权重:', strategy, {
          tech_match: w.tech_match_weight,
          load_balance: w.load_balance_weight,
          deadline: w.deadline_weight,
          value_efficiency: w.value_efficiency_weight,
          business_priority: w.business_priority_weight
        });
      } else {
        console.error('❌ API响应错误:', resp);
        alert('加载权重失败: ' + (resp.message || '未知错误'));
      }
    }).fail(function(xhr, status, error) {
      console.error('❌ API请求失败:', status, error, xhr.responseText);
      alert('网络错误，无法加载权重配置: ' + error);
    });
  }

  // 策略选择器变化事件
  $('#strategySelector').change(function() {
    currentStrategy = $(this).val();
    console.log('🔄 切换策略到:', currentStrategy);
    loadWeights(currentStrategy);
  });

  // 权重输入框变化事件
  $('input[type="number"]').on('input change', function() {
    var field = $(this).attr('id');
    var value = parseFloat($(this).val()) || 0;

    // 更新对应的显示值
    var displayId = field.replace('_weight', 'Value').replace('tech_match', 'techMatch').replace('load_balance', 'loadBalance').replace('value_efficiency', 'valueEfficiency').replace('business_priority', 'businessPriority');
    $('#' + displayId).text(value.toFixed(1));

    // 更新总和
    updateTotalWeight();
  });

  // 重置按钮事件
  $('#resetBtn').click(function() {
    if (confirm('确定要重置为当前策略的默认权重吗？')) {
      loadWeights(currentStrategy);
    }
  });

  // 初始化加载默认策略权重
  console.log('🚀 页面初始化，加载默认策略:', currentStrategy);
  loadWeights(currentStrategy);

  // 保存权重
  $('#weights-form').submit(function(e) {
    e.preventDefault();
    console.log('💾 开始保存权重配置...');

    var payload = {
      strategy: currentStrategy
    };
    $(this).serializeArray().forEach(function(item) {
      payload[item.name] = parseFloat(item.value) || 0;
    });

    console.log('📤 保存数据:', payload);

    // 验证权重总和
    var totalWeight = payload.tech_match_weight + payload.load_balance_weight +
                     payload.deadline_weight + payload.value_efficiency_weight +
                     payload.business_priority_weight;

    if (Math.abs(totalWeight - 100) > 0.01) {
      alert('权重总和必须为100%，当前为' + totalWeight.toFixed(2) + '%');
      return;
    }

    // 禁用提交按钮防止重复提交
    var submitBtn = $(this).find('button[type="submit"]');
    var originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('保存中...');

    $.ajax({
      url: '/api/production/algorithm-weights',
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify(payload),
      success: function(resp) {
        console.log('📡 保存响应:', resp);
        if (resp.success) {
          alert('✅ 策略 "' + currentStrategy + '" 的权重配置已保存');
          // 重新加载权重以确保数据一致性
          loadWeights(currentStrategy);
        } else {
          console.error('❌ 保存失败:', resp);
          alert('❌ 保存失败: ' + (resp.message || '未知错误'));
        }
      },
      error: function(xhr, status, error) {
        console.error('❌ 保存请求失败:', status, error, xhr.responseText);
        var errorMsg = '保存出错';
        try {
          var errorResp = JSON.parse(xhr.responseText);
          errorMsg = errorResp.message || errorMsg;
        } catch(e) {
          errorMsg = xhr.responseText || errorMsg;
        }
        alert('❌ ' + errorMsg);
      },
      complete: function() {
        // 恢复提交按钮
        submitBtn.prop('disabled', false).text(originalText);
      }
    });
  });
});
</script>
{% endblock %}
import imaplib
import email
import os
import datetime
import logging
from email.header import decode_header
import pandas as pd
import json
from app import db
from app.models import EmailConfig, EmailAttachment, ExcelMapping, OrderData
import socket
import ssl
import uuid
from email.utils import parsedate_to_datetime
from datetime import timedelta

# 配置日志
logger = logging.getLogger(__name__)

class EmailProcessor:
    """邮件处理器类，用于连接邮箱、获取邮件、下载附件等功能"""
    
    def __init__(self, config_id=None):
        """初始化邮件处理器
        
        参数:
            config_id: 邮箱配置ID，为None时不自动连接
        """
        self.imap = None
        self.config = None
        
        if config_id:
            self.config = EmailConfig.query.get(config_id)
            if self.config:
                self.connect()
    
    def connect(self, config=None):
        """连接到邮箱服务器
        
        参数:
            config: 邮箱配置对象，为None时使用已有配置
            
        返回:
            bool: 连接成功返回True，否则返回False
        """
        if config:
            self.config = config
        
        if not self.config:
            logger.error("未提供邮箱配置")
            return False
        
        try:
            # 断开已有连接
            if self.imap:
                try:
                    self.imap.logout()
                except:
                    pass
                self.imap = None
            
            # 创建IMAP连接
            logger.info(f"尝试连接到IMAP服务器: {self.config.server}:{self.config.port}")
            try:
                self.imap = imaplib.IMAP4_SSL(self.config.server, self.config.port)
            except ConnectionRefusedError:
                logger.error(f"连接被拒绝: {self.config.server}:{self.config.port}")
                return False
            except socket.gaierror as e:
                logger.error(f"无法解析主机名: {self.config.server}, 错误: {str(e)}")
                return False
            except ssl.SSLError as e:
                logger.error(f"SSL错误: {str(e)}")
                return False
            except TimeoutError:
                logger.error(f"连接超时")
                return False
            except Exception as e:
                logger.error(f"创建IMAP连接时出错: {str(e)}")
                return False
            
            # 登录
            logger.info(f"尝试登录邮箱: {self.config.email}")
            try:
                self.imap.login(self.config.email, self.config.password)
            except imaplib.IMAP4.error as e:
                error_msg = str(e)
                if "ERR.LOGIN.REQCODE" in error_msg:
                    logger.error(f"网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: {error_msg}")
                elif "LOGIN failed" in error_msg:
                    logger.error(f"登录失败，可能是邮箱地址或授权码错误: {error_msg}")
                elif "authentication failed" in error_msg.lower():
                    logger.error(f"认证失败，请检查邮箱地址和授权码是否正确: {error_msg}")
                else:
                    logger.error(f"IMAP登录错误: {error_msg}")
                return False
            
            # 测试连接是否正常
            try:
                list_response = self.imap.list()
                if list_response[0] != 'OK':
                    logger.error(f"无法获取邮箱文件夹列表")
                    return False
            except Exception as e:
                logger.error(f"测试连接时出错: {str(e)}")
                return False
            
            logger.info(f"成功连接到邮箱: {self.config.email}")
            return True
        
        except Exception as e:
            logger.error(f"连接邮箱失败: {type(e).__name__}: {str(e)}")
            return False
    
    def disconnect(self):
        """断开与邮箱服务器的连接"""
        if self.imap:
            try:
                self.imap.logout()
                logger.info("已断开邮箱连接")
            except Exception as e:
                logger.error(f"断开邮箱连接失败: {str(e)}")
            finally:
                self.imap = None
    
    def list_folders(self):
        """列出邮箱中的所有文件夹
        
        返回:
            list: 文件夹名称列表
        """
        if not self.imap:
            logger.error("未连接到邮箱")
            return []
        
        try:
            status, folder_list = self.imap.list()
            if status != 'OK':
                logger.error("获取文件夹列表失败")
                return []
            
            folders = []
            for folder in folder_list:
                # 解析文件夹名称
                try:
                    folder_parts = folder.decode('utf-8', errors='replace').split(' "')
                    if len(folder_parts) > 1:
                        folder_name = folder_parts[-1].strip('"')
                        folders.append(folder_name)
                    else:
                        # 处理没有引号的文件夹名称格式
                        parts = folder.decode('utf-8', errors='replace').split(' ')
                        if len(parts) >= 3:
                            folder_name = parts[-1]
                            # 移除可能的前缀或后缀
                            if folder_name.startswith('"') and folder_name.endswith('"'):
                                folder_name = folder_name[1:-1]
                            folders.append(folder_name)
                except Exception as e:
                    logger.error(f"解析文件夹名称失败: {str(e)}, 原始数据: {folder}")
            
            logger.info(f"找到以下文件夹: {', '.join(folders)}")
            return folders
        except Exception as e:
            logger.error(f"列出文件夹失败: {str(e)}")
            return []
    
    def preview_attachments(self, days=7, folder=None):
        """预览邮箱附件（不下载）
        
        参数:
            days: 获取最近多少天的邮件，默认7天
            folder: 指定文件夹，None表示搜索所有文件夹
            
        返回:
            dict: 预览结果，包含attachments列表和统计信息
        """
        if not self.imap:
            logger.error("未连接到邮箱")
            return {"attachments": [], "total_emails": 0}
        
        results = {
            "attachments": [],
            "total_emails": 0
        }
        
        try:
            # 获取要搜索的文件夹列表
            if folder:
                folders = [folder]
            else:
                folders = self.list_folders()
                if not folders:
                    logger.warning("未找到任何文件夹，使用默认收件箱")
                    folders = ['INBOX']
            
            # 计算日期范围
            since_date = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime("%d-%b-%Y")
            
            # 处理每个文件夹
            for folder_name in folders:
                try:
                    logger.info(f"开始预览文件夹: {folder_name}")
                    
                    # 选择文件夹
                    status, data = self.imap.select(folder_name)
                    if status != 'OK':
                        logger.warning(f"无法选择文件夹 '{folder_name}', 跳过")
                        continue
                    
                    # 搜索邮件
                    status, data = self.imap.search(None, f'(SINCE {since_date})')
                    if status != 'OK':
                        logger.warning(f"搜索文件夹 {folder_name} 失败")
                        continue
                    
                    message_ids = data[0].split()
                    folder_email_count = len(message_ids)
                    logger.info(f"在文件夹 '{folder_name}' 中找到 {folder_email_count} 封近期邮件")
                    results["total_emails"] += folder_email_count
                    
                    if folder_email_count == 0:
                        continue
                    
                    # 应用发件人和主题筛选
                    filtered_messages = self._apply_filters(message_ids, since_date)
                    
                    # 预览每封邮件的附件
                    for msg_id in filtered_messages:
                        try:
                            # 获取邮件内容
                            status, data = self.imap.fetch(msg_id, '(RFC822)')
                            if status != 'OK':
                                continue
                            
                            raw_email = data[0][1]
                            email_message = email.message_from_bytes(raw_email)
                            
                            # 解析邮件信息
                            sender = self._decode_header(email_message.get('From', ''))
                            subject = self._decode_header(email_message.get('Subject', ''))
                            date_str = email_message.get('Date', '')
                            
                            try:
                                receive_date = parsedate_to_datetime(date_str)
                            except:
                                receive_date = datetime.datetime.now()
                            
                            # 检查附件
                            for part in email_message.walk():
                                if part.get_content_maintype() == 'multipart':
                                    continue
                                
                                if part.get('Content-Disposition') is None:
                                    continue
                                
                                # 获取文件名
                                filename = part.get_filename()
                                if not filename:
                                    continue
                                
                                # 解码文件名
                                filename = self._decode_header(filename)
                                filename = self._sanitize_filename(filename)
                                
                                # 只处理Excel文件
                                if not filename.lower().endswith(('.xlsx', '.xls')):
                                    continue
                                
                                # 获取文件大小
                                file_size = len(part.get_payload(decode=True) or b'')
                                
                                # 添加到预览结果
                                results["attachments"].append({
                                    "filename": filename,
                                    "sender": sender,
                                    "subject": subject,
                                    "receive_date": receive_date.strftime('%Y-%m-%d %H:%M:%S'),
                                    "file_size": file_size,
                                    "folder": folder_name
                                })
                                
                        except Exception as e:
                            logger.error(f"预览邮件 {msg_id} 时出错: {str(e)}")
                            continue
                            
                except Exception as e:
                    logger.error(f"处理文件夹 '{folder_name}' 时出错: {str(e)}")
                    continue
            
            logger.info(f"预览完成：找到 {results['total_emails']} 封邮件，{len(results['attachments'])} 个Excel附件")
            return results
            
        except Exception as e:
            logger.error(f"预览邮件附件时出错: {str(e)}")
            return {"attachments": [], "total_emails": 0}
    
    def _apply_filters(self, message_ids, since_date):
        """应用发件人和主题筛选条件
        
        参数:
            message_ids: 邮件ID列表
            since_date: 日期筛选条件
            
        返回:
            list: 筛选后的邮件ID列表
        """
        filtered_messages = message_ids.copy()
        
        # 应用发件人筛选
        if self.config.senders and self.config.senders.strip():
            senders = [s.strip() for s in self.config.senders.split(';') if s.strip()]
            if senders:
                sender_filtered_messages = []
                for sender in senders:
                    status, data = self.imap.search(None, f'(SINCE {since_date} FROM "{sender}")')
                    if status == 'OK':
                        sender_filtered_messages.extend(data[0].split())
                
                sender_filtered_messages = list(set(sender_filtered_messages))
                if sender_filtered_messages:
                    filtered_messages = sender_filtered_messages
        
        # 应用主题筛选
        if self.config.subjects and self.config.subjects.strip() and filtered_messages:
            subjects = [s.strip() for s in self.config.subjects.split(';') if s.strip()]
            if subjects:
                subject_filtered_messages = []
                for msg_id in filtered_messages:
                    try:
                        status, data = self.imap.fetch(msg_id, '(RFC822.HEADER)')
                        if status != 'OK':
                            continue
                        
                        msg_data = data[0][1]
                        msg = email.message_from_bytes(msg_data)
                        subject = self._decode_header(msg['Subject'] or '')
                        
                        for subject_keyword in subjects:
                            if subject_keyword.lower() in subject.lower():
                                if msg_id not in subject_filtered_messages:
                                    subject_filtered_messages.append(msg_id)
                                break
                    except Exception as e:
                        logger.error(f"处理邮件主题筛选时出错: {str(e)}")
                        continue
                
                if subject_filtered_messages:
                    filtered_messages = subject_filtered_messages
        
        return filtered_messages

    def fetch_attachments(self, days=7, save_to_db=True, folder=None):
        """获取附件
        
        Args:
            days (int): 获取最近几天的邮件，默认7天
            save_to_db (bool): 是否保存到数据库，默认True
            folder (str): 指定IMAP文件夹，默认None表示搜索所有文件夹
            
        Returns:
            dict: 处理结果统计和详细信息
        """
        if not self.imap:
            self.connect()
            if not self.imap:
                return {
                    "total": 0,
                    "downloaded": 0,
                    "new_downloaded": 0,  # 新增属性：新下载的附件数
                    "skipped": 0,
                    "failed": 0,
                    "message": "连接邮箱失败"
                }
        
        # 处理结果统计
        results = {
            "total": 0,           # 处理的邮件总数
            "downloaded": 0,       # 成功下载的附件数
            "new_downloaded": 0,    # 新增属性：新下载的附件数
            "skipped": 0,          # 跳过的附件数
            "failed": 0,           # 下载失败的附件数
            "processed_files": [],  # 成功处理的附件详情
            "skipped_files": [],    # 跳过的附件详情
            "failed_files": []      # 失败的附件详情
        }
        
        # 获取文件夹列表 - 优化：搜索所有文件夹
        if folder == "ALL" or folder is None:
            folders = self.list_folders()
            logger.info(f"🔍 将搜索所有 {len(folders)} 个文件夹: {', '.join(folders[:5])}{'...' if len(folders) > 5 else ''}")
        else:
            folders = [folder]
            logger.info(f"🔍 将只搜索指定文件夹: {folder}")
        
        # 移除JW Order特殊处理 - 现在搜索所有文件夹
        
        # 计算日期范围 - 优化：使用配置中的fetch_days或传入的days参数
        actual_days = days
        if hasattr(self.config, 'fetch_days') and self.config.fetch_days:
            actual_days = self.config.fetch_days
        since_date = (datetime.datetime.now() - datetime.timedelta(days=actual_days)).strftime("%d-%b-%Y")
        
        # 处理每个文件夹
        for folder_name in folders:
            try:
                logger.info(f"开始搜索文件夹: {folder_name}")
                
                # 选择文件夹
                try:
                    status, data = self.imap.select(folder_name)
                    if status != 'OK':
                        logger.warning(f"无法选择文件夹 '{folder_name}', 状态: {status}, 将尝试使用引号")
                        # 尝试使用引号包裹文件夹名称
                        status, data = self.imap.select(f'"{folder_name}"')
                        if status != 'OK':
                            logger.error(f"无法选择文件夹 '{folder_name}', 跳过此文件夹")
                            continue
                except Exception as e:
                    logger.error(f"选择文件夹 '{folder_name}' 时出错: {str(e)}")
                    continue
                
                # 优化：简化搜索逻辑 - 只按日期搜索，过滤在本地进行
                status, data = self.imap.search(None, f'(SINCE {since_date})')
                if status != 'OK':
                    logger.warning(f"搜索文件夹 {folder_name} 失败")
                    continue
                
                message_ids = data[0].split()
                folder_email_count = len(message_ids)
                logger.info(f"📧 文件夹 '{folder_name}': 找到 {folder_email_count} 封近期邮件")
                results["total"] += folder_email_count
                
                if folder_email_count == 0:
                    continue
                
                # 处理每封邮件的附件 - 在这里进行本地过滤
                for msg_id in message_ids:
                    try:
                        # 获取邮件内容
                        status, data = self.imap.fetch(msg_id, '(RFC822)')
                        if status != 'OK':
                            logger.warning(f"获取邮件内容失败: {msg_id}")
                            continue
                        
                        raw_email = data[0][1]
                        email_message = email.message_from_bytes(raw_email)
                        
                        # 解析邮件信息
                        message_id = email_message.get('Message-ID', f'<{str(uuid.uuid4())}>')
                        sender = self._decode_header(email_message.get('From', ''))
                        subject = self._decode_header(email_message.get('Subject', ''))
                        date_str = email_message.get('Date', '')
                        
                        try:
                            receive_date = parsedate_to_datetime(date_str)
                        except:
                            receive_date = datetime.datetime.now()
                        
                        # 本地过滤：检查发件人
                        if self.config.senders and self.config.senders.strip():
                            senders = [s.strip() for s in self.config.senders.split(';') if s.strip()]
                            sender_match = False
                            for allowed_sender in senders:
                                if allowed_sender.lower() in sender.lower():
                                    sender_match = True
                                    break
                            if not sender_match:
                                logger.info(f"❌ 邮件发件人不匹配，跳过: {sender}")
                                continue  # 跳过不匹配的发件人
                        
                        # 本地过滤：检查主题 - 优化：如果主题匹配，就处理这封邮件的所有Excel附件
                        subject_matched = True  # 默认为True，如果没有设置主题过滤
                        if self.config.subjects and self.config.subjects.strip():
                            subjects = [s.strip() for s in self.config.subjects.split(';') if s.strip()]
                            subject_matched = False
                            for keyword in subjects:
                                if keyword.lower() in subject.lower():
                                    subject_matched = True
                                    logger.debug(f"邮件主题匹配关键词 '{keyword}': {subject}")
                                    break
                            if not subject_matched:
                                logger.info(f"❌ 邮件主题不匹配任何关键词，跳过: {subject}")
                                continue  # 跳过不匹配的主题
                        
                        # 处理附件
                        attachments_found = False
                        for part in email_message.walk():
                            if part.get_content_maintype() == 'multipart':
                                continue
                            
                            if part.get('Content-Disposition') is None:
                                continue
                            
                            # 获取文件名
                            filename = part.get_filename()
                            if not filename:
                                continue
                            
                            # 解码文件名
                            filename = self._decode_header(filename)
                            filename = self._sanitize_filename(filename)
                            
                            # 只处理Excel文件
                            if not filename.lower().endswith(('.xlsx', '.xls')):
                                # 记录跳过的非Excel文件
                                results["skipped"] += 1
                                results["skipped_files"].append({
                                    "filename": filename,
                                    "reason": "非Excel文件",
                                    "sender": sender,
                                    "subject": subject
                                })
                                continue
                            
                            # 新增：检查附件名称是否包含"生产订单"
                            if '生产订单' not in filename:
                                # 记录跳过的不符合附件名称筛选的文件
                                results["skipped"] += 1
                                results["skipped_files"].append({
                                    "filename": filename,
                                    "reason": "附件名称不包含'生产订单'",
                                    "sender": sender,
                                    "subject": subject
                                })
                                logger.info(f"❌ 附件名称不包含'生产订单'，跳过: {filename}")
                                continue
                            
                            # 附件名称和主题都匹配，处理该附件
                            attachments_found = True
                            logger.debug(f"✅ 处理Excel附件: {filename} (主题匹配: {subject_matched}, 附件名匹配: True)")
                            
                            # 检查数据库中是否已存在该附件
                            existing_attachment = EmailAttachment.query.filter_by(
                                message_id=message_id,
                                filename=filename
                            ).first()
                            
                            if existing_attachment and existing_attachment.file_size > 0:
                                # 附件已存在且已下载
                                logger.info(f"附件已存在: {filename}")
                                results["skipped"] += 1
                                results["skipped_files"].append({
                                    "filename": filename,
                                    "reason": "附件已存在",
                                    "sender": sender,
                                    "subject": subject
                                })
                                continue
                            
                            # 确定保存路径
                            save_dir = self.config.download_path
                            
                            # 如果配置为按日期分类保存
                            if self.config.use_date_folder:
                                date_folder = receive_date.strftime('%Y%m%d')
                                save_dir = os.path.join(save_dir, date_folder)
                            
                            # 创建目录
                            if not os.path.exists(save_dir):
                                os.makedirs(save_dir)
                            
                            # 生成唯一文件名
                            base_name, ext = os.path.splitext(filename)
                            unique_filename = f"{base_name}_{uuid.uuid4().hex[:8]}{ext}"
                            file_path = os.path.join(save_dir, unique_filename)
                            
                            # 创建或更新附件记录
                            attachment_info = {
                                'message_id': message_id,
                                'email_config_id': self.config.id,
                                'sender': sender,
                                'subject': subject,
                                'receive_date': receive_date,
                                'filename': filename,
                                'file_path': file_path,
                                'file_size': 0,
                                'processed': False
                            }
                            
                            # 保存附件
                            try:
                                with open(file_path, 'wb') as f:
                                    f.write(part.get_payload(decode=True))
                                
                                # 文件大小
                                file_size = os.path.getsize(file_path)
                                logger.info(f"成功下载附件: {filename} ({file_size} 字节)")
                                
                                # 更新附件信息
                                attachment_info['file_size'] = file_size
                                
                                # 标记是否为新下载的附件
                                is_new_attachment = not existing_attachment
                                
                                # 保存到数据库
                                if save_to_db:
                                    try:
                                        if existing_attachment:
                                            # 更新现有记录
                                            existing_attachment.file_path = file_path
                                            existing_attachment.file_size = file_size
                                            db.session.commit()
                                        else:
                                            # 创建新记录
                                            new_attachment = EmailAttachment(**attachment_info)
                                            db.session.add(new_attachment)
                                            db.session.commit()
                                            # 更新新下载附件计数
                                            results["new_downloaded"] += 1
                                    except Exception as e:
                                        logger.error(f"保存附件记录到数据库失败: {str(e)}")
                                        db.session.rollback()
                                        results["failed"] += 1
                                        results["failed_files"].append({
                                            "filename": filename,
                                            "reason": f"数据库错误: {str(e)}",
                                            "sender": sender,
                                            "subject": subject
                                        })
                                        continue
                                
                                # 统计成功下载
                                results["downloaded"] += 1
                                results["processed_files"].append({
                                    "filename": filename,
                                    "size": file_size,
                                    "sender": sender,
                                    "subject": subject,
                                    "receive_date": receive_date.strftime('%Y-%m-%d %H:%M:%S'),
                                    "is_new": is_new_attachment  # 添加是否为新下载标记
                                })
                            
                            except Exception as e:
                                logger.error(f"下载附件失败: {str(e)}")
                                results["failed"] += 1
                                results["failed_files"].append({
                                    "filename": filename,
                                    "reason": f"下载错误: {str(e)}",
                                    "sender": sender,
                                    "subject": subject
                                })
                        
                        if not attachments_found:
                            logger.info(f"邮件没有附件: {subject}")
                    
                    except Exception as e:
                        logger.error(f"处理邮件时出错: {str(e)}")
                        continue
            
            except Exception as e:
                logger.error(f"处理文件夹 {folder_name} 时出错: {str(e)}")
                continue
        
        logger.info(f"==================== 下载完成 ====================")
        logger.info(f"共搜索了 {len(folders)} 个文件夹，成功下载 {results['downloaded']} 个Excel附件，其中新增 {results['new_downloaded']} 个")
        
        return results
    
    def _sanitize_filename(self, filename):
        """清理文件名，移除或替换不合法的字符
        
        参数:
            filename: 原始文件名
            
        返回:
            str: 清理后的安全文件名
        """
        # 替换Windows下不允许的文件名字符
        invalid_chars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|']
        safe_name = filename
        for char in invalid_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 确保文件名不超过255个字符
        if len(safe_name) > 255:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:255-len(ext)] + ext
        
        return safe_name
    
    def _decode_header(self, header):
        """解码邮件头信息
        
        参数:
            header: 邮件头信息
            
        返回:
            str: 解码后的字符串
        """
        if not header:
            return ''
        
        try:
            decoded_header = decode_header(header)
            result = ''
            
            for content, charset in decoded_header:
                if isinstance(content, bytes):
                    if charset:
                        # 使用指定的字符集解码
                        try:
                            result += content.decode(charset, errors='replace')
                        except LookupError:
                            # 如果字符集不支持，尝试使用utf-8解码
                            result += content.decode('utf-8', errors='replace')
                    else:
                        # 没有指定字符集，尝试使用utf-8解码
                        result += content.decode('utf-8', errors='replace')
                else:
                    # 已经是字符串，直接添加
                    result += str(content)
            
            return result
        
        except Exception as e:
            logger.error(f"解码邮件头信息失败: {str(e)}")
            # 返回原始header，确保不会因为解码失败而丢失信息
            return header if isinstance(header, str) else str(header, errors='replace')

    def download_attachment_by_id(self, attachment_id):
        """根据附件ID下载特定附件
        
        参数:
            attachment_id: 附件ID
            
        返回:
            dict: 下载结果，包含success和message字段
        """
        if not self.imap or not self.config:
            logger.error("未连接到邮箱")
            return {'success': False, 'message': '未连接到邮箱'}
        
        try:
            # 获取附件信息
            attachment = EmailAttachment.query.get(attachment_id)
            if not attachment:
                return {'success': False, 'message': f'找不到ID为{attachment_id}的附件'}
            
            # 检查附件是否属于当前邮箱配置
            if attachment.email_config_id != self.config.id:
                return {'success': False, 'message': '附件不属于当前邮箱配置'}
            
            # 获取附件所在文件夹
            folder = attachment.folder if hasattr(attachment, 'folder') else 'INBOX'
            
            # 选择文件夹
            status, data = self.imap.select(folder)
            if status != 'OK':
                logger.error(f"选择文件夹 '{folder}' 失败: {status}")
                # 尝试使用INBOX
                logger.info("尝试使用默认收件箱INBOX")
                status, data = self.imap.select('INBOX')
                if status != 'OK':
                    return {'success': False, 'message': '无法选择邮箱文件夹'}
            
            # 通过message_id搜索邮件
            message_id = attachment.message_id
            # 先尝试通过IMAP搜索MESSAGE-ID，但这并不总是可靠
            status, search_result = self.imap.search(None, f'HEADER MESSAGE-ID "{message_id}"')
            
            message_ids = []
            if status == 'OK':
                message_ids = search_result[0].split()
            
            # 如果无法通过MESSAGE-ID找到，尝试搜索相同主题的近期邮件
            if not message_ids:
                logger.info(f"通过MESSAGE-ID找不到邮件，尝试通过主题和发件人搜索: {attachment.subject}")
                # 获取近期邮件
                days_ago = 30  # 搜索最近30天的邮件
                date_since = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).strftime("%d-%b-%Y")
                status, messages = self.imap.search(None, f'(SINCE {date_since})')
                
                if status != 'OK' or not messages[0]:
                    return {'success': False, 'message': '找不到相关邮件'}
                
                # 遍历邮件，查找匹配的主题和发件人
                all_message_ids = messages[0].split()
                
                for msg_id in all_message_ids:
                    try:
                        # 获取邮件头
                        status, header_data = self.imap.fetch(msg_id, '(BODY.PEEK[HEADER])')
                        if status != 'OK':
                            continue
                        
                        # 解析邮件头
                        header = email.message_from_bytes(header_data[0][1])
                        subject = self._decode_header(header.get('Subject', ''))
                        sender = header.get('From', '')
                        
                        # 检查是否匹配
                        if attachment.subject in subject and attachment.sender in sender:
                            message_ids.append(msg_id)
                            break
                    except Exception as e:
                        logger.error(f"处理邮件头时出错: {str(e)}")
            
            if not message_ids:
                return {'success': False, 'message': '找不到包含此附件的邮件'}
            
            # 获取第一个匹配的邮件
            msg_id = message_ids[0]
            status, data = self.imap.fetch(msg_id, '(RFC822)')
            if status != 'OK':
                return {'success': False, 'message': '无法获取邮件内容'}
            
            # 解析邮件
            raw_email = data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # 查找匹配的附件
            found = False
            for part in email_message.walk():
                if part.get_content_maintype() == 'multipart':
                    continue
                
                filename = part.get_filename()
                if not filename:
                    continue
                
                # 解码文件名
                filename = self._decode_header(filename)
                
                # 检查是否为目标附件
                if filename == attachment.filename or self._sanitize_filename(filename) == attachment.filename:
                    # 创建保存目录
                    save_dir = os.path.dirname(attachment.file_path)
                    if not os.path.exists(save_dir):
                        os.makedirs(save_dir)
                    
                    # 保存附件
                    with open(attachment.file_path, 'wb') as f:
                        f.write(part.get_payload(decode=True))
                    
                    # 更新附件状态
                    file_size = os.path.getsize(attachment.file_path)
                    attachment.file_size = file_size
                    attachment.process_result = 'success'
                    attachment.process_date = datetime.datetime.utcnow()
                    attachment.process_message = '手动下载成功'
                    db.session.commit()
                    
                    logger.info(f"成功下载附件: {filename} ({file_size} 字节)")
                    found = True
                    break
            
            if found:
                return {'success': True, 'message': '附件下载成功'}
            else:
                return {'success': False, 'message': '在邮件中找不到匹配的附件'}
        
        except Exception as e:
            logger.error(f"下载附件失败: {str(e)}")
            return {'success': False, 'message': f'下载附件失败: {str(e)}'}

# 已移除的Excel处理器类，保留代码作为后续重新开发的参考
'''
class ExcelProcessor:
    """Excel处理器类，用于处理Excel文件和提取数据"""
    
    def __init__(self, mapping_id=None):
        """初始化Excel处理器
        
        参数:
            mapping_id: Excel映射配置ID，为None时不加载配置
        """
        self.mapping = None
        
        if mapping_id:
            self.mapping = ExcelMapping.query.get(mapping_id)
    
    def process_file(self, file_path, mapping=None, save_to_db=True):
        """处理Excel文件
        
        参数:
            file_path: Excel文件路径
            mapping: Excel映射配置对象，为None时使用已有配置
            save_to_db: 是否保存到数据库，默认为True
            
        返回:
            dict: 处理结果
        """
        if mapping:
            self.mapping = mapping
        
        if not self.mapping:
            raise ValueError("未提供Excel映射配置")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            # 加载Excel文件
            sheet_name = self.mapping.sheet_name if self.mapping.sheet_name else 0
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 配置信息
            header_row = self.mapping.header_row - 1  # pandas行索引从0开始
            data_start_row = self.mapping.start_row - 1
            
            # 设置表头
            if header_row >= 0:
                headers = df.iloc[header_row].values
                df.columns = headers
                
                # 重新加载数据，从数据起始行开始
                df = df.iloc[data_start_row:]
                df = df.reset_index(drop=True)
            
            # 获取字段映射
            field_mappings = json.loads(self.mapping.field_mappings)
            
            # 提取数据
            extracted_data = []
            
            # 获取关键字段
            key_fields = [field.strip() for field in self.mapping.key_fields.split(',') if field.strip()]
            
            # 处理每一行数据
            for index, row in df.iterrows():
                data = {}
                
                # 原始数据
                raw_data = row.to_dict()
                
                # 根据映射提取数据
                for excel_field, system_field in field_mappings.items():
                    if excel_field in row:
                        data[system_field] = row[excel_field]
                
                # 检查关键字段是否存在
                key_values = {}
                for key_field in key_fields:
                    if key_field in data:
                        key_values[key_field] = data[key_field]
                
                # 保存到数据库
                if save_to_db and key_values:
                    # 构建查询条件
                    query_conditions = []
                    for field, value in key_values.items():
                        query_conditions.append(getattr(OrderData, field) == value)
                    
                    # 查找是否存在相同记录
                    existing_order = OrderData.query.filter(*query_conditions).first()
                    
                    if existing_order:
                        # 更新现有记录
                        for field, value in data.items():
                            setattr(existing_order, field, value)
                        existing_order.updated_at = datetime.datetime.utcnow()
                        existing_order.source_file = file_path
                        existing_order.raw_data = json.dumps(raw_data, ensure_ascii=False)
                        db.session.commit()
                        
                        data['id'] = existing_order.id
                        data['updated'] = True
                    else:
                        # 创建新记录
                        order = OrderData(**data)
                        order.source_file = file_path
                        order.raw_data = json.dumps(raw_data, ensure_ascii=False)
                        db.session.add(order)
                        db.session.commit()
                        
                        data['id'] = order.id
                        data['created'] = True
                
                extracted_data.append(data)
            
            # 生成输出文件
            if self.mapping.output_path and self.mapping.output_filename:
                # 确保输出目录存在
                if not os.path.exists(self.mapping.output_path):
                    os.makedirs(self.mapping.output_path)
                
                # 构建输出文件名
                output_filename = self.mapping.output_filename
                if '{date}' in output_filename:
                    output_filename = output_filename.replace('{date}', datetime.datetime.now().strftime('%Y%m%d'))
                
                output_path = os.path.join(self.mapping.output_path, output_filename)
                
                # 将提取的数据保存为Excel
                output_df = pd.DataFrame(extracted_data)
                output_df.to_excel(output_path, index=False)
            
            return {
                'status': 'success',
                'message': f'成功处理 {len(extracted_data)} 条数据',
                'data': extracted_data,
                'output_file': output_path if 'output_path' in locals() else None
            }
        
        except Exception as e:
            logger.error(f"处理Excel文件失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'处理Excel文件失败: {str(e)}',
                'data': None
            }
    
    def generate_summary_file(self, data, template_path=None, output_path=None):
        """生成按照生产工单信息汇总表格式的汇总文件
        
        参数:
            data: 处理后的数据列表
            template_path: 模板文件路径，默认为None表示使用默认模板
            output_path: 输出文件路径，默认为None表示生成默认路径
        
        返回:
            str: 生成的文件路径
        """
        try:
            # 省略实现...
            pass
        except Exception as e:
            logger.error(f"生成汇总文件失败: {str(e)}")
            return None
    
    def batch_process_and_summary(self, file_paths, mapping=None, save_to_db=True, template_path=None):
        """批量处理文件并汇总
        
        参数:
            file_paths: 文件路径列表
            mapping: Excel映射配置对象，为None时使用已有配置
            save_to_db: 是否保存到数据库，默认为True
            template_path: 模板文件路径，默认为None表示使用默认模板
            
        返回:
            dict: 处理结果
        """
        if mapping:
            self.mapping = mapping
            
        if not self.mapping:
            raise ValueError("未提供Excel映射配置")
        
        results = {
            'success': 0,
            'error': 0,
            'total_records': 0,
            'successful_files': [],
            'failed_files': [],
            'summary_file': None
        }
        
        all_data = []
        
        # 处理每个文件
        for file_path in file_paths:
            try:
                result = self.process_file(file_path, self.mapping, save_to_db)
                
                if result['status'] == 'success':
                    results['success'] += 1
                    results['total_records'] += len(result['data'])
                    
                    results['successful_files'].append({
                        'file': os.path.basename(file_path),
                        'records': len(result['data']),
                        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    
                    # 添加到汇总数据
                    all_data.extend(result['data'])
                else:
                    results['error'] += 1
                    results['failed_files'].append({
                        'file': os.path.basename(file_path),
                        'error': result['message']
                    })
            except Exception as e:
                results['error'] += 1
                results['failed_files'].append({
                    'file': os.path.basename(file_path),
                    'error': str(e)
                })
        
        # 生成汇总文件
        if all_data:
            # 默认路径
            now = datetime.datetime.now()
            summary_dir = os.path.join(os.getcwd(), 'downloads')
            if not os.path.exists(summary_dir):
                os.makedirs(summary_dir)
            
            output_path = os.path.join(
                summary_dir, 
                f'生产工单信息汇总表_{now.strftime("%Y%m%d_%H%M%S")}.xlsx'
            )
            
            # 使用模板或直接创建
            if template_path and os.path.exists(template_path):
                # 从模板创建
                df_template = pd.read_excel(template_path)
                
                # 取模板的列名作为标准
                template_columns = df_template.columns.tolist()
                
                # 创建DataFrame并按模板列名组织
                df = pd.DataFrame(all_data)
                
                # 处理可能的字段名不一致问题
                mapped_columns = []
                for col in template_columns:
                    if col in df.columns:
                        mapped_columns.append(col)
                    else:
                        # 添加空列
                        df[col] = None
                        mapped_columns.append(col)
                
                # 按模板列名调整数据列顺序
                df = df[mapped_columns]
            else:
                # 直接创建DataFrame
                df = pd.DataFrame(all_data)
            
            # 保存汇总文件
            df.to_excel(output_path, index=False)
            results['summary_file'] = output_path
        
        return results
''' 
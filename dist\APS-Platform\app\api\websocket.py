#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket事件推送系统
支持实时进度更新和状态同步
"""

import logging
from flask import request, session
from flask_login import current_user
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room, disconnect
from app.services.event_bus import get_event_bus
from app.services.task_manager import get_task_manager

logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket管理器"""
    
    def __init__(self, socketio: SocketIO):
        """初始化WebSocket管理器
        
        Args:
            socketio: Flask-SocketIO实例
        """
        self.socketio = socketio
        self.event_bus = get_event_bus()
        self.task_manager = get_task_manager()
        self._setup_event_handlers()
    
    def _get_current_user_id(self):
        """获取当前用户ID，如果未认证则返回None"""
        if current_user.is_authenticated:
            return current_user.username
        return None
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接处理"""
            # 使用Flask-Login的current_user检查认证状态
            if not current_user.is_authenticated:
                logger.warning("未认证用户尝试WebSocket连接")
                disconnect()
                return False
            
            user_id = current_user.username
            # 用户加入个人房间
            join_room(f"user_{user_id}")
            logger.info(f"用户 {user_id} 已连接WebSocket")
            
            # 发送欢迎消息
            emit('connection_status', {
                'status': 'connected',
                'user_id': user_id,
                'timestamp': self._get_timestamp()
            })
            
            # 发送用户的活动任务状态
            self._send_user_task_status(user_id)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接处理"""
            if current_user.is_authenticated:
                user_id = current_user.username
                leave_room(f"user_{user_id}")
                logger.info(f"用户 {user_id} 已断开WebSocket连接")
        
        @self.socketio.on('join_task_room')
        def handle_join_task_room(data):
            """加入任务房间"""
            if not current_user.is_authenticated:
                emit('error', {'message': '用户未认证'})
                return
                
            user_id = current_user.username
            task_id = data.get('task_id')
            
            if not task_id:
                emit('error', {'message': '无效的任务ID'})
                return
            
            # 验证用户对任务的访问权限
            task = self.task_manager.get_task(task_id)
            if not task or task.user_id != user_id:
                emit('error', {'message': '无权限访问该任务'})
                return
            
            join_room(f"task_{task_id}")
            emit('task_room_joined', {
                'task_id': task_id,
                'timestamp': self._get_timestamp()
            })
            logger.debug(f"用户 {user_id} 加入任务房间 {task_id}")
        
        @self.socketio.on('leave_task_room')
        def handle_leave_task_room(data):
            """离开任务房间"""
            if not current_user.is_authenticated:
                return
                
            user_id = current_user.username
            task_id = data.get('task_id')
            
            if task_id:
                leave_room(f"task_{task_id}")
                emit('task_room_left', {
                    'task_id': task_id,
                    'timestamp': self._get_timestamp()
                })
                logger.debug(f"用户 {user_id} 离开任务房间 {task_id}")
        
        @self.socketio.on('get_task_status')
        def handle_get_task_status(data):
            """获取任务状态"""
            user_id = self._get_current_user_id()
            if not user_id:
                emit('error', {'message': '用户未认证'})
                return
                
            task_id = data.get('task_id')
            
            if not task_id:
                emit('error', {'message': '任务ID不能为空'})
                return
            
            task = self.task_manager.get_task(task_id)
            if not task:
                emit('error', {'message': '任务不存在'})
                return
            
            if task.user_id != user_id:
                emit('error', {'message': '无权限访问该任务'})
                return
            
            emit('task_status', {
                'task': task.to_dict(),
                'timestamp': self._get_timestamp()
            })
        
        @self.socketio.on('cancel_task')
        def handle_cancel_task(data):
            """取消任务"""
            user_id = self._get_current_user_id()
            if not user_id:
                emit('error', {'message': '用户未认证'})
                return
                
            task_id = data.get('task_id')
            
            if not task_id:
                emit('error', {'message': '任务ID不能为空'})
                return
            
            task = self.task_manager.get_task(task_id)
            if not task or task.user_id != user_id:
                emit('error', {'message': '无权限操作该任务'})
                return
            
            if self.task_manager.cancel_task(task_id):
                emit('task_cancelled', {
                    'task_id': task_id,
                    'timestamp': self._get_timestamp()
                })
            else:
                emit('error', {'message': '取消任务失败'})
        
        @self.socketio.on('pause_task')
        def handle_pause_task(data):
            """暂停任务"""
            user_id = self._get_current_user_id()
            if not user_id:
                emit('error', {'message': '用户未认证'})
                return
                
            task_id = data.get('task_id')
            
            if not task_id:
                emit('error', {'message': '任务ID不能为空'})
                return
            
            task = self.task_manager.get_task(task_id)
            if not task or task.user_id != user_id:
                emit('error', {'message': '无权限操作该任务'})
                return
            
            if self.task_manager.pause_task(task_id):
                emit('task_paused', {
                    'task_id': task_id,
                    'timestamp': self._get_timestamp()
                })
            else:
                emit('error', {'message': '暂停任务失败'})
        
        @self.socketio.on('resume_task')
        def handle_resume_task(data):
            """恢复任务"""
            user_id = self._get_current_user_id()
            if not user_id:
                emit('error', {'message': '用户未认证'})
                return
                
            task_id = data.get('task_id')
            
            if not task_id:
                emit('error', {'message': '任务ID不能为空'})
                return
            
            task = self.task_manager.get_task(task_id)
            if not task or task.user_id != user_id:
                emit('error', {'message': '无权限操作该任务'})
                return
            
            if self.task_manager.resume_task(task_id):
                emit('task_resumed', {
                    'task_id': task_id,
                    'timestamp': self._get_timestamp()
                })
            else:
                emit('error', {'message': '恢复任务失败'})
        
        @self.socketio.on('task_control')
        def handle_task_control(data):
            """统一任务控制处理"""
            user_id = self._get_current_user_id()
            if not user_id:
                emit('error', {'message': '用户未认证'})
                return
            
            task_id = data.get('task_id')
            action = data.get('action')
            
            if not task_id or not action:
                emit('error', {'message': '任务ID和操作类型不能为空'})
                return
            
            logger.info(f"用户 {user_id} 请求对任务 {task_id} 执行操作: {action}")
            
            # 验证用户权限
            task = self.task_manager.get_task(task_id)
            if not task:
                emit('error', {'message': '任务不存在'})
                return
            
            if task.user_id != user_id:
                emit('error', {'message': '无权限操作该任务'})
                return
            
            # 执行对应操作
            success = False
            response_event = None
            
            if action == 'pause':
                success = self.task_manager.pause_task(task_id)
                response_event = 'task_paused' if success else 'error'
            elif action == 'resume':
                success = self.task_manager.resume_task(task_id)
                response_event = 'task_resumed' if success else 'error'
            elif action == 'cancel':
                success = self.task_manager.cancel_task(task_id)
                response_event = 'task_cancelled' if success else 'error'
            else:
                emit('error', {'message': f'不支持的操作类型: {action}'})
                return
            
            # 发送响应
            if success:
                emit(response_event, {
                    'task_id': task_id,
                    'action': action,
                    'timestamp': self._get_timestamp()
                })
                logger.info(f"任务 {task_id} {action} 操作成功")
            else:
                emit('error', {
                    'message': f'{action} 任务失败',
                    'task_id': task_id,
                    'timestamp': self._get_timestamp()
                })
                logger.warning(f"任务 {task_id} {action} 操作失败")
    
    def _send_user_task_status(self, user_id: str):
        """发送用户的活动任务状态"""
        try:
            from app.services.task_manager import TaskStatus
            active_tasks = self.task_manager.get_user_tasks(
                user_id, 
                status_filter=[TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED]
            )
            
            if active_tasks:
                tasks_data = [task.to_dict() for task in active_tasks]
                self.socketio.emit('user_tasks_status', {
                    'tasks': tasks_data,
                    'timestamp': self._get_timestamp()
                }, room=f"user_{user_id}")
                
        except Exception as e:
            logger.error(f"发送用户任务状态失败: {e}")
    
    def _get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def broadcast_to_user(self, user_id: str, event: str, data: dict):
        """向指定用户广播消息"""
        try:
            self.socketio.emit(event, data, room=f"user_{user_id}")
            logger.debug(f"向用户 {user_id} 发送事件: {event}")
        except Exception as e:
            logger.error(f"向用户广播消息失败: {e}")
    
    def broadcast_to_task_room(self, task_id: str, event: str, data: dict):
        """向任务房间广播消息"""
        try:
            self.socketio.emit(event, data, room=f"task_{task_id}")
            logger.debug(f"向任务房间 {task_id} 发送事件: {event}")
        except Exception as e:
            logger.error(f"向任务房间广播消息失败: {e}")
    
    def broadcast_progress_update(self, task_id: str, progress_data: dict):
        """广播进度更新"""
        try:
            task = self.task_manager.get_task(task_id)
            if task:
                # 发送给用户
                self.broadcast_to_user(task.user_id, 'progress_update', {
                    'task_id': task_id,
                    'progress': progress_data,
                    'timestamp': self._get_timestamp()
                })
                
                # 发送给任务房间
                self.broadcast_to_task_room(task_id, 'progress_update', {
                    'task_id': task_id,
                    'progress': progress_data,
                    'timestamp': self._get_timestamp()
                })
                
        except Exception as e:
            logger.error(f"广播进度更新失败: {e}")
    
    def broadcast_task_status_change(self, task_id: str, status: str, task_data: dict):
        """广播任务状态变更"""
        try:
            task = self.task_manager.get_task(task_id)
            if task:
                message = {
                    'task_id': task_id,
                    'status': status,
                    'task': task_data,
                    'timestamp': self._get_timestamp()
                }
                
                # 发送给用户
                self.broadcast_to_user(task.user_id, 'task_status_change', message)
                
                # 发送给任务房间
                self.broadcast_to_task_room(task_id, 'task_status_change', message)
                
        except Exception as e:
            logger.error(f"广播任务状态变更失败: {e}")
    
    def broadcast_error(self, user_id: str, error_message: str, task_id: str = None):
        """广播错误消息"""
        try:
            error_data = {
                'message': error_message,
                'timestamp': self._get_timestamp()
            }
            
            if task_id:
                error_data['task_id'] = task_id
            
            self.broadcast_to_user(user_id, 'error', error_data)
            
            if task_id:
                self.broadcast_to_task_room(task_id, 'error', error_data)
                
        except Exception as e:
            logger.error(f"广播错误消息失败: {e}")


# 全局WebSocket管理器实例
_websocket_manager = None

def get_websocket_manager() -> WebSocketManager:
    """获取全局WebSocket管理器实例"""
    global _websocket_manager
    return _websocket_manager

def init_websocket_manager(socketio: SocketIO) -> WebSocketManager:
    """初始化WebSocket管理器"""
    global _websocket_manager
    _websocket_manager = WebSocketManager(socketio)
    return _websocket_manager

def setup_websocket_events(socketio: SocketIO):
    """设置WebSocket事件处理
    
    这个函数用于在不需要完整WebSocketManager时设置基本事件处理
    """
    
    @socketio.on('connect')
    def handle_connect():
        """基本连接处理"""
        if not current_user.is_authenticated:
            logger.warning("未认证用户尝试WebSocket连接")
            disconnect()
            return False
        
        user_id = current_user.username
        join_room(f"user_{user_id}")
        emit('connection_status', {
            'status': 'connected',
            'user_id': user_id
        })
        logger.info(f"用户 {user_id} 已连接WebSocket")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """基本断开连接处理"""
        if current_user.is_authenticated:
            user_id = current_user.username
            leave_room(f"user_{user_id}")
            logger.info(f"用户 {user_id} 已断开WebSocket连接")
    
    @socketio.on('ping')
    def handle_ping():
        """心跳检测"""
        from datetime import datetime
        emit('pong', {'timestamp': datetime.now().isoformat()})

# 便捷函数用于发送实时更新
def emit_progress_update(task_id: str, progress_data: dict):
    """发送进度更新"""
    manager = get_websocket_manager()
    if manager:
        manager.broadcast_progress_update(task_id, progress_data)

def emit_task_status_change(task_id: str, status: str, task_data: dict):
    """发送任务状态变更"""
    manager = get_websocket_manager()
    if manager:
        manager.broadcast_task_status_change(task_id, status, task_data)

def emit_error_to_user(user_id: str, error_message: str, task_id: str = None):
    """向用户发送错误消息"""
    manager = get_websocket_manager()
    if manager:
        manager.broadcast_error(user_id, error_message, task_id)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单处理服务
整合邮箱附件下载、Excel解析、分类汇总等功能
使用事件驱动架构支持实时进度反馈
使用EnhancedExcelParser实现完整的订单处理工作流
"""

import os
import logging
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import current_app
from flask_login import current_user

from app.services.event_bus import get_event_bus
from app.services.task_manager import get_task_manager, TaskPriority
from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
# 使用EnhancedExcelParser替代ExcelProcessor
from app.services.enhanced_excel_parser import EnhancedExcelParser
from app.models import EmailConfig, EmailAttachment, OrderData
from app import db

logger = logging.getLogger(__name__)

class OrderProcessingService:
    """订单处理服务 - 统一的订单处理工作流"""
    
    def __init__(self):
        """初始化订单处理服务"""
        self.event_bus = get_event_bus()
        self.task_manager = get_task_manager()
        
        # 注意：邮件处理器现在在任务执行时动态创建，不在初始化时创建
        # 这样可以避免在没有配置的情况下出错
        self.email_processor = None
        
        # 使用EnhancedExcelParser替代ExcelProcessor
        self.excel_parser = EnhancedExcelParser(downloads_dir="downloads", search_subdirs=True)
        
        # 汇总表路径
        self.engineering_summary_file = os.path.join("downloads", "FT工程订单汇总表.xlsx")
        self.production_summary_file = os.path.join("downloads", "FT量产订单汇总表.xlsx")
        
    def create_fetch_and_parse_task(self, config_id: int, user_id: str = None) -> str:
        """创建获取附件并解析的任务
        
        Args:
            config_id: 邮箱配置ID
            user_id: 用户ID
            
        Returns:
            str: 任务ID
        """
        user_id = user_id or str(current_user.id) if current_user.is_authenticated else "system"
        
        task_id = self.task_manager.create_task(
            name="获取邮件附件并自动解析",
            description=f"从邮箱配置 {config_id} 获取新附件并自动解析分类",
            priority=TaskPriority.HIGH,
            user_id=user_id,
            metadata={
                'config_id': config_id,
                'process_type': 'fetch_and_parse',
                'auto_classify': True
            }
        )
        
        # 启动任务
        success = self.task_manager.start_task(
            task_id, 
            self._execute_fetch_and_parse_task,
            config_id=config_id
        )
        
        if not success:
            logger.error(f"启动获取并解析任务失败: {task_id}")
            return None
            
        return task_id
    
    def create_parse_existing_files_task(self, file_paths: List[str], 
                                       classification_rules: Dict[str, Any] = None,
                                       user_id: str = None) -> str:
        """创建解析现有文件的任务
        
        Args:
            file_paths: 待解析的文件路径列表
            classification_rules: 分类规则配置
            user_id: 用户ID
            
        Returns:
            str: 任务ID
        """
        user_id = user_id or str(current_user.id) if current_user.is_authenticated else "system"
        
        task_id = self.task_manager.create_task(
            name="批量解析Excel文件",
            description=f"解析 {len(file_paths)} 个Excel文件并进行分类汇总",
            priority=TaskPriority.NORMAL,
            user_id=user_id,
            metadata={
                'file_paths': file_paths,
                'classification_rules': classification_rules or {},
                'process_type': 'parse_existing'
            }
        )
        
        # 启动任务
        success = self.task_manager.start_task(
            task_id,
            self._execute_parse_existing_files_task,
            file_paths=file_paths,
            classification_rules=classification_rules
        )
        
        if not success:
            logger.error(f"启动解析现有文件任务失败: {task_id}")
            return None
            
        return task_id

    def _execute_fetch_and_parse_task(self, config_id: int, task_id: str = None, 
                                    task_manager=None, **kwargs):
        """执行获取附件并解析的任务"""
        try:
            # 更新进度：开始获取邮件附件
            task_manager.update_task_progress(
                task_id, 
                current=0, 
                total=100,
                message="开始获取邮件附件...",
                current_step="获取邮件附件"
            )
            
            # 发布开始事件
            self.event_bus.publish_event(
                self.event_bus.EVENT_TYPES['PARSING_STARTED'],
                {
                    'task_id': task_id,
                    'process_type': 'fetch_and_parse',
                    'config_id': config_id
                },
                user_id=kwargs.get('user_id')
            )
            
            # 获取邮箱配置
            config = EmailConfig.query.get(config_id)
            if not config:
                raise ValueError(f"邮箱配置 {config_id} 不存在")
            
            # 步骤1：获取新附件
            task_manager.update_task_progress(
                task_id, 
                current=10, 
                message="正在连接邮箱服务器...",
                current_step="连接邮箱"
            )
            
            # 创建高性能邮件处理器
            from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
            email_processor = HighPerformanceEmailProcessor(config)
            
            # 使用邮件处理器获取附件
            fetch_result = email_processor.process_fast(days=config.fetch_days or 3)
            
            if not fetch_result or fetch_result.get('processed', 0) == 0:
                task_manager.update_task_progress(
                    task_id, 
                    current=100, 
                    message="未发现新的邮件附件",
                    current_step="完成"
                )
                return {
                    'success': True,
                    'message': '未发现新的邮件附件',
                    'attachments_count': 0,
                    'parsed_count': 0
                }
            
            # 从数据库获取最近的附件记录
            from datetime import timedelta
            recent_attachments = EmailAttachment.query.filter(
                EmailAttachment.email_config_id == config_id,
                EmailAttachment.created_at >= datetime.now() - timedelta(days=7)
            ).order_by(EmailAttachment.created_at.desc()).all()
            
            # 步骤2：识别宜欣订单文件
            task_manager.update_task_progress(
                task_id, 
                current=30, 
                message=f"已获取 {len(recent_attachments)} 个附件，正在识别订单文件...",
                current_step="识别订单文件"
            )
            
            excel_files = self._identify_order_files(recent_attachments)
            
            if not excel_files:
                task_manager.update_task_progress(
                    task_id, 
                    current=100, 
                    message="未发现宜欣订单Excel文件",
                    current_step="完成"
                )
                return {
                    'success': True,
                    'message': '未发现宜欣订单Excel文件',
                    'attachments_count': len(recent_attachments),
                    'parsed_count': 0
                }
            
            # 步骤3：批量解析Excel文件
            task_manager.update_task_progress(
                task_id, 
                current=50, 
                message=f"发现 {len(excel_files)} 个订单文件，开始解析...",
                current_step="解析Excel文件"
            )
            
            parsed_results = self._batch_parse_excel_files(
                excel_files, task_id, task_manager, 
                progress_start=50, progress_end=80
            )
            
            # 步骤4：分类汇总
            task_manager.update_task_progress(
                task_id, 
                current=80, 
                message="正在进行数据分类汇总...",
                current_step="分类汇总"
            )
            
            classification_results = self._classify_and_summarize(parsed_results, task_id, task_manager)
            
            # 步骤5：保存数据和生成汇总表
            task_manager.update_task_progress(
                task_id, 
                current=90, 
                message="正在保存数据和生成汇总表...",
                current_step="保存数据"
            )
            
            saved_count = self._save_parsed_data_and_generate_summary(parsed_results, classification_results)
            
            # 完成
            task_manager.update_task_progress(
                task_id, 
                current=100, 
                message=f"任务完成！解析 {len(excel_files)} 个文件，保存 {saved_count} 条数据",
                current_step="完成"
            )
            
            # 发布完成事件
            self.event_bus.publish_event(
                self.event_bus.EVENT_TYPES['PARSING_COMPLETE'],
                {
                    'task_id': task_id,
                    'result': {
                        'success': True,
                        'message': '获取并解析完成',
                        'files_count': len(excel_files),
                        'parsed_count': sum(len(r.get('data', [])) for r in parsed_results),
                        'saved_count': saved_count,
                        'classification_results': classification_results
                    }
                }
            )
            
            return {
                'success': True,
                'message': '获取并解析完成',
                'attachments_count': len(recent_attachments),
                'files_count': len(excel_files),
                'parsed_count': sum(len(r.get('data', [])) for r in parsed_results),
                'saved_count': saved_count,
                'classification_results': classification_results
            }
            
        except Exception as e:
            logger.error(f"执行获取并解析任务失败: {e}")
            task_manager.update_task_progress(
                task_id, 
                current=100, 
                message=f"任务失败: {str(e)}",
                current_step="错误",
                status="failed"
            )
            raise

    def _execute_parse_existing_files_task(self, file_paths: List[str], 
                                         classification_rules: Dict[str, Any] = None,
                                         task_id: str = None, task_manager=None, **kwargs):
        """执行解析现有文件的任务"""
        try:
            # 更新解析器的分类规则
            if classification_rules:
                self.excel_parser.classification_rules = classification_rules
                logger.info(f"应用自定义分类规则: {len(classification_rules)} 个规则")
            
            # 发布开始事件
            self.event_bus.publish_event(
                self.event_bus.EVENT_TYPES['PARSING_STARTED'],
                {
                    'task_id': task_id,
                    'process_type': 'parse_existing',
                    'files_count': len(file_paths)
                },
                user_id=kwargs.get('user_id')
            )
            
            # 步骤1：批量解析Excel文件
            task_manager.update_task_progress(
                task_id, 
                current=0, 
                total=100,
                message=f"开始解析 {len(file_paths)} 个Excel文件...",
                current_step="解析Excel文件"
            )
            
            parsed_results = self._batch_parse_excel_files(
                file_paths, task_id, task_manager, 
                progress_start=0, progress_end=70
            )
            
            # 步骤2：分类汇总
            task_manager.update_task_progress(
                task_id, 
                current=70, 
                message="正在进行数据分类汇总...",
                current_step="分类汇总"
            )
            
            classification_results = self._classify_and_summarize(parsed_results, task_id, task_manager)
            
            # 步骤3：保存数据和生成汇总表
            task_manager.update_task_progress(
                task_id, 
                current=85, 
                message="正在保存数据和生成汇总表...",
                current_step="保存数据"
            )
            
            saved_count = self._save_parsed_data_and_generate_summary(parsed_results, classification_results)
            
            # 完成
            task_manager.update_task_progress(
                task_id, 
                current=100, 
                message=f"文件解析完成！处理 {len(file_paths)} 个文件，保存 {saved_count} 条数据",
                current_step="完成"
            )
            
            # 发布完成事件
            self.event_bus.publish_event(
                self.event_bus.EVENT_TYPES['PARSING_COMPLETE'],
                {
                    'task_id': task_id,
                    'result': {
                        'success': True,
                        'message': '文件解析完成',
                        'files_count': len(file_paths),
                        'parsed_count': sum(len(r.get('data', [])) for r in parsed_results),
                        'saved_count': saved_count,
                        'classification_results': classification_results
                    }
                }
            )
            
            return {
                'success': True,
                'message': '文件解析完成',
                'files_count': len(file_paths),
                'parsed_count': sum(len(r.get('data', [])) for r in parsed_results),
                'saved_count': saved_count,
                'classification_results': classification_results
            }
            
        except Exception as e:
            logger.error(f"执行解析现有文件任务失败: {e}")
            task_manager.update_task_progress(
                task_id, 
                current=100, 
                message=f"任务失败: {str(e)}",
                current_step="错误",
                status="failed"
            )
            raise

    def _identify_order_files(self, attachments: List[EmailAttachment]) -> List[str]:
        """识别宜欣订单Excel文件"""
        order_files = []
        yixin_keywords = ['宜欣', '生产订单', '宜欣生产订单']
        
        for attachment in attachments:
            file_name = attachment.file_name.lower()
            file_path = attachment.file_path
            
            # 检查文件名是否包含宜欣关键词，且是Excel文件
            if (any(keyword in attachment.file_name for keyword in yixin_keywords) and 
                (file_name.endswith('.xlsx') or file_name.endswith('.xls')) and
                os.path.exists(file_path)):
                order_files.append(file_path)
                logger.info(f"识别到宜欣订单文件: {attachment.file_name}")
        
        return order_files

    def _batch_parse_excel_files(self, file_paths: List[str], task_id: str, 
                               task_manager, progress_start: int = 0, 
                               progress_end: int = 100) -> List[Dict[str, Any]]:
        """批量解析Excel文件 - 使用EnhancedExcelParser"""
        parsed_results = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            try:
                # 更新进度
                current_progress = progress_start + (i / total_files) * (progress_end - progress_start)
                task_manager.update_task_progress(
                    task_id,
                    current=int(current_progress),
                    message=f"正在解析文件 {i+1}/{total_files}: {os.path.basename(file_path)}",
                    current_step="解析Excel文件"
                )
                
                # 使用EnhancedExcelParser解析单个文件
                result = self.excel_parser.parse_order_file(file_path)
                if result and result.get('status') == 'success':
                    parsed_results.append({
                        'file_path': file_path,
                        'file_name': os.path.basename(file_path),
                        'data': result.get('data', []),
                        'metadata': {
                            'confidence_score': result.get('confidence_score', 0.0),
                            'parse_method': result.get('parse_method', 'enhanced'),
                            'horizontal_info': result.get('horizontal_info', {}),
                            'structure_analysis': result.get('structure_analysis', {})
                        },
                        'parsed_at': datetime.now().isoformat()
                    })
                    logger.info(f"成功解析文件: {os.path.basename(file_path)}, "
                              f"数据量: {len(result.get('data', []))}")
                else:
                    logger.error(f"解析文件失败: {os.path.basename(file_path)}, "
                               f"错误: {result.get('message', '未知错误')}")
                
                # 发布文件解析完成事件
                self.event_bus.publish_event(
                    self.event_bus.EVENT_TYPES['FILE_SCANNED'],
                    {
                        'task_id': task_id,
                        'file_path': file_path,
                        'file_name': os.path.basename(file_path),
                        'success': result and result.get('status') == 'success',
                        'progress': {
                            'current': i + 1,
                            'total': total_files
                        }
                    }
                )
                
            except Exception as e:
                logger.error(f"解析文件失败 {file_path}: {e}")
                # 继续处理其他文件
                continue
        
        return parsed_results

    def _classify_and_summarize(self, parsed_results: List[Dict[str, Any]], 
                              task_id: str, task_manager) -> Dict[str, Any]:
        """分类汇总数据"""
        try:
            engineering_data = []
            production_data = []
            unknown_data = []
            
            total_records = 0
            
            for result in parsed_results:
                data = result.get('data', [])
                for row in data:
                    total_records += 1
                    classification = row.get('分类结果', '未知')
                    
                    if classification == '工程':
                        engineering_data.append(row)
                    elif classification == '量产':
                        production_data.append(row)
                    else:
                        unknown_data.append(row)
            
            # 创建汇总结果
            classification_results = {
                'engineering': {
                    'count': len(engineering_data),
                    'data': engineering_data
                },
                'production': {
                    'count': len(production_data),
                    'data': production_data
                },
                'unknown': {
                    'count': len(unknown_data),
                    'data': unknown_data
                },
                'total_records': total_records,
                'summary': {
                    'engineering_percentage': len(engineering_data) / total_records * 100 if total_records > 0 else 0,
                    'production_percentage': len(production_data) / total_records * 100 if total_records > 0 else 0,
                    'unknown_percentage': len(unknown_data) / total_records * 100 if total_records > 0 else 0
                }
            }
            
            logger.info(f"分类汇总完成: 工程 {len(engineering_data)} 条, "
                       f"量产 {len(production_data)} 条, 未知 {len(unknown_data)} 条")
            
            # 发布分类更新事件
            self.event_bus.publish_event(
                self.event_bus.EVENT_TYPES['CLASSIFICATION_UPDATED'],
                {
                    'task_id': task_id,
                    'classification_results': classification_results
                }
            )
            
            return classification_results
            
        except Exception as e:
            logger.error(f"分类汇总失败: {e}")
            raise

    def _save_parsed_data_and_generate_summary(self, parsed_results: List[Dict[str, Any]], 
                                             classification_results: Dict[str, Any]) -> int:
        """保存解析后的数据到数据库并生成汇总表"""
        try:
            saved_count = 0
            
            # 保存到数据库
            engineering_data = classification_results.get('engineering', {}).get('data', [])
            production_data = classification_results.get('production', {}).get('data', [])
            
            # 保存工程订单数据
            for row in engineering_data:
                order_data = OrderData(
                    customer=row.get('Customer', ''),
                    lot_id=row.get('Lot ID', ''),
                    lot_type=row.get('Lot Type', ''),
                    device=row.get('Device', ''),
                    qty=self._safe_int_convert(row.get('Qty', 0)),
                    urgent=row.get('Urgent', False),
                    owner=row.get('Owner', ''),
                    note=row.get('Note', ''),
                    classification='engineering',
                    source_file=row.get('源文件', ''),
                    created_by=current_user.username if current_user.is_authenticated else 'system'
                )
                db.session.add(order_data)
                saved_count += 1
            
            # 保存量产订单数据
            for row in production_data:
                order_data = OrderData(
                    customer=row.get('Customer', ''),
                    lot_id=row.get('Lot ID', ''),
                    lot_type=row.get('Lot Type', ''),
                    device=row.get('Device', ''),
                    qty=self._safe_int_convert(row.get('Qty', 0)),
                    urgent=row.get('Urgent', False),
                    owner=row.get('Owner', ''),
                    note=row.get('Note', ''),
                    classification='production',
                    source_file=row.get('源文件', ''),
                    created_by=current_user.username if current_user.is_authenticated else 'system'
                )
                db.session.add(order_data)
                saved_count += 1
            
            db.session.commit()
            logger.info(f"成功保存 {saved_count} 条数据到数据库")
            
            # 生成汇总表
            self._generate_summary_tables(engineering_data, production_data)
            
            # 发布数据存储事件
            self.event_bus.publish_event(
                self.event_bus.EVENT_TYPES['DATA_STORED'],
                {
                    'saved_count': saved_count,
                    'engineering_count': len(engineering_data),
                    'production_count': len(production_data)
                }
            )
            
            return saved_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"保存数据失败: {e}")
            raise

    def _generate_summary_tables(self, engineering_data: List[Dict], production_data: List[Dict]):
        """生成FT工程和FT量产汇总表"""
        try:
            # 确保downloads目录存在
            os.makedirs("downloads", exist_ok=True)
            
            # 生成FT工程订单汇总表
            if engineering_data:
                engineering_df = pd.DataFrame(engineering_data)
                
                # 如果文件已存在，追加数据
                if os.path.exists(self.engineering_summary_file):
                    existing_df = pd.read_excel(self.engineering_summary_file)
                    combined_df = pd.concat([existing_df, engineering_df], ignore_index=True)
                    # 去重处理（基于订单号和Lot ID）
                    combined_df = combined_df.drop_duplicates(subset=['订单号', 'Lot ID'], keep='last')
                else:
                    combined_df = engineering_df
                
                combined_df.to_excel(self.engineering_summary_file, index=False)
                logger.info(f"更新FT工程订单汇总表: {len(engineering_data)} 条新数据")
            
            # 生成FT量产订单汇总表
            if production_data:
                production_df = pd.DataFrame(production_data)
                
                # 如果文件已存在，追加数据
                if os.path.exists(self.production_summary_file):
                    existing_df = pd.read_excel(self.production_summary_file)
                    combined_df = pd.concat([existing_df, production_df], ignore_index=True)
                    # 去重处理（基于订单号和Lot ID）
                    combined_df = combined_df.drop_duplicates(subset=['订单号', 'Lot ID'], keep='last')
                else:
                    combined_df = production_df
                
                combined_df.to_excel(self.production_summary_file, index=False)
                logger.info(f"更新FT量产订单汇总表: {len(production_data)} 条新数据")
                
        except Exception as e:
            logger.error(f"生成汇总表失败: {e}")
            raise

    def _safe_int_convert(self, value) -> int:
        """安全的整数转换"""
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                return int(float(value.replace(',', '')))
            else:
                return 0
        except (ValueError, TypeError):
            return 0

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.task_manager.get_task(task_id)
        return task.to_dict() if task else None

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return self.task_manager.cancel_task(task_id)

    def get_user_tasks(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的任务列表"""
        tasks = self.task_manager.get_user_tasks(user_id)
        return [task.to_dict() for task in tasks]

    def is_healthy(self) -> bool:
        """检查订单处理服务健康状态
        
        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # 检查关键组件
            if not self.event_bus:
                logger.error("事件总线未初始化")
                return False
                
            if not self.task_manager:
                logger.error("任务管理器未初始化")
                return False
                
            if not self.email_processor:
                logger.error("邮件处理器未初始化")
                return False
                
            if not self.excel_parser:
                logger.error("Excel解析器未初始化")
                return False
            
            # 检查事件总线健康状态
            if hasattr(self.event_bus, 'is_healthy') and not self.event_bus.is_healthy():
                logger.error("事件总线状态异常")
                return False
                
            # 检查任务管理器健康状态
            if hasattr(self.task_manager, 'is_healthy') and not self.task_manager.is_healthy():
                logger.error("任务管理器状态异常")
                return False
            
            # 检查数据库连接
            try:
                db.session.execute('SELECT 1')
                db.session.commit()
            except Exception as e:
                logger.error(f"数据库连接异常: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return False

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取订单处理统计信息
        
        Returns:
            dict: 处理统计信息
        """
        try:
            # 获取任务统计
            user_id = str(current_user.id) if current_user.is_authenticated else "system"
            user_tasks = self.get_user_tasks(user_id)
            
            # 按状态分组统计
            task_stats = {
                'total': len(user_tasks),
                'running': len([t for t in user_tasks if t.get('status') == 'running']),
                'completed': len([t for t in user_tasks if t.get('status') == 'completed']),
                'failed': len([t for t in user_tasks if t.get('status') == 'failed']),
                'pending': len([t for t in user_tasks if t.get('status') == 'pending'])
            }
            
            # 获取订单数据统计
            try:
                total_orders = db.session.query(OrderData).count()
                engineering_orders = db.session.query(OrderData).filter_by(classification='engineering').count()
                production_orders = db.session.query(OrderData).filter_by(classification='production').count()
                
                order_stats = {
                    'total_orders': total_orders,
                    'engineering_orders': engineering_orders,
                    'production_orders': production_orders,
                    'unknown_orders': total_orders - engineering_orders - production_orders
                }
            except Exception as e:
                logger.warning(f"获取订单统计失败: {e}")
                order_stats = {
                    'total_orders': 0,
                    'engineering_orders': 0,
                    'production_orders': 0,
                    'unknown_orders': 0
                }
            
            # 检查汇总表状态
            summary_files_status = {
                'engineering_summary_exists': os.path.exists(self.engineering_summary_file),
                'production_summary_exists': os.path.exists(self.production_summary_file),
                'engineering_summary_size': os.path.getsize(self.engineering_summary_file) if os.path.exists(self.engineering_summary_file) else 0,
                'production_summary_size': os.path.getsize(self.production_summary_file) if os.path.exists(self.production_summary_file) else 0
            }
            
            return {
                'status': 'healthy' if self.is_healthy() else 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'task_stats': task_stats,
                'order_stats': order_stats,
                'summary_files': summary_files_status,
                'parser_info': {
                    'type': 'EnhancedExcelParser',
                    'downloads_dir': self.excel_parser.downloads_dir,
                    'search_subdirs': self.excel_parser.search_subdirs,
                    'horizontal_extraction_enabled': self.excel_parser.horizontal_extraction_enabled
                }
            }
            
        except Exception as e:
            logger.error(f"获取处理统计失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }


# 全局服务实例
_order_processing_service = None

def get_order_processing_service() -> OrderProcessingService:
    """获取订单处理服务的全局实例"""
    global _order_processing_service
    if _order_processing_service is None:
        _order_processing_service = OrderProcessingService()
    return _order_processing_service 
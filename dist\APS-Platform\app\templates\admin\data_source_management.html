<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源管理 - APS系统</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-database me-2"></i>数据源管理</h2>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    系统支持MySQL实时数据和Excel备用数据两种数据源。正常情况下优先使用MySQL数据，当MySQL数据异常时可切换到Excel备用数据。
                </div>
                
                <div class="mb-3">
                    <button id="refreshBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> 刷新状态
                    </button>
                    <button id="healthCheckBtn" class="btn btn-success">
                        <i class="fas fa-heartbeat"></i> 健康检查
                    </button>
                </div>
                
                <div id="dataSourceCards" class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载数据源状态...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 页面加载时自动获取状态
        document.addEventListener('DOMContentLoaded', function() {
            loadDataSourceStatus();
        });
        
        // 刷新按钮事件
        document.getElementById('refreshBtn').addEventListener('click', function() {
            loadDataSourceStatus();
        });
        
        // 健康检查按钮事件  
        document.getElementById('healthCheckBtn').addEventListener('click', function() {
            performHealthCheck();
        });
        
        function loadDataSourceStatus() {
            const container = document.getElementById('dataSourceCards');
            container.innerHTML = '<div class="col-12"><div class="card"><div class="card-body text-center"><div class="spinner-border" role="status"></div><p class="mt-2">正在加载数据源状态...</p></div></div></div>';
            
            fetch('/admin/data-source/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    renderDataSourceCards(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    container.innerHTML = '<div class="col-12"><div class="alert alert-danger">获取数据源状态失败: ' + error.message + '</div></div>';
                });
        }
        
        function renderDataSourceCards(data) {
            const container = document.getElementById('dataSourceCards');
            container.innerHTML = '';
            
            if (!data.tables || Object.keys(data.tables).length === 0) {
                container.innerHTML = '<div class="col-12"><div class="alert alert-warning">未找到数据源信息</div></div>';
                return;
            }
            
            Object.entries(data.tables).forEach(([tableName, info]) => {
                const card = createDataSourceCard(tableName, info);
                container.appendChild(card);
            });
        }
        
        function createDataSourceCard(tableName, info) {
            const col = document.createElement('div');
            col.className = 'col-lg-6 col-xl-4 mb-4';
            
            const recommended = info.recommended?.source || 'none';
            const cardClass = recommended !== 'none' ? 'border-success' : 'border-danger';
            
            col.innerHTML = `
                <div class="card ${cardClass}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-table me-2"></i>${tableName}</h6>
                        <span class="badge bg-${recommended === 'mysql' ? 'primary' : recommended === 'excel' ? 'warning' : 'danger'}">
                            ${recommended === 'mysql' ? 'MySQL' : recommended === 'excel' ? 'Excel' : '无可用数据源'}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="text-muted">MySQL数据源</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-circle text-${info.mysql?.healthy ? 'success' : 'danger'} me-2"></i>
                                    <small>${info.mysql?.status || '未知状态'}</small>
                                </div>
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="showSwitchModal('${tableName}', 'mysql')"
                                        ${!info.mysql?.healthy ? 'disabled' : ''}>
                                    切换到MySQL
                                </button>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted">Excel数据源</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-circle text-${info.excel?.healthy ? 'success' : 'danger'} me-2"></i>
                                    <small>${info.excel?.status || '未知状态'}</small>
                                </div>
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="showSwitchModal('${tableName}', 'excel')"
                                        ${!info.excel?.healthy ? 'disabled' : ''}>
                                    切换到Excel
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <small class="text-muted">
                            当前使用: ${info.recommended?.table_name || '无'}
                        </small>
                    </div>
                </div>
            `;
            
            return col;
        }
        
        function showSwitchModal(tableName, targetSource) {
            if (confirm(`确定要将表 ${tableName} 的数据源切换到 ${targetSource === 'mysql' ? 'MySQL' : 'Excel'} 吗？`)) {
                performSwitch(tableName, targetSource);
            }
        }
        
        function performSwitch(tableName, targetSource) {
            fetch('/admin/data-source/api/switch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    table_name: tableName,
                    target_source: targetSource
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`成功切换 ${tableName} 到 ${targetSource} 数据源`);
                    loadDataSourceStatus(); // 重新加载状态
                } else {
                    alert(`切换失败: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('切换操作失败');
            });
        }
        
        function performHealthCheck() {
            const btn = document.getElementById('healthCheckBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';
            btn.disabled = true;
            
            fetch('/admin/data-source/api/health-check')
                .then(response => response.json())
                .then(data => {
                    alert('健康检查完成');
                    loadDataSourceStatus(); // 重新加载状态
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('健康检查失败');
                })
                .finally(() => {
                    btn.innerHTML = '<i class="fas fa-heartbeat"></i> 健康检查';
                    btn.disabled = false;
                });
        }
    </script>
</body>
</html>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 精简日志配置工具
解决日志配置分散、格式不统一的问题
"""

import os
import logging
import logging.handlers
from datetime import datetime
from flask import Flask, request, g

def setup_logging(app: Flask):
    """设置统一的日志配置"""
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    # 清除现有处理器避免重复
    app.logger.handlers.clear()
    
    # 统一日志格式
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 1. 应用主日志 - 合理的轮转大小
    app_handler = logging.handlers.RotatingFileHandler(
        'logs/aps_app.log',
        maxBytes=20*1024*1024,  # 20MB
        backupCount=5,
        encoding='utf-8'
    )
    app_handler.setFormatter(formatter)
    app_handler.setLevel(logging.INFO)
    
    # 2. 错误日志 - 只记录错误和严重问题
    error_handler = logging.handlers.RotatingFileHandler(
        'logs/aps_error.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 3. 控制台输出 - 开发环境友好
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    ))
    console_handler.setLevel(logging.INFO)
    
    # 添加处理器到应用
    app.logger.addHandler(app_handler)
    app.logger.addHandler(error_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(logging.INFO)
    
    # 设置第三方库日志级别，减少噪音
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    app.logger.info('✅ 统一日志系统已配置')

def log_user_action(username: str, action: str, resource: str = '', details: str = ''):
    """记录用户操作 - 精简版"""
    logger = logging.getLogger('user_actions')
    
    # 如果没有处理器，添加一个
    if not logger.handlers:
        handler = logging.handlers.RotatingFileHandler(
            'logs/user_actions.log',
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        formatter = logging.Formatter(
            '%(asctime)s | %(username)s | %(action)s | %(resource)s | %(details)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        logger.propagate = False
    
    # 记录操作
    logger.info('', extra={
        'username': username,
        'action': action,
        'resource': resource,
        'details': details
    })

def cleanup_old_logs(days_to_keep: int = 30):
    """清理旧日志文件"""
    import glob
    from pathlib import Path
    
    cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
    cleaned_count = 0
    
    # 查找所有日志文件
    log_patterns = [
        'logs/*.log.*',  # 轮转的日志文件
        'logs/*.log.gz',  # 压缩的日志文件
        '*.log'  # 根目录的日志文件
    ]
    
    for pattern in log_patterns:
        for log_file in glob.glob(pattern):
            try:
                file_path = Path(log_file)
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    cleaned_count += 1
                    print(f"已删除旧日志: {log_file}")
            except Exception as e:
                print(f"删除日志文件失败: {log_file}, 错误: {e}")
    
    print(f"清理完成，共删除 {cleaned_count} 个旧日志文件")
    return cleaned_count

def get_log_summary():
    """获取日志摘要信息"""
    import glob
    from pathlib import Path
    
    summary = {
        'total_files': 0,
        'total_size_mb': 0,
        'files': []
    }
    
    # 统计所有日志文件
    for log_file in glob.glob('logs/*.log*') + glob.glob('*.log'):
        try:
            file_path = Path(log_file)
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                
                summary['files'].append({
                    'name': log_file,
                    'size_mb': round(size_mb, 2),
                    'modified': modified.strftime('%Y-%m-%d %H:%M:%S')
                })
                summary['total_size_mb'] += size_mb
                summary['total_files'] += 1
        except Exception:
            continue
    
    summary['total_size_mb'] = round(summary['total_size_mb'], 2)
    summary['files'].sort(key=lambda x: x['size_mb'], reverse=True)
    
    return summary

# 装饰器：记录函数执行时间（精简版）
def log_execution_time(func):
    """装饰器：记录慢查询和异常"""
    from functools import wraps
    import time
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        logger = logging.getLogger('performance')
        
        try:
            result = func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000
            
            # 只记录慢操作（超过1秒）
            if execution_time > 1000:
                logger.warning(f'慢操作: {func.__name__} 耗时 {execution_time:.0f}ms')
            
            return result
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f'操作失败: {func.__name__} 耗时 {execution_time:.0f}ms, 错误: {str(e)}')
            raise
    
    return wrapper 
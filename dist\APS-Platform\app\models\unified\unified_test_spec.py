"""
统一测试规范模型

整合TestSpec和Test_Spec两个重复模型的功能，
提供统一的测试规范数据管理接口。

设计原则：
1. 以Test_Spec为基础（实际业务使用）
2. 整合TestSpec的现代化设计理念
3. 通过JSON字段存储复杂测试参数
4. 保持向后兼容性
"""

from datetime import datetime
from app import db
import json


class UnifiedTestSpec(db.Model):
    """统一测试规范模型"""
    __tablename__ = 'unified_test_specs'
    
    # 核心标识
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    TEST_SPEC_ID = db.Column(db.String(50), nullable=False, unique=True, index=True, comment='测试规范ID')
    TEST_SPEC_NAME = db.Column(db.String(100), nullable=False, comment='测试规范名称')
    TEST_SPEC_VER = db.Column(db.String(20), nullable=False, comment='版本号')
    
    # 产品关联
    DEVICE = db.Column(db.String(50), nullable=True, index=True, comment='产品型号')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片ID')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='产品ID')
    STAGE = db.Column(db.String(50), nullable=True, index=True, comment='工序')
    
    # 测试配置
    TESTER = db.Column(db.String(50), nullable=True, comment='测试机')
    HANDLER = db.Column(db.String(50), nullable=True, comment='分选机')
    TEST_SPEC_TYPE = db.Column(db.String(50), nullable=True, comment='测试规范类型')
    TEST_AREA = db.Column(db.String(50), nullable=True, comment='测试区域')
    TEMPERATURE = db.Column(db.String(50), nullable=True, comment='测试温度')
    
    # 硬件配置
    TB_PN = db.Column(db.String(50), nullable=True, comment='测试板型号')
    HB_PN = db.Column(db.String(50), nullable=True, comment='分选板型号')
    KIT_PN = db.Column(db.String(50), nullable=True, comment='KIT型号')
    SOCKET_PN = db.Column(db.String(50), nullable=True, comment='Socket型号')
    TESTER_CONFIG = db.Column(db.String(100), nullable=True, comment='测试机配置')
    
    # 程序配置
    FT_PROGRAM = db.Column(db.String(100), nullable=True, comment='FT程序')
    QA_PROGRAM = db.Column(db.String(100), nullable=True, comment='QA程序')
    GU_PROGRAM = db.Column(db.String(100), nullable=True, comment='GU程序')
    FT_PROGRAM_PATH = db.Column(db.String(200), nullable=True, comment='FT程序路径')
    QA_PROGRAM_PATH = db.Column(db.String(200), nullable=True, comment='QA程序路径')
    GU_PROGRAM_PATH = db.Column(db.String(200), nullable=True, comment='GU程序路径')
    
    # 性能参数
    UPH = db.Column(db.Integer, nullable=True, comment='每小时产量')
    TEST_TIME = db.Column(db.Float, nullable=True, comment='测试时间')
    STANDARD_YIELD = db.Column(db.Float, nullable=True, comment='标准良率')
    LOW_YIELD = db.Column(db.Float, nullable=True, comment='低良率')
    HIGH_YIELD = db.Column(db.Float, nullable=True, comment='高良率')
    DOWN_YIELD = db.Column(db.Float, nullable=True, comment='停机良率')
    
    # 数量配置
    ORT_QTY = db.Column(db.Integer, nullable=True, comment='ORT数量')
    REMAIN_QTY = db.Column(db.Integer, nullable=True, comment='剩余数量')
    
    # 状态管理
    status = db.Column(db.String(20), default='draft', comment='状态(draft/active/archived)')
    APPROVAL_STATE = db.Column(db.String(20), nullable=True, comment='审批状态')
    ACTV_YN = db.Column(db.String(1), default='Y', comment='是否激活')
    
    # 审批信息
    APPROVE_USER = db.Column(db.String(50), nullable=True, comment='审批人')
    APPROVE_TIME = db.Column(db.String(50), nullable=True, comment='审批时间')
    TEST_ENG = db.Column(db.String(50), nullable=True, comment='测试工程师')
    
    # 工厂信息
    FAC_ID = db.Column(db.String(50), nullable=True, comment='工厂ID')
    SUB_FAC = db.Column(db.String(50), nullable=True, comment='子工厂')
    COMPANY_ID = db.Column(db.String(50), nullable=True, comment='公司ID')
    
    # 测试参数 (JSON格式存储复杂参数)
    test_parameters = db.Column(db.Text, nullable=True, comment='测试参数JSON')
    
    # 扩展数据 (存储Test_Spec的其他字段)
    extended_data = db.Column(db.Text, nullable=True, comment='扩展数据JSON')
    
    # 数据源跟踪
    source_table = db.Column(db.String(20), nullable=True, comment='数据来源表')
    migration_status = db.Column(db.String(20), default='pending', comment='迁移状态')
    data_version = db.Column(db.Integer, default=1, comment='数据版本')
    
    # 审计字段
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    created_by = db.Column(db.String(64), nullable=True, comment='创建人')
    updated_by = db.Column(db.String(64), nullable=True, comment='更新人')
    
    # 索引定义
    __table_args__ = (
        db.Index('idx_unified_spec_device_stage', 'DEVICE', 'STAGE'),
        db.Index('idx_unified_spec_type_status', 'TEST_SPEC_TYPE', 'status'),
        db.Index('idx_unified_spec_tester', 'TESTER', 'HANDLER'),
        db.Index('idx_unified_spec_version', 'TEST_SPEC_ID', 'TEST_SPEC_VER'),
        {'comment': '统一测试规范表 - 整合TestSpec和Test_Spec功能'}
    )
    
    def __repr__(self):
        return f'<UnifiedTestSpec {self.TEST_SPEC_ID}>'
    
    def to_dict(self):
        """转换为字典格式"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if value is not None:
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
        
        # 解析测试参数
        if self.test_parameters:
            try:
                params = json.loads(self.test_parameters)
                result['test_parameters_parsed'] = params
            except (json.JSONDecodeError, TypeError):
                result['test_parameters_parsed'] = {}
        
        # 解析扩展数据
        if self.extended_data:
            try:
                extended = json.loads(self.extended_data)
                result['extended_data_parsed'] = extended
            except (json.JSONDecodeError, TypeError):
                result['extended_data_parsed'] = {}
        
        return result
    
    def set_test_parameters(self, params_dict):
        """设置测试参数"""
        if params_dict:
            self.test_parameters = json.dumps(params_dict, ensure_ascii=False)
        else:
            self.test_parameters = None
    
    def get_test_parameters(self):
        """获取测试参数"""
        if self.test_parameters:
            try:
                return json.loads(self.test_parameters)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def set_extended_data(self, data_dict):
        """设置扩展数据"""
        if data_dict:
            self.extended_data = json.dumps(data_dict, ensure_ascii=False)
        else:
            self.extended_data = None
    
    def get_extended_data(self):
        """获取扩展数据"""
        if self.extended_data:
            try:
                return json.loads(self.extended_data)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def update_from_test_spec(self, test_spec_record):
        """从Test_Spec记录更新数据"""
        self.TEST_SPEC_ID = test_spec_record.TEST_SPEC_ID
        self.TEST_SPEC_NAME = test_spec_record.TEST_SPEC_NAME
        self.TEST_SPEC_VER = test_spec_record.TEST_SPEC_VER
        self.STAGE = test_spec_record.STAGE
        self.TESTER = test_spec_record.TESTER
        self.TEST_SPEC_TYPE = test_spec_record.TEST_SPEC_TYPE
        self.APPROVAL_STATE = test_spec_record.APPROVAL_STATE
        self.ACTV_YN = test_spec_record.ACTV_YN
        self.PROD_ID = test_spec_record.PROD_ID
        self.DEVICE = test_spec_record.DEVICE
        self.CHIP_ID = test_spec_record.CHIP_ID
        self.PKG_PN = test_spec_record.PKG_PN
        self.COMPANY_ID = test_spec_record.COMPANY_ID
        self.APPROVE_USER = test_spec_record.APPROVE_USER
        self.APPROVE_TIME = test_spec_record.APPROVE_TIME
        self.ORT_QTY = self._safe_int_convert(test_spec_record.ORT_QTY)
        self.REMAIN_QTY = self._safe_int_convert(test_spec_record.REMAIN_QTY)
        self.STANDARD_YIELD = self._safe_float_convert(test_spec_record.STANDARD_YIELD)
        self.LOW_YIELD = self._safe_float_convert(test_spec_record.LOW_YIELD)
        self.DOWN_YIELD = self._safe_float_convert(test_spec_record.DOWN_YIELD)
        self.HIGH_YIELD = self._safe_float_convert(test_spec_record.HIGH_YIELD)
        self.TEST_AREA = test_spec_record.TEST_AREA
        self.HANDLER = test_spec_record.HANDLER
        self.TEMPERATURE = test_spec_record.TEMPERATURE
        self.FT_PROGRAM = test_spec_record.FT_PROGRAM
        self.QA_PROGRAM = test_spec_record.QA_PROGRAM
        self.GU_PROGRAM = test_spec_record.GU_PROGRAM
        self.TB_PN = test_spec_record.TB_PN
        self.HB_PN = test_spec_record.HB_PN
        self.TEST_TIME = self._safe_float_convert(test_spec_record.TEST_TIME)
        self.UPH = self._safe_int_convert(test_spec_record.UPH)
        self.TESTER_CONFIG = test_spec_record.TESTER_CONFIG
        self.KIT_PN = test_spec_record.KIT_PN
        self.SOCKET_PN = test_spec_record.SOCKET_PN
        self.SUB_FAC = test_spec_record.SUB_FAC
        self.FAC_ID = test_spec_record.FAC_ID
        self.TEST_ENG = test_spec_record.TEST_ENG
        self.FT_PROGRAM_PATH = test_spec_record.FT_PROGRAM_PATH
        self.QA_PROGRAM_PATH = test_spec_record.QA_PROGRAM_PATH
        self.GU_PROGRAM_PATH = test_spec_record.GU_PROGRAM_PATH
        
        # 将其他字段存储到扩展数据中
        extended_fields = {}
        for column in test_spec_record.__table__.columns:
            if column.name not in [
                'id', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE',
                'TESTER', 'TEST_SPEC_TYPE', 'APPROVAL_STATE', 'ACTV_YN', 'PROD_ID',
                'DEVICE', 'CHIP_ID', 'PKG_PN', 'COMPANY_ID', 'APPROVE_USER',
                'APPROVE_TIME', 'ORT_QTY', 'REMAIN_QTY', 'STANDARD_YIELD',
                'LOW_YIELD', 'DOWN_YIELD', 'HIGH_YIELD', 'TEST_AREA', 'HANDLER',
                'TEMPERATURE', 'FT_PROGRAM', 'QA_PROGRAM', 'GU_PROGRAM', 'TB_PN',
                'HB_PN', 'TEST_TIME', 'UPH', 'TESTER_CONFIG', 'KIT_PN',
                'SOCKET_PN', 'SUB_FAC', 'FAC_ID', 'TEST_ENG', 'FT_PROGRAM_PATH',
                'QA_PROGRAM_PATH', 'GU_PROGRAM_PATH', 'created_at'
            ]:
                value = getattr(test_spec_record, column.name)
                if value is not None:
                    extended_fields[column.name] = str(value)
        
        if extended_fields:
            self.set_extended_data(extended_fields)
        
        self.source_table = 'Test_Spec'
    
    def update_from_testspec(self, testspec_record):
        """从TestSpec记录更新数据"""
        self.TEST_SPEC_ID = testspec_record.name  # TestSpec使用name作为ID
        self.TEST_SPEC_NAME = testspec_record.name
        self.TEST_SPEC_VER = testspec_record.version
        self.status = testspec_record.status
        
        # 解析parameters字段
        if testspec_record.parameters:
            try:
                params = json.loads(testspec_record.parameters)
                self.set_test_parameters(params)
            except (json.JSONDecodeError, TypeError):
                pass
        
        self.source_table = 'TestSpec'
    
    def _safe_int_convert(self, value):
        """安全转换为整数"""
        if value is None or value == '':
            return None
        try:
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None
    
    def _safe_float_convert(self, value):
        """安全转换为浮点数"""
        if value is None or value == '':
            return None
        try:
            return float(str(value))
        except (ValueError, TypeError):
            return None
    
    @classmethod
    def find_by_spec_id(cls, spec_id):
        """根据规范ID查找记录"""
        return cls.query.filter_by(TEST_SPEC_ID=spec_id).first()
    
    @classmethod
    def find_by_device_stage(cls, device, stage):
        """根据产品和工序查找记录"""
        return cls.query.filter_by(DEVICE=device, STAGE=stage).all()
    
    @classmethod
    def find_active_specs(cls):
        """查找激活的测试规范"""
        return cls.query.filter(
            cls.status == 'active',
            cls.ACTV_YN == 'Y'
        ).all()
    
    @classmethod
    def find_by_tester(cls, tester):
        """根据测试机查找规范"""
        return cls.query.filter_by(TESTER=tester).all()
    
    @classmethod
    def find_by_product(cls, device, chip_id=None, pkg_pn=None):
        """根据产品信息查找规范"""
        query = cls.query.filter_by(DEVICE=device)
        if chip_id:
            query = query.filter_by(CHIP_ID=chip_id)
        if pkg_pn:
            query = query.filter_by(PKG_PN=pkg_pn)
        return query.all()
    
    def is_active(self):
        """检查规范是否激活"""
        return self.status == 'active' and self.ACTV_YN == 'Y'
    
    def get_yield_range(self):
        """获取良率范围"""
        return {
            'standard': self.STANDARD_YIELD,
            'low': self.LOW_YIELD,
            'high': self.HIGH_YIELD,
            'down': self.DOWN_YIELD
        }
    
    def get_program_paths(self):
        """获取程序路径"""
        return {
            'ft_program': self.FT_PROGRAM,
            'ft_path': self.FT_PROGRAM_PATH,
            'qa_program': self.QA_PROGRAM,
            'qa_path': self.QA_PROGRAM_PATH,
            'gu_program': self.GU_PROGRAM,
            'gu_path': self.GU_PROGRAM_PATH
        }
    
    def get_hardware_config(self):
        """获取硬件配置"""
        return {
            'tester': self.TESTER,
            'handler': self.HANDLER,
            'tb_pn': self.TB_PN,
            'hb_pn': self.HB_PN,
            'kit_pn': self.KIT_PN,
            'socket_pn': self.SOCKET_PN,
            'tester_config': self.TESTER_CONFIG,
            'temperature': self.TEMPERATURE
        } 
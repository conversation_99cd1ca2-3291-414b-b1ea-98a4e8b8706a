# 数据库管理脚本 (Database Management Script)
# 版本: 1.0
# 功能: 导出或导入 APS 项目所需的 MySQL 数据库。

# --- 配置区域 ---
# 请根据你的 MySQL 环境修改以下连接信息。
$DbUser     = "root"
$DbPassword = "WWWwww123!"
$DbHost     = "localhost"

# 需要备份的数据库名称列表。
$DbNames = @("aps", "aps_system")

# 备份文件存放的目录。
$BackupDir = ".\database_backups"
# --- 配置结束 ---


# 函数：检查一个外部命令是否存在 (如 mysqldump, mysql)
function Test-CommandExists {
    param($command)
    # Get-Command 会在找不到命令时抛出错误，我们用 -ErrorAction SilentlyContinue 来抑制错误并返回 $null
    return (Get-Command $command -ErrorAction SilentlyContinue)
}

# 函数：导出数据库
function Export-Databases {
    if (-not (Test-CommandExists mysqldump)) {
        Write-Host "错误: 'mysqldump' 命令未找到。" -ForegroundColor Red
        Write-Host "请确保 MySQL 客户端工具已经安装，并且其 bin 目录已添加到系统的 PATH 环境变量中。" -ForegroundColor Yellow
        return
    }

    # 如果备份目录不存在，则创建它
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Force -Path $BackupDir
        Write-Host "已创建备份目录: $BackupDir"
    }

    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = Join-Path $BackupDir "aps_backup_$timestamp.sql"

    Write-Host "正在导出数据库到: $backupFile ..." -ForegroundColor Cyan

    # --routines: 导出存储过程和函数
    # --triggers: 导出触发器
    # --databases: 指定要导出的数据库，并会在脚本中包含 'CREATE DATABASE' 语句
    $dumpCommand = "mysqldump --host=$DbHost --user=$DbUser --password=$DbPassword --routines --triggers --databases $($DbNames -join ' ')"

    try {
        # 使用 Invoke-Expression 执行命令并将输出重定向到文件
        Invoke-Expression -Command "$dumpCommand > `"$backupFile`""
        Write-Host "✅ 数据库导出成功！" -ForegroundColor Green
        Write-Host "备份文件位于: $backupFile"
    }
    catch {
        Write-Host "❌ 数据库导出失败。" -ForegroundColor Red
        Write-Host $_.Exception.Message
    }
}

# 函数：导入数据库
function Import-Databases {
    param(
        [string]$inputFile
    )

    if (-not (Test-Path $inputFile)) {
        Write-Host "错误: 指定的导入文件不存在: $inputFile" -ForegroundColor Red
        return
    }

    if (-not (Test-CommandExists mysql)) {
        Write-Host "错误: 'mysql' 命令未找到。" -ForegroundColor Red
        Write-Host "请确保 MySQL 客户端工具已经安装，并且其 bin 目录已添加到系统的 PATH 环境变量中。" -ForegroundColor Yellow
        return
    }

    Write-Host "正在从文件导入数据库: $inputFile ..." -ForegroundColor Cyan
    Write-Host "警告：此操作将可能会覆盖目标数据库中的同名数据表，请谨慎操作！" -ForegroundColor Yellow

    $importCommand = "mysql --host=$DbHost --user=$DbUser --password=$DbPassword"

    try {
        # 使用 Get-Content 读取文件内容并通过管道传给 mysql 命令
        Get-Content $inputFile | Invoke-Expression -Command $importCommand
        Write-Host "✅ 数据库导入成功！" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 数据库导入失败。" -ForegroundColor Red
        Write-Host $_.Exception.Message
    }
}

# --- 脚本主逻辑 ---
# 使用 param 定义脚本接受的参数
param(
    # 'Action' 是一个强制参数，只能是 'export' 或 'import'
    [Parameter(Mandatory=$true, Position=0, HelpMessage="要执行的操作: 'export' 或 'import'")]
    [ValidateSet('export', 'import')]
    [string]$Action,

    # 'FilePath' 是 'import' 操作需要的可选参数
    [Parameter(Position=1, HelpMessage="当操作为 'import' 时，需要提供 .sql 文件的路径")]
    [string]$FilePath
)

# 根据用户提供的 Action 参数，执行相应的函数
switch ($Action) {
    "export" { Export-Databases }
    "import" {
        if (-not $FilePath) {
            Write-Host "错误: 'import' 操作需要提供一个 .sql 文件的路径。" -ForegroundColor Red
            return
        }
        Import-Databases -inputFile $FilePath
    }
} 
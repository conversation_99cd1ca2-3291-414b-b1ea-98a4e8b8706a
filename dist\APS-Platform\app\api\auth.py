from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from app.api import bp
from app.models import User, MenuSetting, UserPermission
from app import db
from functools import wraps
from app.decorators import admin_required
from app.config.menu_config import MENU_CONFIG, MENU_ID_MAP, get_menu_by_id, get_all_menu_ids
import logging

logger = logging.getLogger(__name__)

# 使用app/decorators.py中的admin_required，删除重复定义

@bp.route('/auth/users', methods=['GET'])
@login_required
@admin_required
def get_users():
    """获取用户列表"""
    users = User.query.all()
    return jsonify([{
        'username': user.username,
        'role': user.role,
        'created_at': user.created_at.isoformat()
    } for user in users])

@bp.route('/auth/users', methods=['POST'])
@login_required
@admin_required
def create_user():
    """创建用户"""
    data = request.get_json()
    if not data or not all(k in data for k in ('username', 'password', 'role')):
        return jsonify({'error': 'Missing required fields: username, password, role'}), 400
    
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': 'Username already exists'}), 400
    
    if data['role'] not in ['admin', 'Operator', 'boss']:
        return jsonify({'error': 'Invalid role. Must be: admin, Operator, boss'}), 400
    
    user = User(username=data['username'], role=data['role'])
    user.set_password(data['password'])
    db.session.add(user)
    db.session.commit()
    
    logger.info(f"用户 {data['username']} 创建成功，角色: {data['role']}")
    return jsonify({
        'username': user.username,
        'role': user.role,
        'created_at': user.created_at.isoformat()
    }), 201

@bp.route('/auth/users/<username>', methods=['GET'])
@login_required
@admin_required
def get_user(username):
    """获取用户详情"""
    user = User.query.get_or_404(username)
    return jsonify({
        'username': user.username,
        'role': user.role,
        'created_at': user.created_at.isoformat()
    })

@bp.route('/auth/users/<username>', methods=['PUT'])
@login_required
@admin_required
def update_user(username):
    """更新用户"""
    user = User.query.get_or_404(username)
    data = request.get_json()
    
    if 'role' in data:
        if data['role'] not in ['admin', 'Operator', 'boss']:
            return jsonify({'error': 'Invalid role. Must be: admin, Operator, boss'}), 400
        user.role = data['role']
    
    if 'password' in data:
        user.set_password(data['password'])
    
    db.session.commit()
    logger.info(f"用户 {username} 更新成功")
    return jsonify({
        'username': user.username,
        'role': user.role,
        'created_at': user.created_at.isoformat()
    })

@bp.route('/auth/users/<username>', methods=['DELETE'])
@login_required
@admin_required
def delete_user(username):
    """删除用户"""
    user = User.query.get_or_404(username)
    
    # 防止删除自己
    if user.username == current_user.username:
        return jsonify({'error': 'Cannot delete yourself'}), 400
    
    # 防止删除admin（如果当前用户不是admin）
    if user.role == 'admin' and current_user.role != 'admin':
        return jsonify({'error': 'Cannot delete admin user'}), 403
    
    try:
        # 先删除用户相关的权限记录
        deleted_permissions = UserPermission.query.filter_by(username=username).delete()
        logger.info(f"删除了用户 {username} 的 {deleted_permissions} 个权限记录")
        
        # MySQL不需要PRAGMA语句，直接删除用户
        # 由于我们已经移除了外键约束，可以直接删除
        db.session.delete(user)
        db.session.commit()
        
        logger.info(f"成功删除用户: {username}")
        return '', 204
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除用户 {username} 时出错: {str(e)}")
        return jsonify({'error': f'删除用户失败: {str(e)}'}), 500

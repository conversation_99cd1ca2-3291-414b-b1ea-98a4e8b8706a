{"meta": {"version": "1.0", "generated_at": "2025-06-25T21:46:40.392229", "description": "自动生成的动态字段映射配置文件", "total_tables": 49}, "tables": {"apscheduler_jobs": {"display_name": "apscheduler_jobs", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "next_run_time", "job_state"], "datetime_fields": ["next_run_time"], "field_count": 3, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.991276", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "cp_order_summary": {"display_name": "cp_order_summary", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "processing_type", "contractor_name", "contractor_contact", "contractor_address", "contractor_phone", "contractor_fax", "client_name", "client_contact", "client_location", "client_phone", "client_fax", "order_number", "product_name", "chip_name", "chip_batch", "processing_pieces", "finished_model", "wafer_numbers", "cp_mapping", "package_method", "process_step", "shipping_address", "source_file", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 26, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.763805", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "ct": {"display_name": "ct", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "LOT_ID", "WORK_ORDER_ID", "PROD_ID", "DEVICE", "PKG_PN", "CHIP_ID", "FLOW_ID", "STAGE", "LOT_QTY", "ACT_QTY", "GOOD_QTY", "REJECT_QTY", "LOSS_QTY", "MAIN_EQP_ID", "AUXILIARY_EQP_ID", "LOT_START_TIME", "LOT_END_TIME", "SETUP_TIME", "FT_TEST_PROGRAM", "IS_HALF_LOT_DOWN", "FIRST_PASS_YIELD", "FINAL_YIELD", "VM_QTY", "ALARM_BIN", "CREATE_TIME", "CREATE_USER", "FAC_ID", "TRACK_CNT", "COST_TIME", "created_at", "updated_at"], "datetime_fields": ["LOT_START_TIME", "LOT_END_TIME", "SETUP_TIME", "EVENT_TIME", "CREATE_TIME", "COST_TIME", "created_at", "updated_at"], "field_count": 37, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.779533", "hidden_fields": [], "readonly_fields": ["id", "CREATE_TIME"], "required_fields": ["LOT_ID"]}, "customer_orders": {"display_name": "customer_orders", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "order_number", "customer_name", "status", "total_amount", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 7, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.793295", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "eqp_status": {"display_name": "eqp_status", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "HANDLER_ID", "HANDLER_TYPE", "TESTER_ID", "HANDLER_CONFIG", "SOCKET_PN", "KIT_PN", "EQP_CLASS", "EQP_TYPE", "TEMPERATURE_RANGE", "TEMPERATURE_CAPACITY", "LOT_ID", "DEVICE", "STATUS", "HB_PN", "TB_PN", "TESTER_CONFIG", "STAGE", "EVENT_TIME", "created_at", "updated_at"], "datetime_fields": ["EVENT_TIME", "created_at", "updated_at"], "field_count": 21, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.811148", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["LOT_ID"]}, "et_ft_test_spec": {"display_name": "et_ft_test_spec", "database": "aps", "primary_key": "id", "business_key": "DEVICE", "fields": ["id", "TEST_SPEC_ID", "TEST_SPEC_NAME", "TEST_SPEC_VER", "STAGE", "TESTER", "INV_ID", "TEST_SPEC_TYPE", "APPROVAL_STATE", "ACTV_YN", "PROD_ID", "DEVICE", "CHIP_ID", "PKG_PN", "COMPANY_ID", "NOTE", "APPROVE_USER", "APPROVE_TIME", "ORT_QTY", "REMAIN_QTY", "STANDARD_YIELD", "LOW_YIELD", "DOWN_YIELD", "TEST_AREA", "HANDLER", "TEMPERATURE", "FT_PROGRAM", "QA_PROGRAM", "GU_PROGRAM", "TB_PN", "HB_PN", "TIB", "TEST_TIME", "UPH", "SUFFIX_CODE", "TESTER_CONFIG", "GU_COMPARE_PARAM", "STA_COMPARE_PARAM", "DNR", "SITE", "DPAT", "BS_NAME", "GU_NAME", "C_SPEC", "TEST_ENG", "TEST_OPERATION", "ORDER_COMMENT", "HIGH_YIELD", "VISION_LOSS_YIELD", "VISION_YIELD", "LOSS_YIELD", "RETEST_YN", "FT_PROGRAM_PATH", "QA_PROGRAM_PATH", "GU_PROGRAM_PATH", "EFFECTIVE_TIME", "TPL_RULE_TEMP", "TPL_RULE_TEMP_PATH", "ALARM_DATE", "FAC_ID", "EDIT_STATE", "EDIT_TIME", "EDIT_USER", "EVENT", "CREATE_TIME", "CREATE_USER", "created_at", "updated_at"], "datetime_fields": ["DISABLE_TIME", "APPROVE_TIME", "TEST_TIME", "EFFECTIVE_TIME", "ALARM_DATE", "EDIT_TIME", "EVENT_TIME", "CREATE_TIME", "created_at", "updated_at"], "field_count": 75, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.818146", "hidden_fields": [], "readonly_fields": ["id", "CREATE_TIME"], "required_fields": ["DEVICE"]}, "et_recipe_file": {"display_name": "et_recipe_file", "database": "aps", "primary_key": "id", "business_key": "DEVICE", "fields": ["id", "PROD_ID", "STAGE", "DEVICE", "CHIP_ID", "PKG_PN", "RECIPE_FILE_NAME", "RECIPE_FILE_PATH", "APPROVAL_STATE", "FAC_ID", "CREATE_TIME", "CREATE_USER", "PROD_TYPE", "EQP_TYPE", "HANDLER_CONFIG", "RECIPE_VER", "KIT_PN", "SOCKET_PN", "FAMILY", "COORDINATE_ONE", "COORDINATE_TWO", "COORDINATE_THREE", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 35, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.823147", "hidden_fields": [], "readonly_fields": ["id", "DEVICE", "STAGE"], "required_fields": ["DEVICE"]}, "et_uph_eqp": {"display_name": "et_uph_eqp", "database": "aps", "primary_key": "id", "business_key": "DEVICE", "fields": ["id", "DEVICE", "PKG_PN", "STAGE", "UPH", "HANDLER", "FAC_ID", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 19, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.827877", "hidden_fields": [], "readonly_fields": ["id", "DEVICE", "PKG_PN", "STAGE"], "required_fields": ["DEVICE"]}, "et_wait_lot": {"display_name": "et_wait_lot", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "LOT_ID", "LOT_TYPE", "GOOD_QTY", "PROD_ID", "DEVICE", "CHIP_ID", "PKG_PN", "PO_ID", "STAGE", "WIP_STATE", "PROC_STATE", "HOLD_STATE", "FLOW_ID", "FLOW_VER", "FAC_ID", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 20, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.843878", "hidden_fields": [], "readonly_fields": ["id", "CREATE_TIME"], "required_fields": ["LOT_ID"]}, "excel_mappings": {"display_name": "excel_mappings", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "name", "sheet_name", "start_row", "header_row", "field_mappings", "key_fields", "date_format", "created_by", "created_at", "updated_at"], "datetime_fields": ["date_format", "created_by", "created_at", "updated_at"], "field_count": 11, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.128881", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "ft_order_summary": {"display_name": "ft_order_summary", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "order_date", "order_number", "label_name", "circuit_name", "chip_name", "wafer_size", "package_qty1", "package_qty2", "diffusion_batch", "wafer_number", "assembly_method", "drawing_number", "package_form", "stamp_line1", "stamp_line2", "stamp_line3", "other_notes", "delivery_date", "env_requirement", "msl_requirement", "reliability_requirement", "print_pin_dot", "pin_dot_position", "item_code", "shipping_address", "wafer_lot", "order_attribute", "lot_type1", "lot_type2", "lot_type3", "source_file", "import_time", "classification_result", "column_14", "created_at", "updated_at"], "datetime_fields": ["order_date", "delivery_date", "import_time", "created_at", "updated_at"], "field_count": 37, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.852954", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "lot_type_classification_rules": {"display_name": "lot_type_classification_rules", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "lot_type", "classification", "description", "created_by", "created_at", "updated_at"], "datetime_fields": ["created_by", "created_at", "updated_at"], "field_count": 7, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.855954", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "lotprioritydone": {"display_name": "已排产批次", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "PRIORITY", "HANDLER_ID", "LOT_ID", "LOT_TYPE", "GOOD_QTY", "PROD_ID", "DEVICE", "CHIP_ID", "PKG_PN", "PO_ID", "STAGE", "WIP_STATE", "PROC_STATE", "HOLD_STATE", "FLOW_ID", "FLOW_VER", "RELEASE_TIME", "FAC_ID", "CREATE_TIME", "SCHDULED_TIME", "match_type", "comprehensive_score", "processing_time", "changeover_time", "algorithm_version", "created_at", "updated_at"], "datetime_fields": ["RELEASE_TIME", "CREATE_TIME", "SCHDULED_TIME", "created_at", "updated_at"], "field_count": 28, "auto_discovered": true, "last_discovery": "2025-06-26T19:48:00.000000", "hidden_fields": [], "readonly_fields": ["id", "CREATE_TIME", "match_type", "comprehensive_score", "processing_time", "changeover_time", "algorithm_version"], "required_fields": ["LOT_ID", "HANDLER_ID"]}, "maintenance_records": {"display_name": "maintenance_records", "database": "aps", "primary_key": "id", "business_key": "resource_id", "fields": ["id", "resource_id", "maintenance_type", "description", "start_time", "end_time", "status", "operator", "result", "created_at"], "datetime_fields": ["start_time", "end_time", "created_at"], "field_count": 10, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.864943", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["resource_id"]}, "menu_settings": {"display_name": "menu_settings", "database": "aps", "primary_key": "id", "business_key": "parent_id", "fields": ["id", "menu_name", "parent_id", "is_visible", "order", "icon", "route"], "datetime_fields": [], "field_count": 7, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.868942", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["parent_id"]}, "product_priority_config": {"display_name": "product_priority_config", "database": "aps_system", "primary_key": "id", "business_key": "chip_id", "fields": ["id", "device", "stage", "pkg_pn", "chip_id", "priority_level", "priority_order", "lead_time_days", "uph_override", "notes", "created_by", "created_at", "updated_by", "updated_at"], "datetime_fields": ["lead_time_days", "created_by", "created_at", "updated_by", "updated_at"], "field_count": 14, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.192858", "hidden_fields": ["uph_override"], "readonly_fields": ["id"], "required_fields": ["chip_id"]}, "production_orders": {"display_name": "production_orders", "database": "aps", "primary_key": "id", "business_key": "product_id", "fields": ["id", "order_number", "product_id", "quantity", "priority", "status", "scheduled_start", "scheduled_end", "actual_start", "actual_end", "created_by", "created_at", "updated_at"], "datetime_fields": ["scheduled_start", "scheduled_end", "actual_start", "actual_end", "created_by", "created_at", "updated_at"], "field_count": 13, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.878566", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["product_id"]}, "production_schedules": {"display_name": "production_schedules", "database": "aps", "primary_key": "id", "business_key": "production_order_id", "fields": ["id", "production_order_id", "resource_id", "start_time", "end_time", "status", "created_at", "updated_at"], "datetime_fields": ["start_time", "end_time", "created_at", "updated_at"], "field_count": 8, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.894619", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["production_order_id"]}, "products": {"display_name": "products", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "code", "name", "description", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 6, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.897619", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "resource_usage_logs": {"display_name": "resource_usage_logs", "database": "aps", "primary_key": "id", "business_key": "resource_id", "fields": ["id", "resource_id", "order_id", "start_time", "end_time", "operator", "usage_type", "details", "created_at"], "datetime_fields": ["start_time", "end_time", "created_at"], "field_count": 9, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.901619", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["resource_id"]}, "resources": {"display_name": "resources", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "code", "name", "type", "status_field", "specifications", "location", "maintenance_cycle", "last_maintenance", "next_maintenance", "created_at", "updated_at"], "datetime_fields": ["last_maintenance", "next_maintenance", "created_at", "updated_at"], "field_count": 12, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.905619", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "schedule_history": {"display_name": "schedule_history", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "algorithm", "optimization_target", "total_batches", "scheduled_batches", "execution_time", "status", "schedule_data", "metrics_data", "timestamp", "created_by", "created_at"], "datetime_fields": ["execution_time", "timestamp", "created_by", "created_at"], "field_count": 12, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.909942", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "scheduler_config": {"display_name": "scheduler_config", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "key", "value", "description", "updated_at", "updated_by"], "datetime_fields": ["updated_at", "updated_by"], "field_count": 6, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.208859", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "scheduler_job_logs": {"display_name": "scheduler_job_logs", "database": "aps_system", "primary_key": "id", "business_key": "job_id", "fields": ["id", "job_id", "job_name", "start_time", "end_time", "status", "result", "error", "duration", "created_at"], "datetime_fields": ["start_time", "end_time", "created_at"], "field_count": 10, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.226762", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["job_id"]}, "scheduler_jobs": {"display_name": "scheduler_jobs", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "name", "job_type", "func", "args", "kwargs", "trigger", "trigger_args", "enabled", "created_at", "updated_at", "created_by", "description"], "datetime_fields": ["created_at", "updated_at", "created_by"], "field_count": 13, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.242108", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "sheet1": {"display_name": "sheet1", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "批次号", "LOT_ID", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 5, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.922941", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["LOT_ID"]}, "tcc_inv": {"display_name": "tcc_inv", "database": "aps", "primary_key": "id", "business_key": "excel_override", "fields": ["id", "UNNAMED__0", "硬件编码", "关键硬件", "图片", "寿命状态", "仓库", "初始库位", "当前储位1", "当前储位2", "责任人", "周期消耗数", "当前库位", "封装形式", "状态", "类别", "设备机型", "寄放方", "备注_状态_SHIPOUT信息_", "类型", "状态_1", "操作", "data_source", "source_priority", "last_sync_time", "sync_status", "mysql_hash", "excel_override"], "datetime_fields": ["last_sync_time"], "field_count": 28, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.930053", "hidden_fields": ["last_sync_time", "sync_status", "mysql_hash", "excel_override"], "readonly_fields": ["id"], "required_fields": ["excel_override"]}, "test_specs": {"display_name": "test_specs", "database": "aps", "primary_key": "id", "business_key": "id", "fields": ["id", "name", "version", "status", "description", "parameters", "owner", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 9, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.934060", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "unified_lot_management": {"display_name": "unified_lot_management", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "LOT_ID", "DEVICE", "CHIP_ID", "PKG_PN", "PROD_ID", "STAGE", "FLOW_ID", "FLOW_VER", "GOOD_QTY", "LOT_QTY", "LOT_IN_QTY", "LOT_OUT_QTY", "NG_QTY", "WIP_STATE", "PROC_STATE", "HOLD_STATE", "LOT_TYPE", "HANDLER_ID", "TESTER_ID", "EQP_ID", "PRIORITY_LEVEL", "PRIORITY_ORDER", "HOT_TYPE", "RELEASE_TIME", "PLAN_START_DATE", "PLAN_DUE_DATE", "JOB_START_TIME", "JOB_END_TIME", "PO_ID", "WORK_ORDER_ID", "WORK_ORDER_VER", "FAC_ID", "SUB_FAC", "AREA_ID", "ROOT_LOT_ID", "PARENT_LOT_ID", "CHILD_LOT_ID", "CUST_LOT_ID", "extended_data", "source_table", "migration_status", "data_version", "created_at", "updated_at", "created_by", "updated_by"], "datetime_fields": ["RELEASE_TIME", "PLAN_START_DATE", "PLAN_DUE_DATE", "JOB_START_TIME", "JOB_END_TIME", "created_at", "updated_at", "created_by", "updated_by"], "field_count": 47, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.939059", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["LOT_ID"]}, "unified_test_specs": {"display_name": "unified_test_specs", "database": "aps", "primary_key": "id", "business_key": "DEVICE", "fields": ["id", "TEST_SPEC_ID", "TEST_SPEC_NAME", "TEST_SPEC_VER", "DEVICE", "CHIP_ID", "PKG_PN", "PROD_ID", "STAGE", "TESTER", "HANDLER", "TEST_SPEC_TYPE", "TEST_AREA", "TEMPERATURE", "TB_PN", "HB_PN", "KIT_PN", "SOCKET_PN", "TESTER_CONFIG", "FT_PROGRAM", "QA_PROGRAM", "GU_PROGRAM", "FT_PROGRAM_PATH", "QA_PROGRAM_PATH", "GU_PROGRAM_PATH", "UPH", "TEST_TIME", "STANDARD_YIELD", "LOW_YIELD", "HIGH_YIELD", "DOWN_YIELD", "ORT_QTY", "REMAIN_QTY", "status", "APPROVAL_STATE", "ACTV_YN", "APPROVE_USER", "APPROVE_TIME", "TEST_ENG", "FAC_ID", "SUB_FAC", "COMPANY_ID", "test_parameters", "extended_data", "source_table", "migration_status", "data_version", "created_at", "updated_at", "created_by", "updated_by"], "datetime_fields": ["TEST_TIME", "APPROVE_TIME", "created_at", "updated_at", "created_by", "updated_by"], "field_count": 51, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.945576", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["DEVICE"]}, "user_action_logs": {"display_name": "user_action_logs", "database": "aps_system", "primary_key": "id", "business_key": "target_id", "fields": ["id", "username", "action_type", "target_model", "target_id", "details", "ip_address", "user_agent", "created_at"], "datetime_fields": ["created_at"], "field_count": 9, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.311112", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["target_id"]}, "users": {"display_name": "users", "database": "aps_system", "primary_key": "username", "business_key": "id", "fields": ["username", "email", "password_hash", "role", "created_at", "updated_at", "last_login", "is_active"], "datetime_fields": ["created_at", "updated_at", "last_login"], "field_count": 8, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.377375", "hidden_fields": ["password_hash"], "readonly_fields": [], "required_fields": []}, "wip_lot": {"display_name": "WIP批次管理", "database": "aps", "primary_key": "id", "business_key": "LOT_ID", "fields": ["id", "LOT_ID", "LOT_TYPE", "DET_LOT_TYPE", "LOT_QTY", "SUB_QTY", "UNIT", "SUB_UNIT", "WIP_STATE", "PROC_STATE", "HOLD_STATE", "RW_STATE", "REPAIR_STATE", "QC_STATE", "PROD_ID", "DEVICE", "CHIP_ID", "PKG_PN", "STAGE", "PROC_RULE_ID", "PRP_ID", "FLOW_ID", "PRP_VER", "FLOW_VER", "OPER_VER", "EQP_ID", "SUB_EQP_ID", "PORT_ID", "AREA_ID", "LOC_ID", "CARR_ID", "MAIN_EQP_ID", "AUXILIARY_EQP_ID", "RESV_EQP_ID", "LOT_IN_QTY", "LOT_OUT_QTY", "GOOD_QTY", "NG_QTY", "ACT_QTY", "ORT_QTY", "IQC_QTY", "UPH", "ORT_SAMP_QTY", "IQC_SAMP_QTY", "STRM_QTY", "STRM_SAMP_QTY", "ROOT_LOT_ID", "PARENT_LOT_ID", "CHILD_LOT_ID", "CUST_LOT_ID", "MERGE_LOT_ID", "WORK_ORDER_ID", "WORK_ORDER_VER", "PO_ID", "BOM_ID", "BOM_VER", "FAC_ID", "SUB_FAC", "LOT_OWNER", "OPER_CHANGE_TIME", "JOB_START_TIME", "JOB_END_TIME", "PLAN_START_DATE", "PLAN_DUE_DATE", "RELEASE_TIME", "SHIP_TIME", "CREATE_TIME", "HOT_TYPE", "PRIORITY_LEVEL", "PRIORITY_ORDER", "PILOT_TYPE", "RECIPE_ID", "SUB_RECIPE_ID", "TEST_SPEC_ID", "TEST_SPEC_NAME", "TEST_SPEC_VER", "RETEST_YN", "RETEST_FLOW_ID", "DUT_ID", "PACK_SPEC_ID", "PACK_SPEC_VER", "CONTAINER_ID", "WAREHOUSE_CONTAINER_ID", "TRACK_CARD_ID", "MARK_ID", "GRADE", "REASON_GRP", "REASON_CODE", "LOT_JUDGE", "FULL_INSP_QC", "USE_SUB_LOT", "STR_FLAG", "OA_FLAG", "SEAL_FLAG", "SPLIT_TYPE", "HALF_LOT_HOLD", "RELEASE_HOLD_TYPE", "DATA_CONFIRM_HOLD_YN", "EVENT", "EVENT_KEY", "EVENT_TIME", "EVENT_USER", "EVENT_MSG", "CREATE_USER", "created_at", "updated_at"], "datetime_fields": ["PLAN_START_DATE", "PLAN_DUE_DATE", "created_at", "updated_at"], "field_count": 90, "auto_discovered": true, "last_discovery": "2025-06-27T12:09:46.390492", "hidden_fields": [], "readonly_fields": ["id", "CREATE_TIME"], "required_fields": ["LOT_ID"], "description": "在制品批次管理表，包含完整业务字段，连接到aps主业务数据库"}, "wip_records": {"display_name": "wip_records", "database": "aps", "primary_key": "id", "business_key": "production_order_id", "fields": ["id", "production_order_id", "product_id", "quantity", "status", "location", "recorded_at"], "datetime_fields": ["recorded_at"], "field_count": 7, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.982729", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["production_order_id"]}, "ai_settings": {"display_name": "ai_settings", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "settings", "updated_at"], "datetime_fields": ["updated_at"], "field_count": 3, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:39.987729", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "database_configs": {"display_name": "database_configs", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "name", "db_type", "host", "port", "username", "password_encrypted", "database_name", "charset", "timezone", "ssl_enabled", "ssl_cert_path", "connection_pool_size", "connection_timeout", "extra_params", "is_default", "is_active", "description", "created_at", "updated_at", "last_test_at", "last_test_status", "last_test_message"], "datetime_fields": ["timezone", "connection_timeout", "created_at", "updated_at", "last_test_at"], "field_count": 23, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.021847", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "database_info": {"display_name": "database_info", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "db_name", "db_type", "connection_info", "description", "status", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 8, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.033255", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "database_mappings": {"display_name": "database_mappings", "database": "aps_system", "primary_key": "id", "business_key": "database_config_id", "fields": ["id", "mapping_type", "target_name", "database_config_id", "priority", "is_active", "description", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 9, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.061587", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["database_config_id"]}, "devicepriorityconfig": {"display_name": "devicepriorityconfig", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "device", "priority", "from_time", "end_time", "refresh_time", "user", "created_at", "updated_at"], "datetime_fields": ["from_time", "end_time", "refresh_time", "created_at", "updated_at"], "field_count": 9, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.086283", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "email_attachments": {"display_name": "email_attachments", "database": "aps_system", "primary_key": "id", "business_key": "email_config_id", "fields": ["id", "email_config_id", "message_id", "sender", "subject", "receive_date", "filename", "file_path", "file_size", "file_md5", "processed", "process_date", "process_result", "process_message", "created_at"], "datetime_fields": ["receive_date", "process_date", "created_at"], "field_count": 15, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.115785", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["email_config_id"]}, "email_configs": {"display_name": "email_configs", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "name", "server", "port", "email", "password", "senders", "subjects", "check_interval", "work_start_time", "work_end_time", "enabled", "download_path", "use_date_folder", "fetch_days", "description", "created_by", "created_at", "updated_at"], "datetime_fields": ["work_start_time", "work_end_time", "use_date_folder", "created_by", "created_at", "updated_at"], "field_count": 19, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.122787", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "lotpriorityconfig": {"display_name": "lotpriorityconfig", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "device", "stage", "priority", "refresh_time", "user", "created_at", "updated_at"], "datetime_fields": ["refresh_time", "created_at", "updated_at"], "field_count": 8, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.144479", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "menu_permissions": {"display_name": "menu_permissions", "database": "aps_system", "primary_key": "id", "business_key": "parent_id", "fields": ["id", "name", "route", "icon", "parent_id", "sort_order", "is_active", "created_at"], "datetime_fields": ["created_at"], "field_count": 8, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.161322", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["parent_id"]}, "migration_log": {"display_name": "migration_log", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "migration_name", "migration_type", "source_db", "target_db", "status", "start_time", "end_time", "records_processed", "error_message", "details", "created_by"], "datetime_fields": ["start_time", "end_time", "created_by"], "field_count": 12, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.176949", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "scheduling_tasks": {"display_name": "scheduling_tasks", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "task_name", "task_type", "schedule_config", "enabled", "last_run", "next_run", "created_by", "created_at", "updated_at"], "datetime_fields": ["last_run", "next_run", "created_by", "created_at", "updated_at"], "field_count": 10, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.257408", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "settings": {"display_name": "settings", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "key", "value", "description", "data_type", "category", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 8, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.261410", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "system_settings": {"display_name": "system_settings", "database": "aps_system", "primary_key": "id", "business_key": "user_id", "fields": ["id", "key", "value", "description", "user_id", "setting_type", "created_at", "updated_at", "CREATE_TIME"], "datetime_fields": ["created_at", "updated_at", "CREATE_TIME"], "field_count": 9, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.289169", "hidden_fields": [], "readonly_fields": ["id", "CREATE_TIME"], "required_fields": ["user_id"]}, "user_filter_presets": {"display_name": "user_filter_presets", "database": "aps_system", "primary_key": "id", "business_key": "id", "fields": ["id", "username", "preset_name", "page_type", "filters", "created_at", "updated_at"], "datetime_fields": ["created_at", "updated_at"], "field_count": 7, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.326836", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": []}, "user_permissions": {"display_name": "user_permissions", "database": "aps_system", "primary_key": "id", "business_key": "menu_id", "fields": ["id", "username", "menu_id", "granted_by", "created_at"], "datetime_fields": ["created_at"], "field_count": 5, "auto_discovered": true, "last_discovery": "2025-06-25T21:46:40.349972", "hidden_fields": [], "readonly_fields": ["id"], "required_fields": ["menu_id"]}}, "field_types": {"datetime_patterns": ["time", "date", "created", "updated"], "id_patterns": ["id", "key"], "numeric_patterns": ["qty", "count", "priority", "uph"]}, "display_rules": {"hidden_fields": ["created_at", "updated_at", "mysql_hash", "excel_override"], "readonly_fields": ["id", "create_time", "CREATE_TIME"], "required_fields": []}}